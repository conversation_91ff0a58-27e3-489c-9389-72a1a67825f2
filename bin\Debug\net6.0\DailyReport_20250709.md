# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Waiting on Customer (BLOCKED)

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| 🚨 | Boeing Mesa - General Tickets  | [BN-126](https://universalsoftware.atlassian.net/browse/BN-126) | Boeing Mesa - New Report - On Hand Inventory w/ ReceiptID | 2025-04-02 | Waiting on Customer (BLOCKED) | 98 days overdue | 14 days | <PERSON> |
| 🚨 | Boeing Mesa - General Tickets  | [BN-91](https://universalsoftware.atlassian.net/browse/BN-91) | Boeing Mesa - Pick Confirm - RQST_WORK_CENTER Included in Pick Confirm File (Unplanned Pick Request) | 2025-06-27 | Waiting on Customer (BLOCKED) | 12 days overdue | 14 days | <PERSON> |
| ⚠️ | PortPro Integrations  | [POR-23](https://universalsoftware.atlassian.net/browse/POR-23) | Great Plains Integrations | 2025-08-11 | Waiting on Customer (BLOCKED) | 33 days remaining | 9 days | Emily Gamble |
| ⚠️ | PortPro Integrations  | [POR-22](https://universalsoftware.atlassian.net/browse/POR-22) | Highway - Carriers Integrations | 2025-08-11 | Waiting on Customer (BLOCKED) | 33 days remaining | 9 days | Emily Gamble |
| ⚠️ | PortPro Integrations  | [POR-21](https://universalsoftware.atlassian.net/browse/POR-21) | Transman (Equipment) Integrations | 2025-08-11 | Waiting on Customer (BLOCKED) | 33 days remaining | 9 days | Emily Gamble |
| ⚠️ | PortPro Integrations  | [POR-20](https://universalsoftware.atlassian.net/browse/POR-20) | EFS - Fuel Integrations | 2025-08-11 | Waiting on Customer (BLOCKED) | 33 days remaining | 9 days | Emily Gamble |
| 🚨 | PortPro Integrations  | [POR-12](https://universalsoftware.atlassian.net/browse/POR-12) | Deploying and Testing in staging POR-9 and POR-10 | Not set | Waiting on Customer (BLOCKED) | No due date | 14 days | Jon Taylor |
| 🚨 | Carta Porte | [CP-15](https://universalsoftware.atlassian.net/browse/CP-15) | Process/retain CFDI + Carta Porte doc from Digital Invoice xml response | Not set | Waiting on Customer (BLOCKED) | No due date | 41 days | Unassigned |

## Late Items

| Status | Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|--------|---------|---------|------|----------|--------------|----------|
| 🚨 | WES | [WES-103](https://universalsoftware.atlassian.net/browse/WES-103) | Migrating the container search report (generate report button) from YMS to WES - frontend | 2025-05-30 | 40 days overdue | Unassigned |
| 🚨 | WES | [WES-20](https://universalsoftware.atlassian.net/browse/WES-20) | Migrating the container search report from YMS to WES - frontend | 2025-05-30 | 40 days overdue | Unassigned |
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | 29 days overdue | Alex Gonzalez |
| 🚨 | Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | 28 days overdue | Rakhi Garg |
| ⚠️ | WES | [WES-139](https://universalsoftware.atlassian.net/browse/WES-139) | Migrating Container Page (action menu - empty type options) - At Docks - from YMS to WES | 2025-06-30 | 9 days overdue | Unassigned |
| ⚠️ | DTNA RFID | [DR-3](https://universalsoftware.atlassian.net/browse/DR-3) | Clone Boeing instance for RFID testing  | 2025-06-30 | 9 days overdue | Unassigned |
| ⚠️ | BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | 9 days overdue | Antonio Silva |
| 🟡 | BMW Spartanburg | [BMWS-430](https://universalsoftware.atlassian.net/browse/BMWS-430) | Update Error Message on Failed Label Prints in DCO Service | 2025-07-06 | 3 days overdue | Isaiah Childs |
| 🟡 | Fleet Tracker | [FLEET-101](https://universalsoftware.atlassian.net/browse/FLEET-101) | Asset detail - Side-panel Features (Change Contractor) | 2025-07-07 | 2 days overdue | R Sanders |
| 🟡 | Fleet Tracker | [FLEET-100](https://universalsoftware.atlassian.net/browse/FLEET-100) | Asset detail - Side-panel Features (Reinstate) | 2025-07-07 | 2 days overdue | R Sanders |
| 🟡 | BMW Spartanburg | [BMWS-440](https://universalsoftware.atlassian.net/browse/BMWS-440) | Update Stored Procedures Supporting the 'ASN Generation' Process | 2025-07-07 | 2 days overdue | Antonio Silva |
| 🟡 | BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-07-08 | 1 days overdue | Justin Kerketta |

## Coming Soon (Next 7 Days)

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|--------|---------|---------|------|----------|----------------|----------|
| 🚨 | FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-09 | Due today | Adam Caldwell |
| 🚨 | Boeing - XDock - General Tickets  | [BOEIN-39](https://universalsoftware.atlassian.net/browse/BOEIN-39) | EDI File Upload | 2025-07-09 | Due today | Chris Collins |
| 🚨 | Boeing Mesa - General Tickets  | [BN-135](https://universalsoftware.atlassian.net/browse/BN-135) | Add Option (Paper Work Hold) to Select Grief Status (Mesa Boeing) | 2025-07-09 | Due today | Chris Collins |
| 🚨 | Value Added  | [VAL-447](https://universalsoftware.atlassian.net/browse/VAL-447) | Update post process procedure to make it more dynamic | 2025-07-10 | 1 days remaining | Igor Ivanovski |
| ⚠️ | YMS Project | [YP-124](https://universalsoftware.atlassian.net/browse/YP-124) | Deployment #1 for Phase 2 | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | YMS Project | [YP-122](https://universalsoftware.atlassian.net/browse/YP-122) | Carrier Master update needed for this epic - edit - adding SCAC | 2025-07-11 | 2 days remaining | Ludmil Gueorguiev |
| ⚠️ | YMS Project | [YP-120](https://universalsoftware.atlassian.net/browse/YP-120) | Carrier Master update needed for this epic - adding SCAC into carrier creation | 2025-07-11 | 2 days remaining | Ludmil Gueorguiev |
| ⚠️ | YMS Project | [YP-50](https://universalsoftware.atlassian.net/browse/YP-50) | Outbound Log Requesed Changes | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | YMS Project | [YP-38](https://universalsoftware.atlassian.net/browse/YP-38) | Trailer Check In Process | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | YMS Project | [YP-37](https://universalsoftware.atlassian.net/browse/YP-37) | In Yard Status Changes | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | YMS Project | [YP-36](https://universalsoftware.atlassian.net/browse/YP-36) | Verbiage Changes | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | YMS Project | [YP-21](https://universalsoftware.atlassian.net/browse/YP-21) | Sorting By Categories | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | Highway | [HIG-27](https://universalsoftware.atlassian.net/browse/HIG-27) | Needs to integrate with GP - ULCarrierImporter service and ULCarrierSyncService - Highway | 2025-07-11 | 2 days remaining | Unassigned |
| ⚠️ | Highway | [HIG-26](https://universalsoftware.atlassian.net/browse/HIG-26) | update to ULAchEmailService | 2025-07-11 | 2 days remaining | Trent Meyering |
| ⚠️ | Highway | [HIG-10](https://universalsoftware.atlassian.net/browse/HIG-10) | Implement Carrier Sync | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | Highway | [HIG-7](https://universalsoftware.atlassian.net/browse/HIG-7) | Implement Carrier Full Update Endpoint | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | Highway | [HIG-4](https://universalsoftware.atlassian.net/browse/HIG-4) | Implement Carrier Alerts Endpoint | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | Highway | [HIG-1](https://universalsoftware.atlassian.net/browse/HIG-1) | Implement Carrier Onboarding Endpoint | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | GM ODC Launch  | [GMDCL-83](https://universalsoftware.atlassian.net/browse/GMDCL-83) | MGO Status Updater - Update All Interface Msg Records For On-Hand | 2025-07-11 | 2 days remaining | Adam Caldwell |
| ⚠️ | GM ODC Launch  | [GMDCL-82](https://universalsoftware.atlassian.net/browse/GMDCL-82) | Routing Processor - Prevent Web Service Errors | 2025-07-11 | 2 days remaining | Adam Caldwell |
| ⚠️ | Fleet Tracker | [FLEET-103](https://universalsoftware.atlassian.net/browse/FLEET-103) | Asset detail - Secured Parking tab (delete) | 2025-07-11 | 2 days remaining | R Sanders |
| ⚠️ | Fleet Tracker | [FLEET-43](https://universalsoftware.atlassian.net/browse/FLEET-43) | Asset detail - Secured Parking tab (add/edit) | 2025-07-11 | 2 days remaining | R Sanders |
| ⚠️ | Fleet Tracker | [FLEET-42](https://universalsoftware.atlassian.net/browse/FLEET-42) | Asset detail - Plate tab  | 2025-07-11 | 2 days remaining | R Sanders |
| ⚠️ | Fleet Tracker | [FLEET-41](https://universalsoftware.atlassian.net/browse/FLEET-41) | Asset detail - Equipment tab | 2025-07-11 | 2 days remaining | R Sanders |
| ⚠️ | Fleet Tracker | [FLEET-40](https://universalsoftware.atlassian.net/browse/FLEET-40) | Asset detail - Side-panel Display | 2025-07-11 | 2 days remaining | R Sanders |
| ⚠️ | Fleet Tracker | [FLEET-16](https://universalsoftware.atlassian.net/browse/FLEET-16) | Personnel - Personnel tab | 2025-07-11 | 2 days remaining | Jeff Priskorn |
| ⚠️ | Control Tower | [CTP-128](https://universalsoftware.atlassian.net/browse/CTP-128) | R&D Control Tower Notes | 2025-07-11 | 2 days remaining | Ben Blazy |
| ⚠️ | BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-11 | 2 days remaining | Justin Kerketta |
| ⚠️ | AppDev | [AP-57](https://universalsoftware.atlassian.net/browse/AP-57) | Atlas - potential orders entry change | 2025-07-11 | 2 days remaining | Aaron Anderson |
| ⚠️ | AppDev | [AP-53](https://universalsoftware.atlassian.net/browse/AP-53) | Atlas EDI 214 Issues - 324048 | 2025-07-11 | 2 days remaining | Aaron Anderson |
| ⚠️ | AppDev | [AP-51](https://universalsoftware.atlassian.net/browse/AP-51) | Lane exception Pay rules | 2025-07-11 | 2 days remaining | Aaron Anderson |
| ⚠️ | AppDev | [AP-50](https://universalsoftware.atlassian.net/browse/AP-50) | New settlement line item - fuel (lane exception pay) | 2025-07-11 | 2 days remaining | Aaron Anderson |
| ⚠️ | AppDev | [AP-49](https://universalsoftware.atlassian.net/browse/AP-49) | Driver system limit update on payout | 2025-07-11 | 2 days remaining | Aaron Anderson |
| ⚠️ | AppDev | [AP-48](https://universalsoftware.atlassian.net/browse/AP-48) | Project Lockdown - Atlas Settlements | 2025-07-11 | 2 days remaining | Emily Gamble |
| ⚠️ | AppDev | [AP-41](https://universalsoftware.atlassian.net/browse/AP-41) | Data Update from Atlas to AtlasCommunications failure BUG | 2025-07-11 | 2 days remaining | Aaron Anderson |
| ✅ | Highway | [HIG-31](https://universalsoftware.atlassian.net/browse/HIG-31) | Deploying Highway | 2025-07-14 | 5 days remaining | Unassigned |
| ✅ | Highway | [HIG-25](https://universalsoftware.atlassian.net/browse/HIG-25) | Carriers use EIN or SSN when setting up their company | 2025-07-14 | 5 days remaining | Trent Meyering |
| ✅ | Westport - Seats - General Tickets  | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | 2025-07-15 | 6 days remaining | Igor Ivanovski |
| ✅ | Boeing Mesa - General Tickets  | [BN-137](https://universalsoftware.atlassian.net/browse/BN-137) | Blind Audit Scanner Feature (Mesa Boeing) | 2025-07-16 | 7 days remaining | Chris Collins |
| ✅ | Boeing Mesa - General Tickets  | [BN-38](https://universalsoftware.atlassian.net/browse/BN-38) | Kitted Picked Orders Blind Audit Boeing Mesa UAT | 2025-07-16 | 7 days remaining | Chris Collins |

## Blocked Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| 🔴 | Fleet Tracker | [FLEET-68](https://universalsoftware.atlassian.net/browse/FLEET-68) | Contractor details - Insurance  | 2025-09-30 | Blocked/Deferred | 83 days remaining | 2 days | Unassigned |
| 🚨 | BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 176 days remaining | 28 days | Shayne Vallad |
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-30](https://universalsoftware.atlassian.net/browse/WSPPGT-30) | Painted Parts accepting new Chassis prefix | Not set | Blocked/Deferred | No due date | 33 days | Igor Ivanovski |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-79](https://universalsoftware.atlassian.net/browse/TAMA-79) | File Generator - Archive File Name | Not set | Blocked/Deferred | No due date | 28 days | Unassigned |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-78](https://universalsoftware.atlassian.net/browse/TAMA-78) | update LastLoginDate column in Users table | Not set | Blocked/Deferred | No due date | 28 days | Unassigned |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-72](https://universalsoftware.atlassian.net/browse/TAMA-72) | Create Web Portal Documentation To Share With Customers | Not set | Blocked/Deferred | No due date | 28 days | Unassigned |
| 🚨 | Sequencing | [SEQ-13](https://universalsoftware.atlassian.net/browse/SEQ-13) | Research ABP.IO | Not set | Blocked/Deferred | No due date | 20 days | Unassigned |
| 🚨 | PortPro Integrations  | [POR-10](https://universalsoftware.atlassian.net/browse/POR-10) | Process PTO Source Data and Update Driver 'accountHold' Status | Not set | Blocked/Deferred | No due date | 23 days | Unassigned |
| 🚨 | PortPro Integrations  | [POR-9](https://universalsoftware.atlassian.net/browse/POR-9) | Demographic Item | Not set | Blocked/Deferred | No due date | 23 days | Unassigned |
| 🚨 | GM - Flint Continental - General Tickets  | [GFCGT-67](https://universalsoftware.atlassian.net/browse/GFCGT-67) | No Pick Function request | Not set | Blocked/Deferred | No due date | 89 days | Unassigned |
| 🚨 | Flint - Torrey - General Tickets  | [FLIN-4](https://universalsoftware.atlassian.net/browse/FLIN-4) | Enable Image Capture Functionality for Scanner Devices | Not set | Blocked/Deferred | No due date | 69 days | Unassigned |
| ⚠️ | BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | Not set | Blocked/Deferred | No due date | 7 days | Cameron Rye |
| 🔴 | AppDev | [AP-69](https://universalsoftware.atlassian.net/browse/AP-69) | MXExcelToEDI change | Not set | Blocked/Deferred | No due date | 1 days | Duncan Miller |

## Project on Hold (BLOCKED)

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| 🔴 | YMS Project | [YP-109](https://universalsoftware.atlassian.net/browse/YP-109) | Yard Check Module - Status | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-108](https://universalsoftware.atlassian.net/browse/YP-108) | Yard Check Module - Check In New Trailer | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-107](https://universalsoftware.atlassian.net/browse/YP-107) | Yard Check Module - Popup to Check Out Trailer | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-103](https://universalsoftware.atlassian.net/browse/YP-103) | Yard Check Module - Verified Popup | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-102](https://universalsoftware.atlassian.net/browse/YP-102) | Yard Check Module - Edit Menu | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-101](https://universalsoftware.atlassian.net/browse/YP-101) | Yard Check Module - All Search Functionality | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-100](https://universalsoftware.atlassian.net/browse/YP-100) | Yard Check Module - Ability to Sort/Pagination | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-99](https://universalsoftware.atlassian.net/browse/YP-99) | Yard Check Module - Main Page | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-98](https://universalsoftware.atlassian.net/browse/YP-98) | Move Queue Module for Switchers - Popup for when completing a move when no one was assigned | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-95](https://universalsoftware.atlassian.net/browse/YP-95) | Inbound Log Requested Change | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-91](https://universalsoftware.atlassian.net/browse/YP-91) | Move Queue Module for Switchers - Search | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🔴 | YMS Project | [YP-90](https://universalsoftware.atlassian.net/browse/YP-90) | Move Queue Module for Switchers - Sorting | Not set | Project on Hold (BLOCKED) | No due date | 0 days | Unassigned |
| 🚨 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | Project on Hold (BLOCKED) | No due date | 42 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-20](https://universalsoftware.atlassian.net/browse/CMS-20) | Verify Manifest Page | Not set | Project on Hold (BLOCKED) | No due date | 9 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-19](https://universalsoftware.atlassian.net/browse/CMS-19) | Import Pkg Sched | Not set | Project on Hold (BLOCKED) | No due date | 9 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-18](https://universalsoftware.atlassian.net/browse/CMS-18) | Print Route Manifest | Not set | Project on Hold (BLOCKED) | No due date | 9 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-17](https://universalsoftware.atlassian.net/browse/CMS-17) | ASN Lookup Page | Not set | Project on Hold (BLOCKED) | No due date | 9 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-3](https://universalsoftware.atlassian.net/browse/CMS-3) | CMS UI Update - Add Loading Panels | Not set | Project on Hold (BLOCKED) | No due date | 9 days | Adam Caldwell |

## Overall Summary

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|--------|---------|---------|------|----------|----------------|------------|--------|
| ⚠️ | YMS Project | [YP-50](https://universalsoftware.atlassian.net/browse/YP-50) | Outbound Log Requesed Changes | 2025-07-11 | 2 days remaining | 0.0% | In Progress |
| ⚠️ | YMS Project | [YP-38](https://universalsoftware.atlassian.net/browse/YP-38) | Trailer Check In Process | 2025-07-11 | 2 days remaining | 0.0% | In Progress |
| ⚠️ | YMS Project | [YP-37](https://universalsoftware.atlassian.net/browse/YP-37) | In Yard Status Changes | 2025-07-11 | 2 days remaining | 0.0% | In Progress |
| ⚠️ | YMS Project | [YP-36](https://universalsoftware.atlassian.net/browse/YP-36) | Verbiage Changes | 2025-07-11 | 2 days remaining | 33.3% | In Progress |
| ⚠️ | YMS Project | [YP-21](https://universalsoftware.atlassian.net/browse/YP-21) | Sorting By Categories | 2025-07-11 | 2 days remaining | 42.1% | In Progress |
| ⚠️ | Highway | [HIG-10](https://universalsoftware.atlassian.net/browse/HIG-10) | Implement Carrier Sync | 2025-07-11 | 2 days remaining | 50.0% | In Progress |
| ⚠️ | Highway | [HIG-7](https://universalsoftware.atlassian.net/browse/HIG-7) | Implement Carrier Full Update Endpoint | 2025-07-11 | 2 days remaining | 66.7% | In Progress |
| ⚠️ | Highway | [HIG-4](https://universalsoftware.atlassian.net/browse/HIG-4) | Implement Carrier Alerts Endpoint | 2025-07-11 | 2 days remaining | 50.0% | In Progress |
| ⚠️ | Highway | [HIG-1](https://universalsoftware.atlassian.net/browse/HIG-1) | Implement Carrier Onboarding Endpoint | 2025-07-11 | 2 days remaining | 40.0% | In Progress |
| ⚠️ | AppDev | [AP-48](https://universalsoftware.atlassian.net/browse/AP-48) | Project Lockdown - Atlas Settlements | 2025-07-11 | 2 days remaining | 0.0% | In Progress |
| 🟡 | Fleet Tracker | [FLEET-7](https://universalsoftware.atlassian.net/browse/FLEET-7) | Fleet Tracker - Personnel | 2025-07-18 | 9 days remaining | 84.6% | In Progress |
| 📋 | AppDev | [AP-52](https://universalsoftware.atlassian.net/browse/AP-52) | Atlas EDI bugs | 2025-07-18 | 9 days remaining | 37.5% | In Progress |
| 📋 | Boeing Mesa - General Tickets  | [BN-136](https://universalsoftware.atlassian.net/browse/BN-136) | Boeing Mesa - BOM Count Feature (Scanner/Website) | 2025-07-23 | 14 days remaining | 0.0% | Refining |
| 📋 | Highway | [HIG-28](https://universalsoftware.atlassian.net/browse/HIG-28) | Items still needed | 2025-07-24 | 15 days remaining | 33.3% | In Progress |
| 📋 | YMS Project | [YP-46](https://universalsoftware.atlassian.net/browse/YP-46) | Yard Check Module | 2025-07-25 | 16 days remaining | 0.0% | Backlog |
| 📋 | YMS Project | [YP-44](https://universalsoftware.atlassian.net/browse/YP-44) | Move Queue Module for Switchers | 2025-07-28 | 19 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-6](https://universalsoftware.atlassian.net/browse/FLEET-6) | Fleet Tracker  - Assets | 2025-07-31 | 22 days remaining | 33.3% | In Progress |
| 📋 | Control Tower | [CTP-129](https://universalsoftware.atlassian.net/browse/CTP-129) | Control Tower UI/UX enhancements and bug fixes | 2025-07-31 | 22 days remaining | 50.0% | Ready |
| 📋 | Carta Porte | [CP-8](https://universalsoftware.atlassian.net/browse/CP-8) | CFDI + Carta Porte UI (front-end) | 2025-07-31 | 22 days remaining | 71.4% | In Progress |
| 📋 | Carta Porte | [CP-1](https://universalsoftware.atlassian.net/browse/CP-1) | CFDI/Carta Porte Data Relay (back-end) | 2025-07-31 | 22 days remaining | 28.6% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-31 | 22 days remaining | 15.4% | In Progress |
| 📋 | PortPro Integrations  | [POR-23](https://universalsoftware.atlassian.net/browse/POR-23) | Great Plains Integrations | 2025-08-11 | 33 days remaining | N/A | Waiting on Customer (BLOCKED) |
| 📋 | PortPro Integrations  | [POR-22](https://universalsoftware.atlassian.net/browse/POR-22) | Highway - Carriers Integrations | 2025-08-11 | 33 days remaining | N/A | Waiting on Customer (BLOCKED) |
| 📋 | PortPro Integrations  | [POR-21](https://universalsoftware.atlassian.net/browse/POR-21) | Transman (Equipment) Integrations | 2025-08-11 | 33 days remaining | N/A | Waiting on Customer (BLOCKED) |
| 📋 | PortPro Integrations  | [POR-20](https://universalsoftware.atlassian.net/browse/POR-20) | EFS - Fuel Integrations | 2025-08-11 | 33 days remaining | N/A | Waiting on Customer (BLOCKED) |
| 📋 | PortPro Integrations  | [POR-3](https://universalsoftware.atlassian.net/browse/POR-3) | Workday Integration | 2025-08-11 | 33 days remaining | 55.0% | In Progress |
| 📋 | Fleet Tracker | [FLEET-57](https://universalsoftware.atlassian.net/browse/FLEET-57) | New Asset creation | 2025-08-15 | 37 days remaining | 0.0% | Refining |
| 📋 | YMS Project | [YP-61](https://universalsoftware.atlassian.net/browse/YP-61) | Switch from AWS to local for pictures and documents | 2025-08-29 | 51 days remaining | 0.0% | Backlog |
| 📋 | WES | [WES-143](https://universalsoftware.atlassian.net/browse/WES-143) | Setting up deployment of WES | 2025-08-29 | 51 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-8](https://universalsoftware.atlassian.net/browse/FLEET-8) | Fleet Tracker - Contractors | 2025-08-29 | 51 days remaining | 0.0% | Refining |
| 📋 | DTNA RFID | [DR-1](https://universalsoftware.atlassian.net/browse/DR-1) | Support DTNA RFID POC | 2025-08-29 | 51 days remaining | 50.0% | Backlog |
| 📋 | Control Tower | [CTP-103](https://universalsoftware.atlassian.net/browse/CTP-103) | Adding users to various locations | 2025-08-29 | 51 days remaining | 0.0% | Refining |
| 📋 | Control Tower | [CTP-71](https://universalsoftware.atlassian.net/browse/CTP-71) | Control Tower Notes | 2025-08-29 | 51 days remaining | 0.0% | In Progress |
| 📋 | Control Tower | [CTP-37](https://universalsoftware.atlassian.net/browse/CTP-37) | Search Functionality for Orders | 2025-08-29 | 51 days remaining | N/A | Ready |
| ✅ | BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 53 days remaining | 100.0% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 53 days remaining | 27.8% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 53 days remaining | 61.1% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 53 days remaining | 6.2% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 53 days remaining | 46.7% | In Progress |
| 📋 | WES | [WES-7](https://universalsoftware.atlassian.net/browse/WES-7) | Migrating Configuration Bucket from YMS to WES | 2025-09-26 | 79 days remaining | 10.9% | Backlog |
| 📋 | YMS Project | [YP-106](https://universalsoftware.atlassian.net/browse/YP-106) | last movement changed to check in date and time | 2025-09-30 | 83 days remaining | N/A | Backlog |
| 📋 | YMS Project | [YP-94](https://universalsoftware.atlassian.net/browse/YP-94) | Inbound Log Requested Change | 2025-09-30 | 83 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-32](https://universalsoftware.atlassian.net/browse/FLEET-32) | Fleet Tracker - Facility Locations | 2025-09-30 | 83 days remaining | 33.3% | In Progress |
| 📋 | Fleet Tracker | [FLEET-15](https://universalsoftware.atlassian.net/browse/FLEET-15) | Fleet Tracker - Agents | 2025-09-30 | 83 days remaining | N/A | Refining |
| 📋 | Control Tower | [CTP-70](https://universalsoftware.atlassian.net/browse/CTP-70) | Add Contact Directory | 2025-09-30 | 83 days remaining | N/A | Refining |
| 📋 | Control Tower | [CTP-69](https://universalsoftware.atlassian.net/browse/CTP-69) | Check-call fields and late updates | 2025-09-30 | 83 days remaining | N/A | Refining |
| 📋 | Control Tower | [CTP-26](https://universalsoftware.atlassian.net/browse/CTP-26) | Customer Login Authentication | 2025-09-30 | 83 days remaining | 0.0% | Ready |
| 📋 | BMW Spartanburg | [BMWS-379](https://universalsoftware.atlassian.net/browse/BMWS-379) | Controls API | 2025-09-30 | 83 days remaining | 0.0% | Refining |
| 📋 | Fleet Tracker | [FLEET-12](https://universalsoftware.atlassian.net/browse/FLEET-12) | Fleet Tracker - Agreements | 2025-10-31 | 114 days remaining | 0.0% | Refining |
| 📋 | WES | [WES-10](https://universalsoftware.atlassian.net/browse/WES-10) | Migrating Docks from YMS to WES | 2025-11-28 | 142 days remaining | 0.0% | Backlog |
| 📋 | YMS Project | [YP-104](https://universalsoftware.atlassian.net/browse/YP-104) | Placeholder Epic for RFID YOTTA hardware | 2025-12-31 | 175 days remaining | N/A | Backlog |
| 📋 | Fleet Tracker | [FLEET-13](https://universalsoftware.atlassian.net/browse/FLEET-13) | Fleet Tracker - Asset Insurance Reports and Documents | 2025-12-31 | 175 days remaining | 0.0% | Refining |
| 📋 | WES | [WES-13](https://universalsoftware.atlassian.net/browse/WES-13) | Migrating Container from YMS to WES | 2026-01-30 | 205 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-114](https://universalsoftware.atlassian.net/browse/FLEET-114) | Fleet  Tracker - Cancellations: Power Units | 2026-02-27 | 233 days remaining | 0.0% | Ready |
| 📋 | Fleet Tracker | [FLEET-9](https://universalsoftware.atlassian.net/browse/FLEET-9) | Fleet  Tracker - Cancellations: Drivers | 2026-02-27 | 233 days remaining | 0.0% | Ready |
| 📋 | WES | [WES-19](https://universalsoftware.atlassian.net/browse/WES-19) | Migrating all reports from YMS to WES | 2026-03-27 | 261 days remaining | 2.0% | Backlog |
| 📋 | WES | [WES-16](https://universalsoftware.atlassian.net/browse/WES-16) | Migrating Logs from YMS to WES | 2026-03-27 | 261 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-10](https://universalsoftware.atlassian.net/browse/FLEET-10) | Fleet Tracker - Closeouts | 2026-03-31 | 265 days remaining | 0.0% | Refining |
| 📋 | Fleet Tracker | [FLEET-14](https://universalsoftware.atlassian.net/browse/FLEET-14) | Fleet Tracker - Litigation (and Claims) | 2026-04-06 | 271 days remaining | 0.0% | Refining |
| 📋 | WES | [WES-22](https://universalsoftware.atlassian.net/browse/WES-22) | Migrating the outbound status section from YMS to WES | 2026-04-17 | 282 days remaining | 0.0% | Backlog |
| 📋 | WES | [WES-170](https://universalsoftware.atlassian.net/browse/WES-170) | Sorting By Categories | 2026-05-22 | 317 days remaining | 0.0% | Backlog |
| 📋 | WES | [WES-270](https://universalsoftware.atlassian.net/browse/WES-270) | Outbound Log Requesed Changes | 2026-05-29 | 324 days remaining | 0.0% | Backlog |
| 📋 | YMS Project | [YP-125](https://universalsoftware.atlassian.net/browse/YP-125) | Adding Zones to system as another master | Not set | No due date | 0.0% | In Progress |
| 📋 | YMS Project | [YP-105](https://universalsoftware.atlassian.net/browse/YP-105) | Aging Report - advanced filter | Not set | No due date | 0.0% | Backlog |
| 📋 | WMS 3.0 Research  | [YJ3-4](https://universalsoftware.atlassian.net/browse/YJ3-4) | Wants | Not set | No due date | 50.0% | Ready |
| 📋 | WMS 3.0 Research  | [YJ3-3](https://universalsoftware.atlassian.net/browse/YJ3-3) | WMS 3.0 - Lear Ford Steering - API Layer Calls | Not set | No due date | 25.0% | Ready |
| 🟡 | WMS 3.0 Research  | [YJ3-1](https://universalsoftware.atlassian.net/browse/YJ3-1) | WMS 3.0 - Lear Ford Steering Conversion | Not set | No due date | 90.0% | Ready |
| 📋 | Westport - WheelTire - General Tickets  | [WSWTGT-47](https://universalsoftware.atlassian.net/browse/WSWTGT-47) | Support TPMS Sensor | Not set | No due date | 0.0% | In Progress |
| 📋 | Westport - Painted Parts - General Tickets   | [WSPPGT-32](https://universalsoftware.atlassian.net/browse/WSPPGT-32) | Implement New Requested File Format | Not set | No due date | 60.0% | Refining |
| 📋 | Westport Axle Rewrite  | [WSAR-1](https://universalsoftware.atlassian.net/browse/WSAR-1) | Westport - Axle - WMS Migration | Not set | No due date | 42.9% | Refining |
| 📋 | WES | [WES-385](https://universalsoftware.atlassian.net/browse/WES-385) | Trailer Check In Process | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-384](https://universalsoftware.atlassian.net/browse/WES-384) | In Yard Status Changes | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-383](https://universalsoftware.atlassian.net/browse/WES-383) | Verbiage Changes | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-382](https://universalsoftware.atlassian.net/browse/WES-382) | tickets for research - different items | Not set | No due date | 70.0% | Backlog |
| 📋 | WES | [WES-347](https://universalsoftware.atlassian.net/browse/WES-347) | Refinement Tickets | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-346](https://universalsoftware.atlassian.net/browse/WES-346) | Rack Sheet Epic | Not set | No due date | 0.0% | Backlog |
| 📋 | WMS Android Future Features | [WAFF-6](https://universalsoftware.atlassian.net/browse/WAFF-6) | Android - Icon Fixes | Not set | No due date | N/A | Refining |
| 📋 | WMS Android Future Features | [WAFF-4](https://universalsoftware.atlassian.net/browse/WAFF-4) | Preference Management - Local Endpoint Updates | Not set | No due date | N/A | Backlog |
| 📋 | Value Added  | [VAL-445](https://universalsoftware.atlassian.net/browse/VAL-445) | Change the BOL for Seats, Painted Parts, and Wheel Tire | Not set | No due date | 0.0% | In Progress |
| 📋 | Value Added  | [VAL-29](https://universalsoftware.atlassian.net/browse/VAL-29) | Redgate Source Control - Compile Site Schemas into Version Control | Not set | No due date | 0.0% | Backlog |
| 📋 | Value Added  | [VAL-9](https://universalsoftware.atlassian.net/browse/VAL-9) | General support between security and Value-Added WMS development team to patch vulnerabilities in our applications | Not set | No due date | 28.6% | Ready |
| 📋 | Value Added  | [VAL-5](https://universalsoftware.atlassian.net/browse/VAL-5) | Planning on migrating features into the master branch for global application support | Not set | No due date | 66.7% | Ready |
| 📋 | Server Migration | [SM-5](https://universalsoftware.atlassian.net/browse/SM-5) | tatlas-services | Not set | No due date | 0.0% | To Do |
| 📋 | Server Migration | [SM-4](https://universalsoftware.atlassian.net/browse/SM-4) | tatlas-w-host01 | Not set | No due date | 0.0% | To Do |
| 📋 | Sequencing | [SEQ-3](https://universalsoftware.atlassian.net/browse/SEQ-3) | Implement Sequencing Domains | Not set | No due date | 0.0% | Refining |
| 📋 | SubZero  | [SBZ-114](https://universalsoftware.atlassian.net/browse/SBZ-114) | Create Dashboards for warehouse managers | Not set | No due date | 0.0% | Refining |
| 🟡 | GM - Factory Zero - General Tickets  | [GMFZGT-8](https://universalsoftware.atlassian.net/browse/GMFZGT-8) | Efforts for cleaning up SQL and dead code found in the app | Not set | No due date | 75.0% | In Progress |
| 📋 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | No due date | N/A | Project on Hold (BLOCKED) |
| 📋 | FoxproRewrite | [FOX-51](https://universalsoftware.atlassian.net/browse/FOX-51) | Redesign the "Driver" Tab in FoxPro | Not set | No due date | 0.0% | To Do |
| 📋 | FoxproRewrite | [FOX-26](https://universalsoftware.atlassian.net/browse/FOX-26) |  Redesign the "Owner" Tab in FoxPro | Not set | No due date | 0.0% | To Do |
| 📋 | FoxproRewrite | [FOX-13](https://universalsoftware.atlassian.net/browse/FOX-13) | Implement Application Security for FoxPro Modernization | Not set | No due date | 0.0% | To Do |
| 📋 | FoxproRewrite | [FOX-10](https://universalsoftware.atlassian.net/browse/FOX-10) | Project Initialization for FoxPro Rewrite | Not set | No due date | 0.0% | To Do |
| 📋 | Fleet Tracker | [FLEET-106](https://universalsoftware.atlassian.net/browse/FLEET-106) | Finishing Asset stepper - rates | Not set | No due date | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-99](https://universalsoftware.atlassian.net/browse/FLEET-99) | Fleet Tracker Login | Not set | No due date | N/A | Refining |
| 📋 | Fleet Tracker | [FLEET-90](https://universalsoftware.atlassian.net/browse/FLEET-90) | Imaging (placeholder) | Not set | No due date | 0.0% | Refining |
| ✅ | Fleet Tracker | [FLEET-5](https://universalsoftware.atlassian.net/browse/FLEET-5) | Setup CI/CD Pipeline (GoCD) | Not set | No due date | 100.0% | Refining |
| 📋 | FilemakerRewrite | [FIL-16](https://universalsoftware.atlassian.net/browse/FIL-16) | Create Screen/Module where Orders can be managed for Dispatch and resources assigned. | Not set | No due date | 0.0% | To Do |
| 📋 | FilemakerRewrite | [FIL-15](https://universalsoftware.atlassian.net/browse/FIL-15) | Create Screen/Module where Orders can be created | Not set | No due date | 33.3% | To Do |
| 📋 | CNH Goodfield | [CNHG-2](https://universalsoftware.atlassian.net/browse/CNHG-2) | Goodfield DMZ - Website and Reporting Service Connection | Not set | No due date | 0.0% | Refining |
| 📋 | AppDev | [AP-70](https://universalsoftware.atlassian.net/browse/AP-70) | MX Excel to EDI Change | Not set | No due date | 50.0% | In Progress |
| 📋 | Alliance Laundry - General Tickets  | [AL-31](https://universalsoftware.atlassian.net/browse/AL-31) | Alliance Laundry V2 Migration Cleanup | Not set | No due date | N/A | In Progress |

## Recently Completed (Last 5 Items)

| Status | Project | Jira ID | Name | Completed Date | Assignee |
|--------|---------|---------|------|----------------|----------|
| ✅ | WMS 3.0 Research  | [YJ3-50](https://universalsoftware.atlassian.net/browse/YJ3-50) | Rework WMS tables to remove as many NVARCHAR fields as possible in favor of using Foreign Keys to other tables | Unknown | Unassigned |
| ✅ | WMS 3.0 Research  | [YJ3-47](https://universalsoftware.atlassian.net/browse/YJ3-47) | Setup WMS 3 Website on new 2022 Dev Server | Unknown | Hunter Vallad |
| ✅ | WMS 3.0 Research  | [YJ3-45](https://universalsoftware.atlassian.net/browse/YJ3-45) | Parts - YTD Cumulative Total | Unknown | Unassigned |
| ✅ | WMS 3.0 Research  | [YJ3-44](https://universalsoftware.atlassian.net/browse/YJ3-44) | Parts - ASN Receipts | Unknown | Unassigned |
| ✅ | WMS 3.0 Research  | [YJ3-43](https://universalsoftware.atlassian.net/browse/YJ3-43) | Parts - Inbound Receipts | Unknown | Unassigned |

