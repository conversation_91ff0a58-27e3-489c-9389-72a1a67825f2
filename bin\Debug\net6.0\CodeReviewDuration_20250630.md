# Code Review Duration Report

## Tickets Currently in Review

| Status | Ticket | Summary | Assignee | Days in Review | Entered Review |
|--------|--------|---------|----------|----------------|----------------|
| 🚨 | [NVSSA-5](https://universalsoftware.atlassian.net/browse/NVSSA-5) | Navistar - Printer Managment Page | Shayne Vallad | 145 | 2025-02-05 |
| 🚨 | [SBZ-10](https://universalsoftware.atlassian.net/browse/SBZ-10) | ASN Location Management Page - What If Scenario - No Printer | <PERSON> | 102 | 2025-03-19 |
| 🚨 | [WES-106](https://universalsoftware.atlassian.net/browse/WES-106) | Migrating the outbound status (ready to depart popup) from YMS to WES - frontend | <PERSON> | 84 | 2025-04-07 |
| 🚨 | [BOEIN-37](https://universalsoftware.atlassian.net/browse/BOEIN-37) | Migrate Label Printer page to desktop app | <PERSON> | 84 | 2025-04-07 |
| 🚨 | [GMFZGT-285](https://universalsoftware.atlassian.net/browse/GMFZGT-285) | Add Feature to View Completed but Not Dispatched Orders | Rakhi Garg | 73 | 2025-04-17 |
| 🚨 | [WSSG-43](https://universalsoftware.atlassian.net/browse/WSSG-43) | Cycle Count Function Seats WMS | Igor Ivanovski | 48 | 2025-05-12 |
| 🚨 | [GFCGT-77](https://universalsoftware.atlassian.net/browse/GFCGT-77) | Dashboard Time Range | Alex DeLuca | 27 | 2025-06-02 |
| 🚨 | [FLEET-41](https://universalsoftware.atlassian.net/browse/FLEET-41) | Asset detail - Equipment tab | R Sanders | 26 | 2025-06-04 |
| 🚨 | [YP-76](https://universalsoftware.atlassian.net/browse/YP-76) | QA Unit Testing of YP-32: Sorting By Categories - Carrier Report | George Moshi | 23 | 2025-06-06 |
| 🚨 | [YP-119](https://universalsoftware.atlassian.net/browse/YP-119) | Add SCAC column to carrier | Ludmil Gueorguiev | 19 | 2025-06-11 |
| 🚨 | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | Igor Ivanovski | 18 | 2025-06-12 |
| 🚨 | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | Rakhi Garg | 16 | 2025-06-13 |
| 🚨 | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | Alex Gonzalez | 14 | 2025-06-16 |
| 🚨 | [YP-92](https://universalsoftware.atlassian.net/browse/YP-92) | Research how to accommodate the switcher queue in the database and generate the data structure | Ludmil Gueorguiev | 13 | 2025-06-17 |
| 🚨 | [WSSG-49](https://universalsoftware.atlassian.net/browse/WSSG-49) | Change the BOL for Seats | Andres Marcelo Garza Cantu | 13 | 2025-06-16 |
| 🚨 | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | Amy DeRousha | 13 | 2025-06-16 |
| 🚨 | [YP-126](https://universalsoftware.atlassian.net/browse/YP-126) | Menu to add zone master | Ludmil Gueorguiev | 12 | 2025-06-18 |
| 🚨 | [YP-129](https://universalsoftware.atlassian.net/browse/YP-129) | Zone Master - adding to database | Ludmil Gueorguiev | 11 | 2025-06-19 |
| 🚨 | [DTCMS-92](https://universalsoftware.atlassian.net/browse/DTCMS-92) | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | Cameron Rye | 10 | 2025-06-20 |
| 🚨 | [BMWS-395](https://universalsoftware.atlassian.net/browse/BMWS-395) | Resolve Permission Issues for Android 14 in Xamarin App | Matthew Berryhill | 10 | 2025-06-19 |
| 🚨 | [FLEET-100](https://universalsoftware.atlassian.net/browse/FLEET-100) | Asset detail - Side-panel Features (Reinstate) | R Sanders | 7 | 2025-06-23 |
| ⚠️ | [POR-17](https://universalsoftware.atlassian.net/browse/POR-17) | Location & Job Profile Filters for Workday Demographic API | Unassigned | 6 | 2025-06-23 |
| ⚠️ | [BMWS-306](https://universalsoftware.atlassian.net/browse/BMWS-306) | Refactor SFG Generator Code for SFG Forecasting Page Update | Isaiah Childs | 5 | 2025-06-24 |
| ⚠️ | [YP-134](https://universalsoftware.atlassian.net/browse/YP-134) | Zone Master - edit yard locations | Ludmil Gueorguiev | 4 | 2025-06-26 |
| ⚠️ | [SBZ-112](https://universalsoftware.atlassian.net/browse/SBZ-112) | Dashboard Displays for TVs | Tyler Meholic | 4 | 2025-06-26 |
| ⚠️ | [YP-138](https://universalsoftware.atlassian.net/browse/YP-138) | Zone Master - Yard Location search by zone | Ludmil Gueorguiev | 3 | 2025-06-27 |
| ⚠️ | [YP-47](https://universalsoftware.atlassian.net/browse/YP-47) | Yard Check Module - Menu | Trent Meyering | 3 | 2025-06-26 |
| ⚠️ | [FLEET-101](https://universalsoftware.atlassian.net/browse/FLEET-101) | Asset detail - Side-panel Features (Change Contractor) | R Sanders | 3 | 2025-06-26 |
| ⚠️ | [CTP-125](https://universalsoftware.atlassian.net/browse/CTP-125) | Database modifications for note Attachments | Ben Blazy | 3 | 2025-06-27 |
| ⚠️ | [CTP-121](https://universalsoftware.atlassian.net/browse/CTP-121) | Database modifications for Control Tower Notes | Ben Blazy | 3 | 2025-06-27 |
| ⚠️ | [BMWS-413](https://universalsoftware.atlassian.net/browse/BMWS-413) | Configure Dashboard URLs for All Supported Commodities | Unassigned | 3 | 2025-06-26 |
| ⚠️ | [AL-269](https://universalsoftware.atlassian.net/browse/AL-269) | Pick Mins to Stock | Alex DeLuca | 3 | 2025-06-26 |
| ✅ | [BMWS-430](https://universalsoftware.atlassian.net/browse/BMWS-430) | Update Error Message on Failed Label Prints in DCO Service | Isaiah Childs | 0 | 2025-06-30 |

## Summary Statistics

- **Total tickets in review:** 33
- **Average days in review:** 24.5
- **Longest in review:** 145 days
- **Tickets over 3 days:** 25
- **Tickets over 7 days:** 20

