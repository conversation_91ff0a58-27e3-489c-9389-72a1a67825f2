# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Waiting on Customer (BLOCKED)

_No items waiting on customer._

## Blocked Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| ⚠️ | Boeing Mesa - General Tickets  | [BN-126](https://universalsoftware.atlassian.net/browse/BN-126) | Boeing Mesa - New Report - On Hand Inventory w/ ReceiptID | 2025-04-02 | Blocked/Deferred | 96 days overdue | 12 days | <PERSON> |
| ⚠️ | Boeing Mesa - General Tickets  | [BN-91](https://universalsoftware.atlassian.net/browse/BN-91) | Boeing Mesa - Pick Confirm - RQST_WORK_CENTER Included in Pick Confirm File (Unplanned Pick Request) | 2025-06-27 | Blocked/Deferred | 10 days overdue | 12 days | <PERSON> |
| 🚨 | BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 178 days remaining | 26 days | Shayne Vallad |
| 🟡 | SubZero  | [SBZ-111](https://universalsoftware.atlassian.net/browse/SBZ-111) | Cycle Ordering | Not set | Blocked/Deferred | No due date | 4 days | Tyler Meholic |
| 🚨 | GM - Factory Zero - General Tickets  | [GMFZGT-289](https://universalsoftware.atlassian.net/browse/GMFZGT-289) | Part Lookup - Part Receipts - Inconsistent Dates | Not set | Blocked/Deferred | No due date | 94 days | Andres Marcelo Garza Cantu |
| 🚨 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | Blocked/Deferred | No due date | 40 days | Adam Caldwell |
| 🚨 | GM Flint Torrey Rd | [GMFNT-72](https://universalsoftware.atlassian.net/browse/GMFNT-72) | Create Admin Master BOL Update Page | Not set | Blocked/Deferred | No due date | 110 days | David Pierce |
| ⚠️ | Container Management System | [CMS-20](https://universalsoftware.atlassian.net/browse/CMS-20) | Verify Manifest Page | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-19](https://universalsoftware.atlassian.net/browse/CMS-19) | Import Pkg Sched | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-18](https://universalsoftware.atlassian.net/browse/CMS-18) | Print Route Manifest | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-17](https://universalsoftware.atlassian.net/browse/CMS-17) | ASN Lookup Page | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| 🟡 | BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | Not set | Blocked/Deferred | No due date | 5 days | Cameron Rye |
| 🟡 | BMW Spartanburg | [BMWS-139](https://universalsoftware.atlassian.net/browse/BMWS-139) | Inbound ASN Presequence screen - scanning delivery note (post-launch) | Not set | Blocked/Deferred | No due date | 4 days | Tyler Meholic |

## Late Items

| Status | Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|--------|---------|---------|------|----------|--------------|----------|
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | 27 days overdue | Alex Gonzalez |
| 🚨 | Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | 26 days overdue | Rakhi Garg |
| ⚠️ | BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | 7 days overdue | Antonio Silva |
| 🟡 | BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-07-03 | 4 days overdue | Amy DeRousha |

## Coming Soon (Next 7 Days)

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|--------|---------|---------|------|----------|----------------|----------|
| 🚨 | GM - Flint Continental - General Tickets  | [GFCGT-77](https://universalsoftware.atlassian.net/browse/GFCGT-77) | Dashboard Time Range | 2025-07-07 | Due today | Alex DeLuca |
| 🚨 | BMW Spartanburg | [BMWS-440](https://universalsoftware.atlassian.net/browse/BMWS-440) | Update Stored Procedures Supporting the 'ASN Generation' Process | 2025-07-07 | Due today | Antonio Silva |
| 🚨 | FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-08 | 1 days remaining | Adam Caldwell |
| 🚨 | BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-07-08 | 1 days remaining | Justin Kerketta |
| ⚠️ | Boeing - XDock - General Tickets  | [BOEIN-39](https://universalsoftware.atlassian.net/browse/BOEIN-39) | EDI File Upload | 2025-07-09 | 2 days remaining | Chris Collins |
| 🟡 | GM ODC Launch  | [GMDCL-82](https://universalsoftware.atlassian.net/browse/GMDCL-82) | Routing Processor - Prevent Web Service Errors | 2025-07-11 | 4 days remaining | Adam Caldwell |
| 🟡 | FCA XDock  | [FX-27](https://universalsoftware.atlassian.net/browse/FX-27) | MGO Status Updater - Update All Interface Msg Records For On-Hand | 2025-07-11 | 4 days remaining | Adam Caldwell |
| 🟡 | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | 4 days remaining | Hunter Vallad |
| 🟡 | BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-11 | 4 days remaining | Justin Kerketta |

## Project on Hold (BLOCKED)

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| ⚠️ | Container Management System | [CMS-3](https://universalsoftware.atlassian.net/browse/CMS-3) | CMS UI Update - Add Loading Panels | Not set | Project on Hold (BLOCKED) | No due date | 7 days | Adam Caldwell |

## Overall Summary

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|--------|---------|---------|------|----------|----------------|------------|--------|
| ⚠️ | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | 4 days remaining | 15.4% | Refining |
| ✅ | BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 55 days remaining | 100.0% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 55 days remaining | 27.8% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 55 days remaining | 64.7% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 55 days remaining | 7.1% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 55 days remaining | 50.0% | In Progress |
| 📋 | Westport - WheelTire - General Tickets  | [WSWTGT-47](https://universalsoftware.atlassian.net/browse/WSWTGT-47) | Support TPMS Sensor | Not set | No due date | 0.0% | In Progress |
| 📋 | Westport - Painted Parts - General Tickets   | [WSPPGT-32](https://universalsoftware.atlassian.net/browse/WSPPGT-32) | Implement New Requested File Format | Not set | No due date | 60.0% | Refining |
| 📋 | Value Added  | [VAL-445](https://universalsoftware.atlassian.net/browse/VAL-445) | Change the BOL for Seats, Painted Parts, and Wheel Tire | Not set | No due date | 0.0% | In Progress |
| 📋 | Value Added  | [VAL-9](https://universalsoftware.atlassian.net/browse/VAL-9) | General support between security and Value-Added WMS development team to patch vulnerabilities in our applications | Not set | No due date | 28.6% | Ready |
| 🟡 | GM - Factory Zero - General Tickets  | [GMFZGT-8](https://universalsoftware.atlassian.net/browse/GMFZGT-8) | Efforts for cleaning up SQL and dead code found in the app | Not set | No due date | 75.0% | In Progress |
| 📋 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | No due date | N/A | Blocked/Deferred |
| 📋 | CNH Goodfield | [CNHG-2](https://universalsoftware.atlassian.net/browse/CNHG-2) | Goodfield DMZ - Website and Reporting Service Connection | Not set | No due date | 0.0% | Refining |
| 📋 | Alliance Laundry - General Tickets  | [AL-31](https://universalsoftware.atlassian.net/browse/AL-31) | Alliance Laundry V2 Migration Cleanup | Not set | No due date | N/A | In Progress |

## Developer Summary

### Adam Caldwell

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-08 | Validated | 1 days remaining |
| GM ODC Launch  | [GMDCL-82](https://universalsoftware.atlassian.net/browse/GMDCL-82) | Routing Processor - Prevent Web Service Errors | 2025-07-11 | In Progress | 4 days remaining |
| FCA XDock  | [FX-27](https://universalsoftware.atlassian.net/browse/FX-27) | MGO Status Updater - Update All Interface Msg Records For On-Hand | 2025-07-11 | In Progress | 4 days remaining |

### Alex DeLuca

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| GM - Flint Continental - General Tickets  | [GFCGT-77](https://universalsoftware.atlassian.net/browse/GFCGT-77) | Dashboard Time Range | 2025-07-07 | Approved | Due today |

### Alex Gonzalez

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | Validated | 27 days overdue |

### Amy DeRousha

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-07-03 | Review | 4 days overdue |

### Andres Marcelo Garza Cantu

#### In Progress Items

_No items in progress._

### Antonio Silva

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | Review | 7 days overdue |
| BMW Spartanburg | [BMWS-440](https://universalsoftware.atlassian.net/browse/BMWS-440) | Update Stored Procedures Supporting the 'ASN Generation' Process | 2025-07-07 | In Progress | Due today |

### Cameron Rye

#### In Progress Items

_No items in progress._

### Chris Collins

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Boeing - XDock - General Tickets  | [BOEIN-39](https://universalsoftware.atlassian.net/browse/BOEIN-39) | EDI File Upload | 2025-07-09 | Validated | 2 days remaining |

### Hunter Vallad

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | Refining | 4 days remaining |

### Justin Kerketta

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-07-08 | In Progress | 1 days remaining |
| BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-11 | In Progress | 4 days remaining |

### Rakhi Garg

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | Validated | 26 days overdue |

### Shayne Vallad

#### In Progress Items

_No items in progress._

### Tyler Meholic

#### In Progress Items

_No items in progress._

## Recently Completed (Last 5 Items)

| Status | Project | Jira ID | Name | Completed Date | Assignee |
|--------|---------|---------|------|----------------|----------|
| ✅ | WMS User Management Utility | [WUMU-3](https://universalsoftware.atlassian.net/browse/WUMU-3) | Remove References to all three Lear Tuscaloosa Instances from User Management | Unknown | Hunter Vallad |
| ✅ | WMS User Management Utility | [WUMU-2](https://universalsoftware.atlassian.net/browse/WUMU-2) | Remove References to All Lucid Sites In User Management Utility | Unknown | Hunter Vallad |
| ✅ | Westport - WheelTire - General Tickets  | [WSWTGT-46](https://universalsoftware.atlassian.net/browse/WSWTGT-46) | Check the performance of PinPoint_InflationStatus_WheelTire_Westport | Unknown | Shayne Vallad |
| ✅ | Westport - WheelTire - General Tickets  | [WSWTGT-45](https://universalsoftware.atlassian.net/browse/WSWTGT-45) | Fix Pinpoint_LoadingComplete_WheelTire_Westport | Unknown | Shayne Vallad |
| ✅ | Westport - WheelTire - General Tickets  | [WSWTGT-43](https://universalsoftware.atlassian.net/browse/WSWTGT-43) | PinPoint WorkOrders Error M359048802 | Unknown | Shayne Vallad |

