# Jira Report Generator

A .NET console application that generates comprehensive reports from <PERSON><PERSON>, including blocked items, late items, upcoming items, and an overall summary of initiatives and epics.

## Prerequisites

- .NET 6.0 or later
- Jira account with API access
- Jira API token

## Setup

1. Clone the repository
2. Navigate to the project directory
3. Update the `appsettings.json` file with your Jira credentials:
   ```json
   {
     "JiraSettings": {
       "ServerUrl": "YOUR_JIRA_SERVER_URL",
       "Username": "YOUR_JIRA_USERNAME",
       "ApiToken": "YOUR_JIRA_API_TOKEN"
     }
   }
   ```
4. Update the project key in `Program.cs` (replace "YOUR_PROJECT_KEY" with your actual Jira project key)

## Running the Application

### Generate Daily Report (default)
```bash
dotnet run
```

### Generate Capacity Report
```bash
dotnet run capacity
```

## Report Contents

The application generates a report with the following sections:

1. **Blocked Items**
   - Lists all items in the "Blocked" state
   - Includes Jira ID, name, and due date

2. **Late Items**
   - Lists all items that are past due
   - Includes assignee, due date, start date, and full hierarchy
   - Shows parent-child relationships

3. **Coming Soon**
   - Lists items due within the next week
   - Includes the same details as Late Items

4. **Overall Summary**
   - Lists all initiatives and epics
   - Shows completion percentage
   - Includes due dates

## Notes

- Make sure your Jira API token has the necessary permissions to read issues
- The application assumes certain Jira field names and status values. You may need to adjust these based on your Jira configuration
- The hierarchy display assumes a parent-child relationship between issues 