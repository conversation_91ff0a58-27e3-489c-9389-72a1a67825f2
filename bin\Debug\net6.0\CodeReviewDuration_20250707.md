# Code Review Duration Report

## Tickets Currently in Review

| Status | Ticket | Summary | Assignee | Days in Review | Entered Review |
|--------|--------|---------|----------|----------------|----------------|
| 🚨 | [GMFZGT-285](https://universalsoftware.atlassian.net/browse/GMFZGT-285) | Add Feature to View Completed but Not Dispatched Orders | Rakhi Garg | 80 | 2025-04-17 |
| 🚨 | [WSSG-43](https://universalsoftware.atlassian.net/browse/WSSG-43) | Cycle Count Function Seats WMS | <PERSON> | 55 | 2025-05-12 |
| 🚨 | [YP-119](https://universalsoftware.atlassian.net/browse/YP-119) | Add SCAC column to carrier | Ludmil Gueorguiev | 26 | 2025-06-11 |
| 🚨 | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | <PERSON> | 25 | 2025-06-12 |
| 🚨 | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | Amy DeRousha | 20 | 2025-06-16 |
| 🚨 | [BMWS-395](https://universalsoftware.atlassian.net/browse/BMWS-395) | Resolve Permission Issues for Android 14 in Xamarin App | Matthew Berryhill | 17 | 2025-06-19 |
| 🚨 | [SBZ-112](https://universalsoftware.atlassian.net/browse/SBZ-112) | Dashboard Displays for TVs | Tyler Meholic | 10 | 2025-06-26 |
| 🚨 | [CTP-125](https://universalsoftware.atlassian.net/browse/CTP-125) | Database modifications for note Attachments | Ben Blazy | 10 | 2025-06-27 |
| 🚨 | [CTP-121](https://universalsoftware.atlassian.net/browse/CTP-121) | Database modifications for Control Tower Notes | Ben Blazy | 10 | 2025-06-27 |
| ⚠️ | [BMWS-405](https://universalsoftware.atlassian.net/browse/BMWS-405) | BMW - Packout Old Data Cleanup | Shayne Vallad | 6 | 2025-07-01 |
| ⚠️ | [DTCMS-92](https://universalsoftware.atlassian.net/browse/DTCMS-92) | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | Cameron Rye | 5 | 2025-07-02 |
| ⚠️ | [BMWS-428](https://universalsoftware.atlassian.net/browse/BMWS-428) | Remove Duplicated Serials - 06/30 | Matthew Berryhill | 5 | 2025-07-01 |
| ⚠️ | [BMWS-451](https://universalsoftware.atlassian.net/browse/BMWS-451) | Update Translator Service to Use New Tracker Tables for Printing | Isaiah Childs | 4 | 2025-07-03 |
| ⚠️ | [BMWS-448](https://universalsoftware.atlassian.net/browse/BMWS-448) | Fix System Error on Part Number Search in TOD Line Items Menu | Justin Mosley | 4 | 2025-07-03 |
| ⚠️ | [BMWS-437](https://universalsoftware.atlassian.net/browse/BMWS-437) | Update Stored Procedures Supporting the 'Rack Building' Process | Isaiah Childs | 4 | 2025-07-03 |
| ⚠️ | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | Antonio Silva | 3 | 2025-07-03 |

## Summary Statistics

- **Total tickets in review:** 16
- **Average days in review:** 17.8
- **Longest in review:** 80 days
- **Tickets over 3 days:** 15
- **Tickets over 7 days:** 9

