# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Late Items

| Status | Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|--------|---------|---------|------|----------|--------------|----------|
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | 20 days overdue | <PERSON> |
| 🚨 | Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | 19 days overdue | Rakhi Garg |
| 🟡 | DTNA CMS | [DTCMS-92](https://universalsoftware.atlassian.net/browse/DTCMS-92) | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | 2025-06-27 | 3 days overdue | <PERSON> Rye |
| 🟡 | Boeing Mesa - General Tickets  | [BN-122](https://universalsoftware.atlassian.net/browse/BN-122) | Blind Audit Manager Override Feature MESA Boeing  | 2025-06-27 | 3 days overdue | Chris Collins |
| 🟡 | Boeing Mesa - General Tickets  | [BN-38](https://universalsoftware.atlassian.net/browse/BN-38) | Kitted Picked Orders Blind Audit Boeing Mesa UAT | 2025-06-27 | 3 days overdue | Chris Collins |
| 🟡 | Boeing Mesa - General Tickets  | [BN-20](https://universalsoftware.atlassian.net/browse/BN-20) | Boeing Mesa - Outbound - Blind BOM item count | 2025-06-27 | 3 days overdue | Chris Collins |
| 🟡 | BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-27 | 3 days overdue | Cameron Rye |
| 🟡 | BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | 3 days overdue | Justin Kerketta |
| 🟡 | BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | 3 days overdue | Amy DeRousha |
| 🟡 | BMW Spartanburg | [BMWS-413](https://universalsoftware.atlassian.net/browse/BMWS-413) | Configure Dashboard URLs for All Supported Commodities | 2025-06-29 | 1 days overdue | Unassigned |

## Blocked Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| 🟡 | Boeing Mesa - General Tickets  | [BN-126](https://universalsoftware.atlassian.net/browse/BN-126) | Boeing Mesa - New Report - On Hand Inventory w/ ReceiptID | 2025-04-02 | Blocked/Deferred | 89 days overdue | 5 days | Chris Collins |
| 🟡 | Boeing Mesa - General Tickets  | [BN-91](https://universalsoftware.atlassian.net/browse/BN-91) | Boeing Mesa - Pick Confirm - RQST_WORK_CENTER Included in Pick Confirm File (Unplanned Pick Request) | 2025-06-27 | Blocked/Deferred | 3 days overdue | 5 days | Chris Collins |
| 🚨 | BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 185 days remaining | 19 days | Shayne Vallad |
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-30](https://universalsoftware.atlassian.net/browse/WSPPGT-30) | Painted Parts accepting new Chassis prefix | Not set | Blocked/Deferred | No due date | 24 days | Igor Ivanovski |
| ⚠️ | Value Added  | [VAL-48](https://universalsoftware.atlassian.net/browse/VAL-48) | Smyrna - SSRS Report Server - Vulnerability Updates | Not set | Blocked/Deferred | No due date | 10 days | Adam Caldwell |
| ⚠️ | Value Added  | [VAL-47](https://universalsoftware.atlassian.net/browse/VAL-47) | Clark Street - SSRS Report Server - Vulnerability Updates | Not set | Blocked/Deferred | No due date | 10 days | Adam Caldwell |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-79](https://universalsoftware.atlassian.net/browse/TAMA-79) | File Generator - Archive File Name | Not set | Blocked/Deferred | No due date | 19 days | Unassigned |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-78](https://universalsoftware.atlassian.net/browse/TAMA-78) | update LastLoginDate column in Users table | Not set | Blocked/Deferred | No due date | 19 days | Unassigned |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-72](https://universalsoftware.atlassian.net/browse/TAMA-72) | Create Web Portal Documentation To Share With Customers | Not set | Blocked/Deferred | No due date | 19 days | Unassigned |
| 🚨 | GM - Factory Zero - General Tickets  | [GMFZGT-289](https://universalsoftware.atlassian.net/browse/GMFZGT-289) | Part Lookup - Part Receipts - Inconsistent Dates | Not set | Blocked/Deferred | No due date | 87 days | Andres Marcelo Garza Cantu |
| 🚨 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | Blocked/Deferred | No due date | 33 days | Adam Caldwell |
| 🚨 | GM Flint Torrey Rd | [GMFNT-72](https://universalsoftware.atlassian.net/browse/GMFNT-72) | Create Admin Master BOL Update Page | Not set | Blocked/Deferred | No due date | 103 days | David Pierce |
| 🚨 | GM - Flint Continental - General Tickets  | [GFCGT-67](https://universalsoftware.atlassian.net/browse/GFCGT-67) | No Pick Function request | Not set | Blocked/Deferred | No due date | 80 days | Unassigned |
| 🚨 | Flint - Torrey - General Tickets  | [FLIN-4](https://universalsoftware.atlassian.net/browse/FLIN-4) | Enable Image Capture Functionality for Scanner Devices | Not set | Blocked/Deferred | No due date | 60 days | Unassigned |
| 🔴 | Container Management System | [CMS-20](https://universalsoftware.atlassian.net/browse/CMS-20) | Verify Manifest Page | Not set | Blocked/Deferred | No due date | 0 days | Adam Caldwell |
| 🔴 | Container Management System | [CMS-19](https://universalsoftware.atlassian.net/browse/CMS-19) | Import Pkg Sched | Not set | Blocked/Deferred | No due date | 0 days | Adam Caldwell |
| 🔴 | Container Management System | [CMS-18](https://universalsoftware.atlassian.net/browse/CMS-18) | Print Route Manifest | Not set | Blocked/Deferred | No due date | 0 days | Adam Caldwell |
| 🔴 | Container Management System | [CMS-17](https://universalsoftware.atlassian.net/browse/CMS-17) | ASN Lookup Page | Not set | Blocked/Deferred | No due date | 0 days | Adam Caldwell |
| 🔴 | Container Management System | [CMS-3](https://universalsoftware.atlassian.net/browse/CMS-3) | CMS UI Update - Add Loading Panels | Not set | Blocked/Deferred | No due date | 0 days | Adam Caldwell |
| 🟡 | BMW Spartanburg | [BMWS-383](https://universalsoftware.atlassian.net/browse/BMWS-383) | Replace READ UNCOMMITTED with NOLOCK in Stored Procedures for OutboundSeq Page | Not set | Blocked/Deferred | No due date | 4 days | Matthew Berryhill |

## Coming Soon (Next 7 Days)

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|--------|---------|---------|------|----------|----------------|----------|
| 🚨 | BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | Due today | Antonio Silva |
| 🚨 | Westport - Seats - General Tickets  | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | 2025-07-01 | 1 days remaining | Igor Ivanovski |
| 🚨 | FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-01 | 1 days remaining | Adam Caldwell |
| 🚨 | BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | 2025-07-01 | 1 days remaining | Cameron Rye |
| 🟡 | BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-04 | 4 days remaining | Justin Kerketta |
| ✅ | BMW Spartanburg | [BMWS-430](https://universalsoftware.atlassian.net/browse/BMWS-430) | Update Error Message on Failed Label Prints in DCO Service | 2025-07-06 | 6 days remaining | Isaiah Childs |

## Overall Summary

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|--------|---------|---------|------|----------|----------------|------------|--------|
| 📋 | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | 11 days remaining | 50.0% | Refining |
| ✅ | BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 62 days remaining | 100.0% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 62 days remaining | 25.0% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 62 days remaining | 58.8% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 62 days remaining | 0.0% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 62 days remaining | 45.5% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-379](https://universalsoftware.atlassian.net/browse/BMWS-379) | Controls API | 2025-09-30 | 92 days remaining | 0.0% | Refining |
| 📋 | Westport - WheelTire - General Tickets  | [WSWTGT-47](https://universalsoftware.atlassian.net/browse/WSWTGT-47) | Support TPMS Sensor | Not set | No due date | 0.0% | In Progress |
| 📋 | Westport - Painted Parts - General Tickets   | [WSPPGT-32](https://universalsoftware.atlassian.net/browse/WSPPGT-32) | Implement New Requested File Format | Not set | No due date | 0.0% | Refining |
| 📋 | Westport Axle Rewrite  | [WSAR-1](https://universalsoftware.atlassian.net/browse/WSAR-1) | Westport - Axle - WMS Migration | Not set | No due date | 42.9% | Refining |
| 📋 | WMS Android Future Features | [WAFF-6](https://universalsoftware.atlassian.net/browse/WAFF-6) | Android - Icon Fixes | Not set | No due date | N/A | Refining |
| 📋 | WMS Android Future Features | [WAFF-4](https://universalsoftware.atlassian.net/browse/WAFF-4) | Preference Management - Local Endpoint Updates | Not set | No due date | N/A | Backlog |
| 📋 | Value Added  | [VAL-445](https://universalsoftware.atlassian.net/browse/VAL-445) | Change the BOL for Seats, Painted Parts, and Wheel Tire | Not set | No due date | 0.0% | Refining |
| 📋 | Value Added  | [VAL-29](https://universalsoftware.atlassian.net/browse/VAL-29) | Redgate Source Control - Compile Site Schemas into Version Control | Not set | No due date | 0.0% | Backlog |
| 📋 | Value Added  | [VAL-9](https://universalsoftware.atlassian.net/browse/VAL-9) | General support between security and Value-Added WMS development team to patch vulnerabilities in our applications | Not set | No due date | 0.0% | Ready |
| 📋 | Value Added  | [VAL-5](https://universalsoftware.atlassian.net/browse/VAL-5) | Planning on migrating features into the master branch for global application support | Not set | No due date | 66.7% | Ready |
| 📋 | SubZero  | [SBZ-114](https://universalsoftware.atlassian.net/browse/SBZ-114) | Create Dashboards for warehouse managers | Not set | No due date | 0.0% | Refining |
| 🟡 | GM - Factory Zero - General Tickets  | [GMFZGT-8](https://universalsoftware.atlassian.net/browse/GMFZGT-8) | Efforts for cleaning up SQL and dead code found in the app | Not set | No due date | 75.0% | In Progress |
| 📋 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | No due date | N/A | Blocked/Deferred |
| 📋 | CNH Goodfield | [CNHG-2](https://universalsoftware.atlassian.net/browse/CNHG-2) | Goodfield DMZ - Website and Reporting Service Connection | Not set | No due date | 0.0% | Refining |
| 📋 | BMW Spartanburg | [BMWS-429](https://universalsoftware.atlassian.net/browse/BMWS-429) | Shane Henson - Open Topics - 06/30 | Not set | No due date | N/A | Backlog |
| 📋 | Alliance Laundry - General Tickets  | [AL-31](https://universalsoftware.atlassian.net/browse/AL-31) | Alliance Laundry V2 Migration Cleanup | Not set | No due date | N/A | In Progress |

## Developer Summary

### Adam Caldwell

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-01 | In Progress | 1 days remaining |

### Alex Gonzalez

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | Review | 20 days overdue |

### Andres Marcelo Garza Cantu

#### In Progress Items

_No items in progress._

### Cameron Rye

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| DTNA CMS | [DTCMS-92](https://universalsoftware.atlassian.net/browse/DTCMS-92) | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | 2025-06-27 | Review | 3 days overdue |

### Chris Collins

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Boeing Mesa - General Tickets  | [BN-122](https://universalsoftware.atlassian.net/browse/BN-122) | Blind Audit Manager Override Feature MESA Boeing  | 2025-06-27 | In Progress | 3 days overdue |
| Boeing Mesa - General Tickets  | [BN-38](https://universalsoftware.atlassian.net/browse/BN-38) | Kitted Picked Orders Blind Audit Boeing Mesa UAT | 2025-06-27 | In Progress | 3 days overdue |
| Boeing Mesa - General Tickets  | [BN-20](https://universalsoftware.atlassian.net/browse/BN-20) | Boeing Mesa - Outbound - Blind BOM item count | 2025-06-27 | Rework | 3 days overdue |

### David Pierce

#### In Progress Items

_No items in progress._

### Igor Ivanovski

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Westport - Seats - General Tickets  | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | 2025-07-01 | Review | 1 days remaining |

### Rakhi Garg

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | Review | 19 days overdue |

