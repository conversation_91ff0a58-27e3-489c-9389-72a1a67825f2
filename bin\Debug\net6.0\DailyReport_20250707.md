# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Waiting on Customer (BLOCKED)

_No items waiting on customer._

## Late Items

| Status | Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|--------|---------|---------|------|----------|--------------|----------|
| 🚨 | WES | [WES-103](https://universalsoftware.atlassian.net/browse/WES-103) | Migrating the container search report (generate report button) from YMS to WES - frontend | 2025-05-30 | 38 days overdue | Unassigned |
| 🚨 | WES | [WES-20](https://universalsoftware.atlassian.net/browse/WES-20) | Migrating the container search report from YMS to WES - frontend | 2025-05-30 | 38 days overdue | Unassigned |
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | 27 days overdue | <PERSON> |
| 🚨 | Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | 26 days overdue | Rakhi Garg |
| ⚠️ | WES | [WES-139](https://universalsoftware.atlassian.net/browse/WES-139) | Migrating Container Page (action menu - empty type options) - At Docks - from YMS to WES | 2025-06-30 | 7 days overdue | Unassigned |
| ⚠️ | DTNA RFID | [DR-3](https://universalsoftware.atlassian.net/browse/DR-3) | Clone Boeing instance for RFID testing  | 2025-06-30 | 7 days overdue | Unassigned |
| ⚠️ | BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | 7 days overdue | Antonio Silva |
| 🟡 | Westport - Seats - General Tickets  | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | 2025-07-01 | 6 days overdue | Igor Ivanovski |
| 🟡 | BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-07-03 | 4 days overdue | Amy DeRousha |
| 🟡 | YMS Project | [YP-112](https://universalsoftware.atlassian.net/browse/YP-112) | QA Testing of YP-97: In Yard Status Changes - Empty Options | 2025-07-04 | 3 days overdue | Unassigned |
| 🟡 | YMS Project | [YP-111](https://universalsoftware.atlassian.net/browse/YP-111) | QA Testing of YP-96: In Yard Status Changes - Outbound Loading | 2025-07-04 | 3 days overdue | Unassigned |
| 🟡 | YMS Project | [YP-110](https://universalsoftware.atlassian.net/browse/YP-110) | QA Testing of YP-19: In Yard Status Changes - Inbound Actions | 2025-07-04 | 3 days overdue | Unassigned |
| 🟡 | Control Tower | [CTP-128](https://universalsoftware.atlassian.net/browse/CTP-128) | R&D Control Tower Notes | 2025-07-04 | 3 days overdue | Ben Blazy |
| 🟡 | AppDev | [AP-51](https://universalsoftware.atlassian.net/browse/AP-51) | Lane exception Pay rules | 2025-07-04 | 3 days overdue | Aaron Anderson |
| 🟡 | AppDev | [AP-50](https://universalsoftware.atlassian.net/browse/AP-50) | New settlement line item - fuel (lane exception pay) | 2025-07-04 | 3 days overdue | Aaron Anderson |
| 🟡 | AppDev | [AP-49](https://universalsoftware.atlassian.net/browse/AP-49) | Driver system limit update on payout | 2025-07-04 | 3 days overdue | Aaron Anderson |
| 🟡 | AppDev | [AP-41](https://universalsoftware.atlassian.net/browse/AP-41) | Data Update from Atlas to AtlasCommunications failure BUG | 2025-07-04 | 3 days overdue | Aaron Anderson |
| 🟡 | BMW Spartanburg | [BMWS-430](https://universalsoftware.atlassian.net/browse/BMWS-430) | Update Error Message on Failed Label Prints in DCO Service | 2025-07-06 | 1 days overdue | Isaiah Childs |

## Coming Soon (Next 7 Days)

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|--------|---------|---------|------|----------|----------------|----------|
| 🚨 | GM - Flint Continental - General Tickets  | [GFCGT-77](https://universalsoftware.atlassian.net/browse/GFCGT-77) | Dashboard Time Range | 2025-07-07 | Due today | Alex DeLuca |
| 🚨 | FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-07 | Due today | Adam Caldwell |
| 🚨 | Fleet Tracker | [FLEET-101](https://universalsoftware.atlassian.net/browse/FLEET-101) | Asset detail - Side-panel Features (Change Contractor) | 2025-07-07 | Due today | R Sanders |
| 🚨 | Fleet Tracker | [FLEET-100](https://universalsoftware.atlassian.net/browse/FLEET-100) | Asset detail - Side-panel Features (Reinstate) | 2025-07-07 | Due today | R Sanders |
| 🚨 | Fleet Tracker | [FLEET-56](https://universalsoftware.atlassian.net/browse/FLEET-56) | Asset detail - Side-panel Features (Cancel & Redomicile) | 2025-07-07 | Due today | Sean Hogg |
| 🚨 | BMW Spartanburg | [BMWS-440](https://universalsoftware.atlassian.net/browse/BMWS-440) | Update Stored Procedures Supporting the 'ASN Generation' Process | 2025-07-07 | Due today | Antonio Silva |
| 🚨 | BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-07-08 | 1 days remaining | Justin Kerketta |
| ⚠️ | YMS Project | [YP-37](https://universalsoftware.atlassian.net/browse/YP-37) | In Yard Status Changes | 2025-07-09 | 2 days remaining | Emily Gamble |
| ⚠️ | Fleet Tracker | [FLEET-36](https://universalsoftware.atlassian.net/browse/FLEET-36) | Enterprise - Notes tab | 2025-07-09 | 2 days remaining | Jeff Priskorn |
| ⚠️ | Boeing - XDock - General Tickets  | [BOEIN-39](https://universalsoftware.atlassian.net/browse/BOEIN-39) | EDI File Upload | 2025-07-09 | 2 days remaining | Unassigned |
| ⚠️ | AppDev | [AP-48](https://universalsoftware.atlassian.net/browse/AP-48) | Project Lockdown - Atlas Settlements | 2025-07-09 | 2 days remaining | Emily Gamble |
| 🟡 | YMS Project | [YP-124](https://universalsoftware.atlassian.net/browse/YP-124) | Deployment #1 for Phase 2 | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | YMS Project | [YP-122](https://universalsoftware.atlassian.net/browse/YP-122) | Carrier Master update needed for this epic - edit - adding SCAC | 2025-07-11 | 4 days remaining | Ludmil Gueorguiev |
| 🟡 | YMS Project | [YP-120](https://universalsoftware.atlassian.net/browse/YP-120) | Carrier Master update needed for this epic - adding SCAC into carrier creation | 2025-07-11 | 4 days remaining | Ludmil Gueorguiev |
| 🟡 | YMS Project | [YP-50](https://universalsoftware.atlassian.net/browse/YP-50) | Outbound Log Requesed Changes | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | YMS Project | [YP-38](https://universalsoftware.atlassian.net/browse/YP-38) | Trailer Check In Process | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | YMS Project | [YP-36](https://universalsoftware.atlassian.net/browse/YP-36) | Verbiage Changes | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | YMS Project | [YP-21](https://universalsoftware.atlassian.net/browse/YP-21) | Sorting By Categories | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | Highway | [HIG-27](https://universalsoftware.atlassian.net/browse/HIG-27) | Needs to integrate with GP - ULCarrierImporter service and ULCarrierSyncService - Highway | 2025-07-11 | 4 days remaining | Unassigned |
| 🟡 | Highway | [HIG-26](https://universalsoftware.atlassian.net/browse/HIG-26) | update to ULAchEmailService | 2025-07-11 | 4 days remaining | Trent Meyering |
| 🟡 | Highway | [HIG-10](https://universalsoftware.atlassian.net/browse/HIG-10) | Implement Carrier Sync | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | Highway | [HIG-7](https://universalsoftware.atlassian.net/browse/HIG-7) | Implement Carrier Full Update Endpoint | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | Highway | [HIG-4](https://universalsoftware.atlassian.net/browse/HIG-4) | Implement Carrier Alerts Endpoint | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | Highway | [HIG-1](https://universalsoftware.atlassian.net/browse/HIG-1) | Implement Carrier Onboarding Endpoint | 2025-07-11 | 4 days remaining | Emily Gamble |
| 🟡 | GM ODC Launch  | [GMDCL-82](https://universalsoftware.atlassian.net/browse/GMDCL-82) | Routing Processor - Prevent Web Service Errors | 2025-07-11 | 4 days remaining | Adam Caldwell |
| 🟡 | FCA XDock  | [FX-27](https://universalsoftware.atlassian.net/browse/FX-27) | MGO Status Updater - Update All Interface Msg Records For On-Hand | 2025-07-11 | 4 days remaining | Adam Caldwell |
| 🟡 | Fleet Tracker | [FLEET-103](https://universalsoftware.atlassian.net/browse/FLEET-103) | Asset detail - Secured Parking tab (delete) | 2025-07-11 | 4 days remaining | R Sanders |
| 🟡 | Fleet Tracker | [FLEET-43](https://universalsoftware.atlassian.net/browse/FLEET-43) | Asset detail - Secured Parking tab (add/edit) | 2025-07-11 | 4 days remaining | R Sanders |
| 🟡 | Fleet Tracker | [FLEET-42](https://universalsoftware.atlassian.net/browse/FLEET-42) | Asset detail - Plate tab  | 2025-07-11 | 4 days remaining | R Sanders |
| 🟡 | Fleet Tracker | [FLEET-41](https://universalsoftware.atlassian.net/browse/FLEET-41) | Asset detail - Equipment tab | 2025-07-11 | 4 days remaining | R Sanders |
| 🟡 | Fleet Tracker | [FLEET-40](https://universalsoftware.atlassian.net/browse/FLEET-40) | Asset detail - Side-panel Display | 2025-07-11 | 4 days remaining | R Sanders |
| 🟡 | Fleet Tracker | [FLEET-16](https://universalsoftware.atlassian.net/browse/FLEET-16) | Personnel - Personnel tab | 2025-07-11 | 4 days remaining | Jeff Priskorn |
| 🟡 | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | 4 days remaining | Hunter Vallad |
| 🟡 | BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-11 | 4 days remaining | Justin Kerketta |
| 🟡 | AppDev | [AP-57](https://universalsoftware.atlassian.net/browse/AP-57) | Atlas - potential orders entry change | 2025-07-11 | 4 days remaining | Aaron Anderson |
| 🟡 | AppDev | [AP-53](https://universalsoftware.atlassian.net/browse/AP-53) | Atlas EDI 214 Issues - 324048 | 2025-07-11 | 4 days remaining | Aaron Anderson |
| ✅ | Highway | [HIG-31](https://universalsoftware.atlassian.net/browse/HIG-31) | Deploying Highway | 2025-07-14 | 7 days remaining | Unassigned |
| ✅ | Highway | [HIG-25](https://universalsoftware.atlassian.net/browse/HIG-25) | Carriers use EIN or SSN when setting up their company | 2025-07-14 | 7 days remaining | Trent Meyering |

## Blocked Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| ⚠️ | Boeing Mesa - General Tickets  | [BN-126](https://universalsoftware.atlassian.net/browse/BN-126) | Boeing Mesa - New Report - On Hand Inventory w/ ReceiptID | 2025-04-02 | Blocked/Deferred | 96 days overdue | 12 days | Chris Collins |
| ⚠️ | Boeing Mesa - General Tickets  | [BN-91](https://universalsoftware.atlassian.net/browse/BN-91) | Boeing Mesa - Pick Confirm - RQST_WORK_CENTER Included in Pick Confirm File (Unplanned Pick Request) | 2025-06-27 | Blocked/Deferred | 10 days overdue | 12 days | Chris Collins |
| ⚠️ | PortPro Integrations  | [POR-23](https://universalsoftware.atlassian.net/browse/POR-23) | Great Plains Integrations | 2025-08-11 | Blocked/Deferred | 35 days remaining | 7 days | Emily Gamble |
| ⚠️ | PortPro Integrations  | [POR-22](https://universalsoftware.atlassian.net/browse/POR-22) | Highway - Carriers Integrations | 2025-08-11 | Blocked/Deferred | 35 days remaining | 7 days | Emily Gamble |
| ⚠️ | PortPro Integrations  | [POR-21](https://universalsoftware.atlassian.net/browse/POR-21) | Transman (Equipment) Integrations | 2025-08-11 | Blocked/Deferred | 35 days remaining | 7 days | Emily Gamble |
| ⚠️ | PortPro Integrations  | [POR-20](https://universalsoftware.atlassian.net/browse/POR-20) | EFS - Fuel Integrations | 2025-08-11 | Blocked/Deferred | 35 days remaining | 7 days | Emily Gamble |
| 🚨 | BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 178 days remaining | 26 days | Shayne Vallad |
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-30](https://universalsoftware.atlassian.net/browse/WSPPGT-30) | Painted Parts accepting new Chassis prefix | Not set | Blocked/Deferred | No due date | 31 days | Igor Ivanovski |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-79](https://universalsoftware.atlassian.net/browse/TAMA-79) | File Generator - Archive File Name | Not set | Blocked/Deferred | No due date | 26 days | Unassigned |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-78](https://universalsoftware.atlassian.net/browse/TAMA-78) | update LastLoginDate column in Users table | Not set | Blocked/Deferred | No due date | 26 days | Unassigned |
| 🚨 | ValAdd Time and Attendance Mobile | [TAMA-72](https://universalsoftware.atlassian.net/browse/TAMA-72) | Create Web Portal Documentation To Share With Customers | Not set | Blocked/Deferred | No due date | 26 days | Unassigned |
| 🚨 | Sequencing | [SEQ-13](https://universalsoftware.atlassian.net/browse/SEQ-13) | Research ABP.IO | Not set | Blocked/Deferred | No due date | 18 days | Unassigned |
| 🟡 | SubZero  | [SBZ-111](https://universalsoftware.atlassian.net/browse/SBZ-111) | Cycle Ordering | Not set | Blocked/Deferred | No due date | 4 days | Tyler Meholic |
| ⚠️ | PortPro Integrations  | [POR-12](https://universalsoftware.atlassian.net/browse/POR-12) | Deploying and Testing in staging POR-9 and POR-10 | Not set | Blocked/Deferred | No due date | 12 days | Jon Taylor |
| 🚨 | PortPro Integrations  | [POR-10](https://universalsoftware.atlassian.net/browse/POR-10) | Process PTO Source Data and Update Driver 'accountHold' Status | Not set | Blocked/Deferred | No due date | 21 days | Unassigned |
| 🚨 | PortPro Integrations  | [POR-9](https://universalsoftware.atlassian.net/browse/POR-9) | Demographic Item | Not set | Blocked/Deferred | No due date | 21 days | Unassigned |
| 🚨 | GM - Factory Zero - General Tickets  | [GMFZGT-289](https://universalsoftware.atlassian.net/browse/GMFZGT-289) | Part Lookup - Part Receipts - Inconsistent Dates | Not set | Blocked/Deferred | No due date | 94 days | Andres Marcelo Garza Cantu |
| 🚨 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | Blocked/Deferred | No due date | 40 days | Adam Caldwell |
| 🚨 | GM Flint Torrey Rd | [GMFNT-72](https://universalsoftware.atlassian.net/browse/GMFNT-72) | Create Admin Master BOL Update Page | Not set | Blocked/Deferred | No due date | 110 days | David Pierce |
| 🚨 | GM - Flint Continental - General Tickets  | [GFCGT-67](https://universalsoftware.atlassian.net/browse/GFCGT-67) | No Pick Function request | Not set | Blocked/Deferred | No due date | 87 days | Unassigned |
| 🚨 | Flint - Torrey - General Tickets  | [FLIN-4](https://universalsoftware.atlassian.net/browse/FLIN-4) | Enable Image Capture Functionality for Scanner Devices | Not set | Blocked/Deferred | No due date | 67 days | Unassigned |
| 🚨 | Carta Porte | [CP-15](https://universalsoftware.atlassian.net/browse/CP-15) | Process/retain CFDI + Carta Porte doc from Digital Invoice xml response | Not set | Blocked/Deferred | No due date | 39 days | Unassigned |
| ⚠️ | Container Management System | [CMS-20](https://universalsoftware.atlassian.net/browse/CMS-20) | Verify Manifest Page | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-19](https://universalsoftware.atlassian.net/browse/CMS-19) | Import Pkg Sched | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-18](https://universalsoftware.atlassian.net/browse/CMS-18) | Print Route Manifest | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| ⚠️ | Container Management System | [CMS-17](https://universalsoftware.atlassian.net/browse/CMS-17) | ASN Lookup Page | Not set | Blocked/Deferred | No due date | 7 days | Adam Caldwell |
| 🟡 | BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | Not set | Blocked/Deferred | No due date | 5 days | Cameron Rye |
| 🟡 | BMW Spartanburg | [BMWS-139](https://universalsoftware.atlassian.net/browse/BMWS-139) | Inbound ASN Presequence screen - scanning delivery note (post-launch) | Not set | Blocked/Deferred | No due date | 4 days | Tyler Meholic |

## Overall Summary

| Status | Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|--------|---------|---------|------|----------|----------------|------------|--------|
| ⚠️ | YMS Project | [YP-37](https://universalsoftware.atlassian.net/browse/YP-37) | In Yard Status Changes | 2025-07-09 | 2 days remaining | 0.0% | In Progress |
| ⚠️ | AppDev | [AP-48](https://universalsoftware.atlassian.net/browse/AP-48) | Project Lockdown - Atlas Settlements | 2025-07-09 | 2 days remaining | 0.0% | In Progress |
| ⚠️ | YMS Project | [YP-50](https://universalsoftware.atlassian.net/browse/YP-50) | Outbound Log Requesed Changes | 2025-07-11 | 4 days remaining | 0.0% | In Progress |
| ⚠️ | YMS Project | [YP-38](https://universalsoftware.atlassian.net/browse/YP-38) | Trailer Check In Process | 2025-07-11 | 4 days remaining | 0.0% | In Progress |
| ⚠️ | YMS Project | [YP-36](https://universalsoftware.atlassian.net/browse/YP-36) | Verbiage Changes | 2025-07-11 | 4 days remaining | 33.3% | In Progress |
| ⚠️ | YMS Project | [YP-21](https://universalsoftware.atlassian.net/browse/YP-21) | Sorting By Categories | 2025-07-11 | 4 days remaining | 42.1% | In Progress |
| ⚠️ | Highway | [HIG-10](https://universalsoftware.atlassian.net/browse/HIG-10) | Implement Carrier Sync | 2025-07-11 | 4 days remaining | 50.0% | In Progress |
| ⚠️ | Highway | [HIG-7](https://universalsoftware.atlassian.net/browse/HIG-7) | Implement Carrier Full Update Endpoint | 2025-07-11 | 4 days remaining | 66.7% | In Progress |
| ⚠️ | Highway | [HIG-4](https://universalsoftware.atlassian.net/browse/HIG-4) | Implement Carrier Alerts Endpoint | 2025-07-11 | 4 days remaining | 50.0% | In Progress |
| ⚠️ | Highway | [HIG-1](https://universalsoftware.atlassian.net/browse/HIG-1) | Implement Carrier Onboarding Endpoint | 2025-07-11 | 4 days remaining | 40.0% | In Progress |
| ⚠️ | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | 4 days remaining | 15.4% | Refining |
| 🟡 | Fleet Tracker | [FLEET-7](https://universalsoftware.atlassian.net/browse/FLEET-7) | Fleet Tracker - Personnel | 2025-07-18 | 11 days remaining | 91.7% | In Progress |
| 📋 | AppDev | [AP-52](https://universalsoftware.atlassian.net/browse/AP-52) | Atlas EDI bugs | 2025-07-18 | 11 days remaining | 37.5% | In Progress |
| 📋 | Highway | [HIG-28](https://universalsoftware.atlassian.net/browse/HIG-28) | Items still needed | 2025-07-24 | 17 days remaining | 33.3% | In Progress |
| 📋 | YMS Project | [YP-46](https://universalsoftware.atlassian.net/browse/YP-46) | Yard Check Module | 2025-07-25 | 18 days remaining | 0.0% | Ready |
| 📋 | YMS Project | [YP-44](https://universalsoftware.atlassian.net/browse/YP-44) | Move Queue Module for Switchers | 2025-07-28 | 21 days remaining | 0.0% | In Progress |
| 📋 | Fleet Tracker | [FLEET-6](https://universalsoftware.atlassian.net/browse/FLEET-6) | Fleet Tracker  - Assets | 2025-07-31 | 24 days remaining | 26.7% | In Progress |
| 📋 | Control Tower | [CTP-129](https://universalsoftware.atlassian.net/browse/CTP-129) | Control Tower UI/UX enhancements and bug fixes | 2025-07-31 | 24 days remaining | 0.0% | Ready |
| 📋 | Carta Porte | [CP-8](https://universalsoftware.atlassian.net/browse/CP-8) | CFDI + Carta Porte UI (front-end) | 2025-07-31 | 24 days remaining | 66.7% | In Progress |
| 📋 | Carta Porte | [CP-1](https://universalsoftware.atlassian.net/browse/CP-1) | CFDI/Carta Porte Data Relay (back-end) | 2025-07-31 | 24 days remaining | 28.6% | In Progress |
| 📋 | PortPro Integrations  | [POR-23](https://universalsoftware.atlassian.net/browse/POR-23) | Great Plains Integrations | 2025-08-11 | 35 days remaining | N/A | Blocked/Deferred |
| 📋 | PortPro Integrations  | [POR-22](https://universalsoftware.atlassian.net/browse/POR-22) | Highway - Carriers Integrations | 2025-08-11 | 35 days remaining | N/A | Blocked/Deferred |
| 📋 | PortPro Integrations  | [POR-21](https://universalsoftware.atlassian.net/browse/POR-21) | Transman (Equipment) Integrations | 2025-08-11 | 35 days remaining | N/A | Blocked/Deferred |
| 📋 | PortPro Integrations  | [POR-20](https://universalsoftware.atlassian.net/browse/POR-20) | EFS - Fuel Integrations | 2025-08-11 | 35 days remaining | N/A | Blocked/Deferred |
| 📋 | PortPro Integrations  | [POR-3](https://universalsoftware.atlassian.net/browse/POR-3) | Workday Integration | 2025-08-11 | 35 days remaining | 55.0% | In Progress |
| 📋 | Fleet Tracker | [FLEET-57](https://universalsoftware.atlassian.net/browse/FLEET-57) | New Asset creation | 2025-08-15 | 39 days remaining | 0.0% | Refining |
| 📋 | YMS Project | [YP-61](https://universalsoftware.atlassian.net/browse/YP-61) | Switch from AWS to local for pictures and documents | 2025-08-29 | 53 days remaining | 0.0% | Ready |
| 📋 | WES | [WES-143](https://universalsoftware.atlassian.net/browse/WES-143) | Setting up deployment of WES | 2025-08-29 | 53 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-8](https://universalsoftware.atlassian.net/browse/FLEET-8) | Fleet Tracker - Contractors | 2025-08-29 | 53 days remaining | 0.0% | Refining |
| 📋 | DTNA RFID | [DR-1](https://universalsoftware.atlassian.net/browse/DR-1) | Support DTNA RFID POC | 2025-08-29 | 53 days remaining | 50.0% | Backlog |
| 📋 | Control Tower | [CTP-103](https://universalsoftware.atlassian.net/browse/CTP-103) | Adding users to various locations | 2025-08-29 | 53 days remaining | 0.0% | Refining |
| 📋 | Control Tower | [CTP-71](https://universalsoftware.atlassian.net/browse/CTP-71) | Control Tower Notes | 2025-08-29 | 53 days remaining | 0.0% | In Progress |
| 📋 | Control Tower | [CTP-37](https://universalsoftware.atlassian.net/browse/CTP-37) | Search Functionality for Orders | 2025-08-29 | 53 days remaining | N/A | Ready |
| ✅ | BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 55 days remaining | 100.0% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 55 days remaining | 27.8% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 55 days remaining | 64.7% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 55 days remaining | 7.1% | In Progress |
| 📋 | BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 55 days remaining | 50.0% | In Progress |
| 📋 | WES | [WES-7](https://universalsoftware.atlassian.net/browse/WES-7) | Migrating Configuration Bucket from YMS to WES | 2025-09-26 | 81 days remaining | 10.9% | Backlog |
| 📋 | YMS Project | [YP-106](https://universalsoftware.atlassian.net/browse/YP-106) | last movement changed to check in date and time | 2025-09-30 | 85 days remaining | N/A | Ready |
| 📋 | YMS Project | [YP-94](https://universalsoftware.atlassian.net/browse/YP-94) | Inbound Log Requested Change | 2025-09-30 | 85 days remaining | 0.0% | Ready |
| 📋 | Fleet Tracker | [FLEET-32](https://universalsoftware.atlassian.net/browse/FLEET-32) | Fleet Tracker - Facility Locations | 2025-09-30 | 85 days remaining | 33.3% | In Progress |
| 📋 | Fleet Tracker | [FLEET-15](https://universalsoftware.atlassian.net/browse/FLEET-15) | Fleet Tracker - Agents | 2025-09-30 | 85 days remaining | N/A | Refining |
| 📋 | Control Tower | [CTP-70](https://universalsoftware.atlassian.net/browse/CTP-70) | Add Contact Directory | 2025-09-30 | 85 days remaining | N/A | Refining |
| 📋 | Control Tower | [CTP-69](https://universalsoftware.atlassian.net/browse/CTP-69) | Check-call fields and late updates | 2025-09-30 | 85 days remaining | N/A | Refining |
| 📋 | Control Tower | [CTP-26](https://universalsoftware.atlassian.net/browse/CTP-26) | Customer Login Authentication | 2025-09-30 | 85 days remaining | 0.0% | Ready |
| 📋 | BMW Spartanburg | [BMWS-379](https://universalsoftware.atlassian.net/browse/BMWS-379) | Controls API | 2025-09-30 | 85 days remaining | 0.0% | Refining |
| 📋 | Fleet Tracker | [FLEET-12](https://universalsoftware.atlassian.net/browse/FLEET-12) | Fleet Tracker - Agreements | 2025-10-31 | 116 days remaining | 0.0% | Refining |
| 📋 | WES | [WES-10](https://universalsoftware.atlassian.net/browse/WES-10) | Migrating Docks from YMS to WES | 2025-11-28 | 144 days remaining | 0.0% | Backlog |
| 📋 | YMS Project | [YP-104](https://universalsoftware.atlassian.net/browse/YP-104) | Placeholder Epic for RFID YOTTA hardware | 2025-12-31 | 177 days remaining | N/A | Refining |
| 📋 | Fleet Tracker | [FLEET-13](https://universalsoftware.atlassian.net/browse/FLEET-13) | Fleet Tracker - Insurance (Cherokee - Asset + Personnel) | 2025-12-31 | 177 days remaining | 0.0% | Refining |
| 📋 | WES | [WES-13](https://universalsoftware.atlassian.net/browse/WES-13) | Migrating Container from YMS to WES | 2026-01-30 | 207 days remaining | 0.0% | Backlog |
| 📋 | WES | [WES-19](https://universalsoftware.atlassian.net/browse/WES-19) | Migrating all reports from YMS to WES | 2026-03-27 | 263 days remaining | 2.0% | Backlog |
| 📋 | WES | [WES-16](https://universalsoftware.atlassian.net/browse/WES-16) | Migrating Logs from YMS to WES | 2026-03-27 | 263 days remaining | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-114](https://universalsoftware.atlassian.net/browse/FLEET-114) | Fleet  Tracker - Cancellations: Power Units | 2026-03-31 | 267 days remaining | 0.0% | Refining |
| 📋 | Fleet Tracker | [FLEET-10](https://universalsoftware.atlassian.net/browse/FLEET-10) | Fleet Tracker - Closeouts | 2026-03-31 | 267 days remaining | 0.0% | Refining |
| 📋 | Fleet Tracker | [FLEET-9](https://universalsoftware.atlassian.net/browse/FLEET-9) | Fleet  Tracker - Cancellations: Drivers | 2026-03-31 | 267 days remaining | N/A | Refining |
| 📋 | Fleet Tracker | [FLEET-14](https://universalsoftware.atlassian.net/browse/FLEET-14) | Fleet Tracker - Litigation (and Claims) | 2026-04-06 | 273 days remaining | 0.0% | Refining |
| 📋 | WES | [WES-22](https://universalsoftware.atlassian.net/browse/WES-22) | Migrating the outbound status section from YMS to WES | 2026-04-17 | 284 days remaining | 0.0% | Backlog |
| 📋 | WES | [WES-170](https://universalsoftware.atlassian.net/browse/WES-170) | Sorting By Categories | 2026-05-22 | 319 days remaining | 0.0% | Backlog |
| 📋 | WES | [WES-270](https://universalsoftware.atlassian.net/browse/WES-270) | Outbound Log Requesed Changes | 2026-05-29 | 326 days remaining | 0.0% | Backlog |
| 📋 | YMS Project | [YP-125](https://universalsoftware.atlassian.net/browse/YP-125) | Adding Zones to system as another master | Not set | No due date | 0.0% | In Progress |
| 📋 | YMS Project | [YP-105](https://universalsoftware.atlassian.net/browse/YP-105) | Aging Report - advanced filter | Not set | No due date | 0.0% | Ready |
| 📋 | WMS 3.0 Research  | [YJ3-4](https://universalsoftware.atlassian.net/browse/YJ3-4) | Wants | Not set | No due date | 50.0% | Ready |
| 📋 | WMS 3.0 Research  | [YJ3-3](https://universalsoftware.atlassian.net/browse/YJ3-3) | WMS 3.0 - Lear Ford Steering - API Layer Calls | Not set | No due date | 25.0% | Ready |
| 🟡 | WMS 3.0 Research  | [YJ3-1](https://universalsoftware.atlassian.net/browse/YJ3-1) | WMS 3.0 - Lear Ford Steering Conversion | Not set | No due date | 90.0% | Ready |
| 📋 | Westport - WheelTire - General Tickets  | [WSWTGT-47](https://universalsoftware.atlassian.net/browse/WSWTGT-47) | Support TPMS Sensor | Not set | No due date | 0.0% | In Progress |
| 📋 | Westport - Painted Parts - General Tickets   | [WSPPGT-32](https://universalsoftware.atlassian.net/browse/WSPPGT-32) | Implement New Requested File Format | Not set | No due date | 60.0% | Refining |
| 📋 | Westport Axle Rewrite  | [WSAR-1](https://universalsoftware.atlassian.net/browse/WSAR-1) | Westport - Axle - WMS Migration | Not set | No due date | 42.9% | Refining |
| 📋 | WES | [WES-385](https://universalsoftware.atlassian.net/browse/WES-385) | Trailer Check In Process | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-384](https://universalsoftware.atlassian.net/browse/WES-384) | In Yard Status Changes | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-383](https://universalsoftware.atlassian.net/browse/WES-383) | Verbiage Changes | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-382](https://universalsoftware.atlassian.net/browse/WES-382) | tickets for research - different items | Not set | No due date | 70.0% | Backlog |
| 📋 | WES | [WES-347](https://universalsoftware.atlassian.net/browse/WES-347) | Refinement Tickets | Not set | No due date | 0.0% | Backlog |
| 📋 | WES | [WES-346](https://universalsoftware.atlassian.net/browse/WES-346) | Rack Sheet Epic | Not set | No due date | 0.0% | Backlog |
| 📋 | WMS Android Future Features | [WAFF-6](https://universalsoftware.atlassian.net/browse/WAFF-6) | Android - Icon Fixes | Not set | No due date | N/A | Refining |
| 📋 | WMS Android Future Features | [WAFF-4](https://universalsoftware.atlassian.net/browse/WAFF-4) | Preference Management - Local Endpoint Updates | Not set | No due date | N/A | Backlog |
| 📋 | Value Added  | [VAL-445](https://universalsoftware.atlassian.net/browse/VAL-445) | Change the BOL for Seats, Painted Parts, and Wheel Tire | Not set | No due date | 0.0% | In Progress |
| 📋 | Value Added  | [VAL-29](https://universalsoftware.atlassian.net/browse/VAL-29) | Redgate Source Control - Compile Site Schemas into Version Control | Not set | No due date | 0.0% | Backlog |
| 📋 | Value Added  | [VAL-9](https://universalsoftware.atlassian.net/browse/VAL-9) | General support between security and Value-Added WMS development team to patch vulnerabilities in our applications | Not set | No due date | 28.6% | Ready |
| 📋 | Value Added  | [VAL-5](https://universalsoftware.atlassian.net/browse/VAL-5) | Planning on migrating features into the master branch for global application support | Not set | No due date | 66.7% | Ready |
| 📋 | Server Migration | [SM-5](https://universalsoftware.atlassian.net/browse/SM-5) | tatlas-services | Not set | No due date | 0.0% | To Do |
| 📋 | Server Migration | [SM-4](https://universalsoftware.atlassian.net/browse/SM-4) | tatlas-w-host01 | Not set | No due date | 0.0% | To Do |
| 📋 | Sequencing | [SEQ-3](https://universalsoftware.atlassian.net/browse/SEQ-3) | Implement Sequencing Domains | Not set | No due date | 0.0% | Refining |
| 📋 | SubZero  | [SBZ-114](https://universalsoftware.atlassian.net/browse/SBZ-114) | Create Dashboards for warehouse managers | Not set | No due date | 0.0% | Refining |
| 🟡 | GM - Factory Zero - General Tickets  | [GMFZGT-8](https://universalsoftware.atlassian.net/browse/GMFZGT-8) | Efforts for cleaning up SQL and dead code found in the app | Not set | No due date | 75.0% | In Progress |
| 📋 | GM Fort Wayne XDock | [GMFWX-1](https://universalsoftware.atlassian.net/browse/GMFWX-1) | GM Ft.Wayne XDock Deconn | Not set | No due date | N/A | Blocked/Deferred |
| 📋 | FoxproRewrite | [FOX-51](https://universalsoftware.atlassian.net/browse/FOX-51) | Redesign the "Driver" Tab in FoxPro | Not set | No due date | 0.0% | To Do |
| 📋 | FoxproRewrite | [FOX-26](https://universalsoftware.atlassian.net/browse/FOX-26) |  Redesign the "Owner" Tab in FoxPro | Not set | No due date | 0.0% | To Do |
| 📋 | FoxproRewrite | [FOX-13](https://universalsoftware.atlassian.net/browse/FOX-13) | Implement Application Security for FoxPro Modernization | Not set | No due date | 0.0% | To Do |
| 📋 | FoxproRewrite | [FOX-10](https://universalsoftware.atlassian.net/browse/FOX-10) | Project Initialization for FoxPro Rewrite | Not set | No due date | 0.0% | To Do |
| 📋 | Fleet Tracker | [FLEET-106](https://universalsoftware.atlassian.net/browse/FLEET-106) | Finishing Asset stepper - rates | Not set | No due date | 0.0% | Backlog |
| 📋 | Fleet Tracker | [FLEET-99](https://universalsoftware.atlassian.net/browse/FLEET-99) | Fleet Tracker Login | Not set | No due date | N/A | Refining |
| 📋 | Fleet Tracker | [FLEET-90](https://universalsoftware.atlassian.net/browse/FLEET-90) | Imaging (placeholder) | Not set | No due date | 0.0% | Refining |
| ✅ | Fleet Tracker | [FLEET-5](https://universalsoftware.atlassian.net/browse/FLEET-5) | Setup CI/CD Pipeline (GoCD) | Not set | No due date | 100.0% | Refining |
| 📋 | FilemakerRewrite | [FIL-16](https://universalsoftware.atlassian.net/browse/FIL-16) | Create Screen/Module where Orders can be managed for Dispatch and resources assigned. | Not set | No due date | 0.0% | To Do |
| 📋 | FilemakerRewrite | [FIL-15](https://universalsoftware.atlassian.net/browse/FIL-15) | Create Screen/Module where Orders can be created | Not set | No due date | 33.3% | To Do |
| 📋 | CNH Goodfield | [CNHG-2](https://universalsoftware.atlassian.net/browse/CNHG-2) | Goodfield DMZ - Website and Reporting Service Connection | Not set | No due date | 0.0% | Refining |
| 📋 | BMW Spartanburg | [BMWS-429](https://universalsoftware.atlassian.net/browse/BMWS-429) | Shane Henson - Open Topics - 06/30 | Not set | No due date | 0.0% | Backlog |
| 📋 | AppDev | [AP-70](https://universalsoftware.atlassian.net/browse/AP-70) | MX Excel to EDI Change | Not set | No due date | 50.0% | In Progress |
| 📋 | Alliance Laundry - General Tickets  | [AL-31](https://universalsoftware.atlassian.net/browse/AL-31) | Alliance Laundry V2 Migration Cleanup | Not set | No due date | N/A | In Progress |

## Developer Summary

### Adam Caldwell

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | FCA XDock  | [FX-22](https://universalsoftware.atlassian.net/browse/FX-22) | Role Management - Fix Role Population Errors | 2025-07-07 | Validated | Due today |
| ✅ | GM ODC Launch  | [GMDCL-82](https://universalsoftware.atlassian.net/browse/GMDCL-82) | Routing Processor - Prevent Web Service Errors | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | FCA XDock  | [FX-27](https://universalsoftware.atlassian.net/browse/FX-27) | MGO Status Updater - Update All Interface Msg Records For On-Hand | 2025-07-11 | In Progress | 4 days remaining |
| 📋 | GM ODC Launch  | [GMDCL-81](https://universalsoftware.atlassian.net/browse/GMDCL-81) | MGO Status Updater - Investigating Response Message | Not set | In Progress | No due date |
| 📋 | Container Management System | [CMS-3](https://universalsoftware.atlassian.net/browse/CMS-3) | CMS UI Update - Add Loading Panels | Not set | Project on Hold (BLOCKED) | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| FCA XDock  | [FX-26](https://universalsoftware.atlassian.net/browse/FX-26) | Protect Part - Fix Cut Time Update | 2025-06-30 |
| FCA XDock  | [FX-18](https://universalsoftware.atlassian.net/browse/FX-18) | Outbound Route Management - Functionality Checks And Fixes | 2025-06-23 |
| GM ODC Launch  | [GMDCL-80](https://universalsoftware.atlassian.net/browse/GMDCL-80) | MGO Status Updater - Improve Performance | 2025-06-20 |
| Value Added  | [VAL-314](https://universalsoftware.atlassian.net/browse/VAL-314) | Outbound Process - Add LoadID To Trailer Setup And Closing | 2025-06-17 |
| FCA XDock App Rewrite  | [FCXDR-20](https://universalsoftware.atlassian.net/browse/FCXDR-20) | Rewrite CMS Pages Relying on Supplier Data Lookup | 2024-02-23 |
| Clark Street - General Tickets  | [CLAR-5](https://universalsoftware.atlassian.net/browse/CLAR-5) | Hard code change to fix start and stop times | 2023-09-15 |
| ValAdd Time and Attendance Mobile | [TAMA-4](https://universalsoftware.atlassian.net/browse/TAMA-4) | Create a process to pull login data and provide to HR-1 | 2023-07-30 |
| ValAdd Time and Attendance Mobile | [TAMA-3](https://universalsoftware.atlassian.net/browse/TAMA-3) | Create Admin Portal (WMS Platform Based) | 2023-07-23 |
| WMS Android Future Features | [WAFF-32](https://universalsoftware.atlassian.net/browse/WAFF-32) | Android Managers Component Classes | Not set |
| Value Added  | [VAL-442](https://universalsoftware.atlassian.net/browse/VAL-442) | MGO 3PL Web Services - Password Rotation 2025 | Not set |
| Value Added  | [VAL-340](https://universalsoftware.atlassian.net/browse/VAL-340) | 05/30/24 Meeting - Required Adjustments | Not set |
| Value Added  | [VAL-299](https://universalsoftware.atlassian.net/browse/VAL-299) | Setup Outbound Door - Add GM RouteID Selection | Not set |
| Value Added  | [VAL-283](https://universalsoftware.atlassian.net/browse/VAL-283) | Update Purge Mechanics in Arlington WMS | Not set |
| Value Added  | [VAL-206](https://universalsoftware.atlassian.net/browse/VAL-206) | JSON Generation - Implement Incrementing Stop Sequence | Not set |
| Value Added  | [VAL-73](https://universalsoftware.atlassian.net/browse/VAL-73) | Load Routes - Default Load Route As OB Route | Not set |
| Value Added  | [VAL-66](https://universalsoftware.atlassian.net/browse/VAL-66) | WMS File Generation | Not set |
| Value Added  | [VAL-64](https://universalsoftware.atlassian.net/browse/VAL-64) | Site Integration | Not set |
| Value Added  | [VAL-54](https://universalsoftware.atlassian.net/browse/VAL-54) | Load Routes - CRUD Management Web Page | Not set |
| Value Added  | [VAL-48](https://universalsoftware.atlassian.net/browse/VAL-48) | Smyrna - SSRS Report Server - Vulnerability Updates | Not set |
| Value Added  | [VAL-47](https://universalsoftware.atlassian.net/browse/VAL-47) | Clark Street - SSRS Report Server - Vulnerability Updates | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-92](https://universalsoftware.atlassian.net/browse/TAMA-92) | View Login Image | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-91](https://universalsoftware.atlassian.net/browse/TAMA-91) | Reset User Password | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-90](https://universalsoftware.atlassian.net/browse/TAMA-90) | Disable Users | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-89](https://universalsoftware.atlassian.net/browse/TAMA-89) | Tablet User Creation | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-88](https://universalsoftware.atlassian.net/browse/TAMA-88) | Add fields to Users page for displaying Last Clock-In and Last Clock-Out | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-85](https://universalsoftware.atlassian.net/browse/TAMA-85) | Create a report to view all current clocked in employees | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-81](https://universalsoftware.atlassian.net/browse/TAMA-81) | Output File Generator For HR-1 | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-80](https://universalsoftware.atlassian.net/browse/TAMA-80) | Add Processor And WMS App To Production Server | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-77](https://universalsoftware.atlassian.net/browse/TAMA-77) | Update references to LogInActivity table | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-76](https://universalsoftware.atlassian.net/browse/TAMA-76) | Authenticate Management Users with ASP.NET Membership | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-75](https://universalsoftware.atlassian.net/browse/TAMA-75) | Create a report to view all T&A Activity | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-74](https://universalsoftware.atlassian.net/browse/TAMA-74) | Maintain the list of Users that will use T&A Tablet | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-120](https://universalsoftware.atlassian.net/browse/RLNGTN-120) | Add the web.config and bin folders to the git ignore files | Not set |
| GM SLP WMS | [GMSLP-10](https://universalsoftware.atlassian.net/browse/GMSLP-10) | Add the web.config and bin folders to the git ignore files | Not set |
| GM Fort Wayne | [GMFWW-15](https://universalsoftware.atlassian.net/browse/GMFWW-15) | Add the web.config and bin folders to the git ignore files | Not set |
| GM ODC Launch  | [GMDCL-79](https://universalsoftware.atlassian.net/browse/GMDCL-79) | DesAdv Processor - Imported ASNs Not Attaching To Weekly Routes | Not set |
| GM ODC Launch  | [GMDCL-78](https://universalsoftware.atlassian.net/browse/GMDCL-78) | Deljit Import - Remove Forecast Data After Ship Date | Not set |
| GM ODC Launch  | [GMDCL-77](https://universalsoftware.atlassian.net/browse/GMDCL-77) | Deljit Processor - Utilize Change Code To Clear Forecasted Data | Not set |
| GM ODC Launch  | [GMDCL-76](https://universalsoftware.atlassian.net/browse/GMDCL-76) | MGO Status Report - Fix Permissions Issue | Not set |
| GM ODC Launch  | [GMDCL-75](https://universalsoftware.atlassian.net/browse/GMDCL-75) | MGO Status Updater - Restrict OH update when status is updated on OB | Not set |
| GM ODC Launch  | [GMDCL-67](https://universalsoftware.atlassian.net/browse/GMDCL-67) | Cut Time - Change Calculation To Lookup | Not set |
| GM ODC Launch  | [GMDCL-62](https://universalsoftware.atlassian.net/browse/GMDCL-62) | Integrate CreateManifest Data Collection into XDock | Not set |
| GM ODC Launch  | [GMDCL-61](https://universalsoftware.atlassian.net/browse/GMDCL-61) | Create CreateManifest Processor to Send Messages Through MGO Interface | Not set |
| GM ODC Launch  | [GMDCL-54](https://universalsoftware.atlassian.net/browse/GMDCL-54) | Setup Admin User Accounts For XDock App | Not set |
| GM ODC Launch  | [GMDCL-53](https://universalsoftware.atlassian.net/browse/GMDCL-53) | Imports - Update Stored Procedures | Not set |
| GM ODC Launch  | [GMDCL-45](https://universalsoftware.atlassian.net/browse/GMDCL-45) | GM - ODC - Outbound Route Issue | Not set |
| GM ODC Launch  | [GMDCL-44](https://universalsoftware.atlassian.net/browse/GMDCL-44) | Packaging File Import - Add Check For Null Values | Not set |
| GM ODC Launch  | [GMDCL-43](https://universalsoftware.atlassian.net/browse/GMDCL-43) | BOL Short Form Report - Temp Table Variable Lengths | Not set |
| GM ODC Launch  | [GMDCL-40](https://universalsoftware.atlassian.net/browse/GMDCL-40) | Tonnage Reports - Conversion Error | Not set |
| GM ODC Launch  | [GMDCL-39](https://universalsoftware.atlassian.net/browse/GMDCL-39) | Manual Packing Slip - Add Part Issue | Not set |
| GM ODC Launch  | [GMDCL-38](https://universalsoftware.atlassian.net/browse/GMDCL-38) | Outbound Routes - Return Trailer Status | Not set |
| GM ODC Launch  | [GMDCL-37](https://universalsoftware.atlassian.net/browse/GMDCL-37) | Printing Issue - 9/29 | Not set |
| GM ODC Launch  | [GMDCL-36](https://universalsoftware.atlassian.net/browse/GMDCL-36) | Outbound Trailers - Overweight Trailer Checks | Not set |
| GM ODC Launch  | [GMDCL-35](https://universalsoftware.atlassian.net/browse/GMDCL-35) | Verify OriginSID field from FCA is not a used field | Not set |
| GM ODC Launch  | [GMDCL-34](https://universalsoftware.atlassian.net/browse/GMDCL-34) | Cancel Part ASC - Hide Option For ODC | Not set |
| GM ODC Launch  | [GMDCL-33](https://universalsoftware.atlassian.net/browse/GMDCL-33) | ASN Importer(s) - Update Post Process Script | Not set |
| GM ODC Launch  | [GMDCL-32](https://universalsoftware.atlassian.net/browse/GMDCL-32) | Daily Routes - Add MGO Columns To Batch Edit Page | Not set |
| GM ODC Launch  | [GMDCL-31](https://universalsoftware.atlassian.net/browse/GMDCL-31) | GM - ODC - Outbound Route Management | Not set |
| GM ODC Launch  | [GMDCL-29](https://universalsoftware.atlassian.net/browse/GMDCL-29) | Site Master - Add Label Printers Page | Not set |
| GM ODC Launch  | [GMDCL-28](https://universalsoftware.atlassian.net/browse/GMDCL-28) | Imported ASNs - ASN Link To Route - Check For Duplicate ASN | Not set |
| GM ODC Launch  | [GMDCL-27](https://universalsoftware.atlassian.net/browse/GMDCL-27) | Review SSRS Reports and Update References to FCA XDock Codes To Represent GM SLP ODC | Not set |
| GM ODC Launch  | [GMDCL-26](https://universalsoftware.atlassian.net/browse/GMDCL-26) | Outbound Trailers - Force Depart Incorrect Trailers | Not set |
| GM ODC Launch  | [GMDCL-24](https://universalsoftware.atlassian.net/browse/GMDCL-24) | Docks - Remove Unused Doors From Table | Not set |
| GM ODC Launch  | [GMDCL-23](https://universalsoftware.atlassian.net/browse/GMDCL-23) | Migrate Report from Nissan SLP to GM ODC | Not set |
| GM ODC Launch  | [GMDCL-21](https://universalsoftware.atlassian.net/browse/GMDCL-21) | OB Route Management - Add Plants Not Working | Not set |
| GM ODC Launch  | [GMDCL-20](https://universalsoftware.atlassian.net/browse/GMDCL-20) | Outbound Routes - Return Trailer - EDI Processor | Not set |
| GM ODC Launch  | [GMDCL-18](https://universalsoftware.atlassian.net/browse/GMDCL-18) | Imported ASNs - Update Route Cut Time | Not set |
| GM ODC Launch  | [GMDCL-16](https://universalsoftware.atlassian.net/browse/GMDCL-16) | Add Cisco column to MasterPartList and Fix Data | Not set |
| GM ODC Launch  | [GMDCL-14](https://universalsoftware.atlassian.net/browse/GMDCL-14) | Imported ASNs - ASN Link To Route - Check For Arrived Routes | Not set |
| GM ODC Launch  | [GMDCL-13](https://universalsoftware.atlassian.net/browse/GMDCL-13) | Imported ASNs - Update Route State | Not set |
| GM ODC Launch  | [GMDCL-12](https://universalsoftware.atlassian.net/browse/GMDCL-12) | GM ODC Bulk Packaging - Update Post Process Script | Not set |
| GM ODC Launch  | [GMDCL-10](https://universalsoftware.atlassian.net/browse/GMDCL-10) | Plants - Update Table Values And References | Not set |
| GM ODC Launch  | [GMDCL-8](https://universalsoftware.atlassian.net/browse/GMDCL-8) | XDock AsnInMaster Discrepancy | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-73](https://universalsoftware.atlassian.net/browse/GFCGT-73) | Add the web.config and bin folders to the git ignore files | Not set |
| FCA XDock  | [FX-17](https://universalsoftware.atlassian.net/browse/FX-17) | OB Tonnage Reports - Use Departed Date For Data Set | Not set |
| FCA XDock  | [FX-16](https://universalsoftware.atlassian.net/browse/FX-16) | Default Transportation Mode - Set Default Value On Close | Not set |
| FCA XDock  | [FX-15](https://universalsoftware.atlassian.net/browse/FX-15) | Change available doors drop down list to display dock name instead of dock ID. | Not set |
| FCA XDock  | [FX-14](https://universalsoftware.atlassian.net/browse/FX-14) | Migration of Toluca to new server | Not set |
| FCA XDock  | [FX-13](https://universalsoftware.atlassian.net/browse/FX-13) | Door Status In - Fixing Minimum/Maximum Cube Values | Not set |
| FCA XDock  | [FX-12](https://universalsoftware.atlassian.net/browse/FX-12) | Manual Packing Slips - Add Packing Slip Status When Creating New ASN | Not set |
| FCA XDock  | [FX-11](https://universalsoftware.atlassian.net/browse/FX-11) | Inbound Scan - Inbound Trailer Check Using DockID | Not set |
| FCA XDock  | [FX-10](https://universalsoftware.atlassian.net/browse/FX-10) | Add Trailer Cube - Require Value Greater Than Zero | Not set |
| FCA XDock  | [FX-9](https://universalsoftware.atlassian.net/browse/FX-9) | Door Status In - Trailer Cube Improvements | Not set |
| FCA XDock  | [FX-8](https://universalsoftware.atlassian.net/browse/FX-8) | Edit Trailer Info - Production Diff | Not set |
| FCA XDock  | [FX-7](https://universalsoftware.atlassian.net/browse/FX-7) | Rad Filters - Add Boolean Type To Filter Options | Not set |
| FCA XDock  | [FX-6](https://universalsoftware.atlassian.net/browse/FX-6) | Inbound ASNs - Add Export Buttons To Inbound Grids | Not set |
| FCA XDock  | [FX-5](https://universalsoftware.atlassian.net/browse/FX-5) | Scanner Door Status - Adjust Trailer Availability Checks | Not set |
| FCA XDock  | [FX-4](https://universalsoftware.atlassian.net/browse/FX-4) | Daily Routes - DockName/DockID Not Displaying In Routes Grid | Not set |
| FCA XDock  | [FX-2](https://universalsoftware.atlassian.net/browse/FX-2) | SCAC Management - Add Ability To Set Default Transportation Mode | Not set |
| FCA XDock  | [FX-1](https://universalsoftware.atlassian.net/browse/FX-1) | Outbound Routes - Add Trim To New Route Names | Not set |
| FCA XDock App Rewrite  | [FCXDR-133](https://universalsoftware.atlassian.net/browse/FCXDR-133) | Inbound Scan - Task Collection Object Reference Fix | Not set |
| FCA XDock App Rewrite  | [FCXDR-130](https://universalsoftware.atlassian.net/browse/FCXDR-130) | Email Generation - Add Custom Attachments | Not set |
| FCA XDock App Rewrite  | [FCXDR-128](https://universalsoftware.atlassian.net/browse/FCXDR-128) | Trailer Not Showing Correct State | Not set |
| FCA XDock App Rewrite  | [FCXDR-127](https://universalsoftware.atlassian.net/browse/FCXDR-127) | SLP - Android | Not set |
| FCA XDock App Rewrite  | [FCXDR-126](https://universalsoftware.atlassian.net/browse/FCXDR-126) | ClarkStreet - Android | Not set |
| FCA XDock App Rewrite  | [FCXDR-125](https://universalsoftware.atlassian.net/browse/FCXDR-125) | ClarkStreet - CMS | Not set |
| FCA XDock App Rewrite  | [FCXDR-124](https://universalsoftware.atlassian.net/browse/FCXDR-124) | Smyrna - Android | Not set |
| FCA XDock App Rewrite  | [FCXDR-123](https://universalsoftware.atlassian.net/browse/FCXDR-123) | Smyrna - CMS | Not set |
| FCA XDock App Rewrite  | [FCXDR-122](https://universalsoftware.atlassian.net/browse/FCXDR-122) | Toluca - Android | Not set |
| FCA XDock App Rewrite  | [FCXDR-121](https://universalsoftware.atlassian.net/browse/FCXDR-121) | Toluca - CMS | Not set |
| FCA XDock App Rewrite  | [FCXDR-120](https://universalsoftware.atlassian.net/browse/FCXDR-120) | Saltillo - Android | Not set |
| FCA XDock App Rewrite  | [FCXDR-119](https://universalsoftware.atlassian.net/browse/FCXDR-119) | Saltillo - CMS | Not set |
| FCA XDock App Rewrite  | [FCXDR-111](https://universalsoftware.atlassian.net/browse/FCXDR-111) | Email Generation - Add Custom Attachments - Update SSRSReport Printing Service | Not set |
| FCA XDock App Rewrite  | [FCXDR-110](https://universalsoftware.atlassian.net/browse/FCXDR-110) | Email Generation - Add Custom Attachments - Create File Uploader | Not set |
| FCA XDock App Rewrite  | [FCXDR-109](https://universalsoftware.atlassian.net/browse/FCXDR-109) | Toluca | Not set |
| FCA XDock App Rewrite  | [FCXDR-108](https://universalsoftware.atlassian.net/browse/FCXDR-108) | SLP | Not set |
| FCA XDock App Rewrite  | [FCXDR-107](https://universalsoftware.atlassian.net/browse/FCXDR-107) | Smyrna | Not set |
| FCA XDock App Rewrite  | [FCXDR-106](https://universalsoftware.atlassian.net/browse/FCXDR-106) | Clark | Not set |
| FCA XDock App Rewrite  | [FCXDR-105](https://universalsoftware.atlassian.net/browse/FCXDR-105) | Saltillo | Not set |
| FCA XDock App Rewrite  | [FCXDR-104](https://universalsoftware.atlassian.net/browse/FCXDR-104) | Duplicate Scans - Investigate Duplicate Scans Causing Over Scans  | Not set |
| FCA XDock App Rewrite  | [FCXDR-103](https://universalsoftware.atlassian.net/browse/FCXDR-103) | Scan To Outbound - Add Feature to New App | Not set |
| FCA XDock App Rewrite  | [FCXDR-102](https://universalsoftware.atlassian.net/browse/FCXDR-102) | Outbound Route Management - Prevent Route Removal When Trailer Is Attached | Not set |
| FCA XDock App Rewrite  | [FCXDR-101](https://universalsoftware.atlassian.net/browse/FCXDR-101) | Plant Management - Add Plants - Populate Missing Fields | Not set |
| FCA XDock App Rewrite  | [FCXDR-100](https://universalsoftware.atlassian.net/browse/FCXDR-100) | Edit OB Trailer - Unable To Clear Trailer Values | Not set |
| FCA XDock App Rewrite  | [FCXDR-99](https://universalsoftware.atlassian.net/browse/FCXDR-99) | Daily Routes - Packing Slips Grid Changes  | Not set |
| FCA XDock App Rewrite  | [FCXDR-98](https://universalsoftware.atlassian.net/browse/FCXDR-98) | Daily Routes - Packing Slips - Visual Indicator For PS With No OB Route Available | Not set |
| FCA XDock App Rewrite  | [FCXDR-97](https://universalsoftware.atlassian.net/browse/FCXDR-97) | Report Server - Fix SSRSReport Printing Service Errors | Not set |
| FCA XDock App Rewrite  | [FCXDR-96](https://universalsoftware.atlassian.net/browse/FCXDR-96) | MasterPartsList - Manual Entry - Populate Supplier/Plant info | Not set |
| FCA XDock App Rewrite  | [FCXDR-94](https://universalsoftware.atlassian.net/browse/FCXDR-94) | Web Reports - Report Grid Ordering | Not set |
| FCA XDock App Rewrite  | [FCXDR-93](https://universalsoftware.atlassian.net/browse/FCXDR-93) | Inbound Routes - Tabbed Page To View All Inbound Routes | Not set |
| FCA XDock App Rewrite  | [FCXDR-92](https://universalsoftware.atlassian.net/browse/FCXDR-92) | Carrier Management - Grid/Page Layout | Not set |
| FCA XDock App Rewrite  | [FCXDR-91](https://universalsoftware.atlassian.net/browse/FCXDR-91) | Standard Tonnage Report - Allow Multiple Date Selection | Not set |
| FCA XDock App Rewrite  | [FCXDR-90](https://universalsoftware.atlassian.net/browse/FCXDR-90) | Outbound Trailers - Add LastScan Column to Packing Slip Grids | Not set |
| FCA XDock App Rewrite  | [FCXDR-89](https://universalsoftware.atlassian.net/browse/FCXDR-89) | Find ASN - Add Outbound Route as Filter Option | Not set |
| FCA XDock App Rewrite  | [FCXDR-88](https://universalsoftware.atlassian.net/browse/FCXDR-88) | Edit Trailer Info - Include OB Route On Page | Not set |
| FCA XDock App Rewrite  | [FCXDR-87](https://universalsoftware.atlassian.net/browse/FCXDR-87) | Daily Routes - Add OB Info To Packing Slips Grid | Not set |
| FCA XDock App Rewrite  | [FCXDR-86](https://universalsoftware.atlassian.net/browse/FCXDR-86) | Arrive Route - Page Allows Empty Arrival Time | Not set |
| FCA XDock App Rewrite  | [FCXDR-85](https://universalsoftware.atlassian.net/browse/FCXDR-85) | Inbound Door Status - Add Packing Slip Trailer | Not set |
| FCA XDock App Rewrite  | [FCXDR-84](https://universalsoftware.atlassian.net/browse/FCXDR-84) | Protect Part - Update Cut Time On Protect | Not set |
| FCA XDock App Rewrite  | [FCXDR-82](https://universalsoftware.atlassian.net/browse/FCXDR-82) | Daily Routes - Create Lookup Table For Day Codes | Not set |
| FCA XDock App Rewrite  | [FCXDR-81](https://universalsoftware.atlassian.net/browse/FCXDR-81) | Plant Management - Grid/Page Layout | Not set |
| FCA XDock App Rewrite  | [FCXDR-80](https://universalsoftware.atlassian.net/browse/FCXDR-80) | Outbound Process - Print Paperwork - Assign Pro Numbers By Destination | Not set |
| FCA XDock App Rewrite  | [FCXDR-79](https://universalsoftware.atlassian.net/browse/FCXDR-79) | ASN Status Report - Show ASN Status From Imported List | Not set |
| FCA XDock App Rewrite  | [FCXDR-78](https://universalsoftware.atlassian.net/browse/FCXDR-78) | RadGrid Batch Edit - Keyboard Navigation | Not set |
| FCA XDock App Rewrite  | [FCXDR-77](https://universalsoftware.atlassian.net/browse/FCXDR-77) | Tonnage Standard - Ignore Paging On Export | Not set |
| FCA XDock App Rewrite  | [FCXDR-76](https://universalsoftware.atlassian.net/browse/FCXDR-76) | Return To Yard - Route Dock Not Clearing | Not set |
| FCA XDock App Rewrite  | [FCXDR-75](https://universalsoftware.atlassian.net/browse/FCXDR-75) | Daily Routes - Add LastScan Column to Packing Slips Table | Not set |
| FCA XDock App Rewrite  | [FCXDR-74](https://universalsoftware.atlassian.net/browse/FCXDR-74) | Find ASNs - AsnOutMasterID Error | Not set |
| FCA XDock App Rewrite  | [FCXDR-73](https://universalsoftware.atlassian.net/browse/FCXDR-73) | Outbound Trailers - Departed Trailer Lookup | Not set |
| FCA XDock App Rewrite  | [FCXDR-72](https://universalsoftware.atlassian.net/browse/FCXDR-72) | Inbound Trailer Close - Check For Closed Trailer Before Updating | Not set |
| FCA XDock App Rewrite  | [FCXDR-71](https://universalsoftware.atlassian.net/browse/FCXDR-71) | Close OB Trailer - Check For Over Scan Part | Not set |
| FCA XDock App Rewrite  | [FCXDR-70](https://universalsoftware.atlassian.net/browse/FCXDR-70) | Packing Slips - Check Route When Creating/Assigning  | Not set |
| FCA XDock App Rewrite  | [FCXDR-69](https://universalsoftware.atlassian.net/browse/FCXDR-69) | Routing Productivity Report - Fix Where Clause | Not set |
| FCA XDock App Rewrite  | [FCXDR-68](https://universalsoftware.atlassian.net/browse/FCXDR-68) | Setup Inbound Trailer - Empty DockID | Not set |
| FCA XDock App Rewrite  | [FCXDR-67](https://universalsoftware.atlassian.net/browse/FCXDR-67) | Scan User - Add Default WarehouseID To New User | Not set |
| FCA XDock App Rewrite  | [FCXDR-66](https://universalsoftware.atlassian.net/browse/FCXDR-66) | Edit Trailer Info - Update Button Not Enabling | Not set |
| FCA XDock App Rewrite  | [FCXDR-65](https://universalsoftware.atlassian.net/browse/FCXDR-65) | Inbound Doors - Resizable Grids | Not set |
| FCA XDock App Rewrite  | [FCXDR-64](https://universalsoftware.atlassian.net/browse/FCXDR-64) | Setup Outbound Door - Trailer Already In Use Error | Not set |
| FCA XDock App Rewrite  | [FCXDR-63](https://universalsoftware.atlassian.net/browse/FCXDR-63) | Page Sizing - Expanding The Maximum Page Size | Not set |
| FCA XDock App Rewrite  | [FCXDR-62](https://universalsoftware.atlassian.net/browse/FCXDR-62) | Rights Management - Assign ASN To Route | Not set |
| FCA XDock App Rewrite  | [FCXDR-61](https://universalsoftware.atlassian.net/browse/FCXDR-61) | Inbound Doors - Add Fields To Routes Table | Not set |
| FCA XDock App Rewrite  | [FCXDR-60](https://universalsoftware.atlassian.net/browse/FCXDR-60) | Daily Routes - Packing Slips - Add DetailBOL Field To Grid | Not set |
| FCA XDock App Rewrite  | [FCXDR-59](https://universalsoftware.atlassian.net/browse/FCXDR-59) | Inbound Doors - Add PS Scan History | Not set |
| FCA XDock App Rewrite  | [FCXDR-58](https://universalsoftware.atlassian.net/browse/FCXDR-58) | EDI Generation - Remove Warehouses Join | Not set |
| FCA XDock App Rewrite  | [FCXDR-57](https://universalsoftware.atlassian.net/browse/FCXDR-57) | ASN LookUp - B Bill Manifest With Empty Supplier | Not set |
| FCA XDock App Rewrite  | [FCXDR-56](https://universalsoftware.atlassian.net/browse/FCXDR-56) | Inventory On-Hand Report - Update Location Column | Not set |
| FCA XDock App Rewrite  | [FCXDR-55](https://universalsoftware.atlassian.net/browse/FCXDR-55) | Protect Part - Set Hot Flag For InMaster Record | Not set |
| FCA XDock App Rewrite  | [FCXDR-54](https://universalsoftware.atlassian.net/browse/FCXDR-54) | Outbound Trailers - Print Paperwork - OB Manifest Info | Not set |
| FCA XDock App Rewrite  | [FCXDR-53](https://universalsoftware.atlassian.net/browse/FCXDR-53) | Manual Packing Slips - Remove DockCode Character Limit | Not set |
| FCA XDock App Rewrite  | [FCXDR-52](https://universalsoftware.atlassian.net/browse/FCXDR-52) | Outbound Trailers - Print Paperwork - Print Tail Loads | Not set |
| FCA XDock App Rewrite  | [FCXDR-51](https://universalsoftware.atlassian.net/browse/FCXDR-51) | Scan To Outbound - Implement Separate Scan To Outbound Page | Not set |
| FCA XDock App Rewrite  | [FCXDR-50](https://universalsoftware.atlassian.net/browse/FCXDR-50) | Clear DockID For Packing Slips Not On Inbound Route | Not set |
| FCA XDock App Rewrite  | [FCXDR-49](https://universalsoftware.atlassian.net/browse/FCXDR-49) | Inventory On-Hand Report - Add Default Cell Alignment on Exports | Not set |
| FCA XDock App Rewrite  | [FCXDR-48](https://universalsoftware.atlassian.net/browse/FCXDR-48) | Inventory On-Hand Report - OB SCAC Column Not Populating | Not set |
| FCA XDock App Rewrite  | [FCXDR-46](https://universalsoftware.atlassian.net/browse/FCXDR-46) | Route Management - Change Default Zone Functionality | Not set |
| FCA XDock App Rewrite  | [FCXDR-45](https://universalsoftware.atlassian.net/browse/FCXDR-45) | Reports - Signature Management - Site Specific Report Properties | Not set |
| FCA XDock App Rewrite  | [FCXDR-44](https://universalsoftware.atlassian.net/browse/FCXDR-44) | Daily Routes - Add OB Link to Parts Grid | Not set |
| FCA XDock App Rewrite  | [FCXDR-43](https://universalsoftware.atlassian.net/browse/FCXDR-43) | Find ASN - Allow Packing Slips To Be Moved | Not set |
| FCA XDock App Rewrite  | [FCXDR-42](https://universalsoftware.atlassian.net/browse/FCXDR-42) | Daily Routes - Make Packing Slip Count Visible | Not set |
| FCA XDock App Rewrite  | [FCXDR-41](https://universalsoftware.atlassian.net/browse/FCXDR-41) | Manual Packing Slips - Allow New Supplier/Plant Combinations | Not set |
| FCA XDock App Rewrite  | [FCXDR-40](https://universalsoftware.atlassian.net/browse/FCXDR-40) | Create Manifest - Supplier Code Validation | Not set |
| FCA XDock App Rewrite  | [FCXDR-39](https://universalsoftware.atlassian.net/browse/FCXDR-39) | Add New Route - Conversion Error | Not set |
| FCA XDock App Rewrite  | [FCXDR-38](https://universalsoftware.atlassian.net/browse/FCXDR-38) | Update OSD - Field Mislabeled | Not set |
| FCA XDock App Rewrite  | [FCXDR-37](https://universalsoftware.atlassian.net/browse/FCXDR-37) | Navigation - Encode All Links | Not set |
| FCA XDock App Rewrite  | [FCXDR-36](https://universalsoftware.atlassian.net/browse/FCXDR-36) | Inbound Scan - Convert to Task Collection | Not set |
| FCA XDock App Rewrite  | [FCXDR-35](https://universalsoftware.atlassian.net/browse/FCXDR-35) | Web Config - Update Authentication Form Token | Not set |
| FCA XDock App Rewrite  | [FCXDR-34](https://universalsoftware.atlassian.net/browse/FCXDR-34) | Protected Parts - UI Highlighting | Not set |
| FCA XDock App Rewrite  | [FCXDR-33](https://universalsoftware.atlassian.net/browse/FCXDR-33) | Part ASC Resubmission - Trailer Not Showing Correct State | Not set |
| FCA XDock App Rewrite  | [FCXDR-32](https://universalsoftware.atlassian.net/browse/FCXDR-32) | Daily Routes - Partial Packing Slip Removal | Not set |
| FCA XDock App Rewrite  | [FCXDR-31](https://universalsoftware.atlassian.net/browse/FCXDR-31) | Remove Tonnage Map Button from Outbound Route Management page | Not set |
| FCA XDock App Rewrite  | [FCXDR-30](https://universalsoftware.atlassian.net/browse/FCXDR-30) | Inbound Doors - Layout Improvement | Not set |
| FCA XDock App Rewrite  | [FCXDR-29](https://universalsoftware.atlassian.net/browse/FCXDR-29) | Elevated Scan Users - Temporary Elevation Not Working | Not set |
| FCA XDock App Rewrite  | [FCXDR-28](https://universalsoftware.atlassian.net/browse/FCXDR-28) | Reopen Inbound Trailer - Trailers Reopened Without Door | Not set |
| FCA XDock App Rewrite  | [FCXDR-27](https://universalsoftware.atlassian.net/browse/FCXDR-27) | Manual Packing Slips - Populate Detail Level BOL Correctly | Not set |
| FCA XDock App Rewrite  | [FCXDR-26](https://universalsoftware.atlassian.net/browse/FCXDR-26) | Printing Errors - Bad IP Address | Not set |
| FCA XDock App Rewrite  | [FCXDR-24](https://universalsoftware.atlassian.net/browse/FCXDR-24) | Date Time Picker - Change Date Format to Military Time | Not set |
| FCA XDock App Rewrite  | [FCXDR-23](https://universalsoftware.atlassian.net/browse/FCXDR-23) | ASN LookUp - Manifest Route Auto Population | Not set |
| FCA XDock App Rewrite  | [FCXDR-22](https://universalsoftware.atlassian.net/browse/FCXDR-22) | Daily Routes - Highlight Packing Slips With Protected Parts | Not set |
| FCA XDock App Rewrite  | [FCXDR-21](https://universalsoftware.atlassian.net/browse/FCXDR-21) | Android Automated Upgrade Configuration - Disable | Not set |
| FCA XDock App Rewrite  | [FCXDR-19](https://universalsoftware.atlassian.net/browse/FCXDR-19) | Inbound Doors - Close Trailer Error | Not set |
| FCA XDock App Rewrite  | [FCXDR-18](https://universalsoftware.atlassian.net/browse/FCXDR-18) | Add Manual Packing Slip - Check For Duplicate Parts | Not set |
| FCA XDock App Rewrite  | [FCXDR-17](https://universalsoftware.atlassian.net/browse/FCXDR-17) | Daily Routes Batch Edit - Packing Slips - Created By Field Not Visible | Not set |
| FCA XDock App Rewrite  | [FCXDR-16](https://universalsoftware.atlassian.net/browse/FCXDR-16) | RoleManager - Object Reference Error | Not set |
| FCA XDock App Rewrite  | [FCXDR-15](https://universalsoftware.atlassian.net/browse/FCXDR-15) | Removing Packing Slips - Resetting Values | Not set |
| FCA XDock App Rewrite  | [FCXDR-14](https://universalsoftware.atlassian.net/browse/FCXDR-14) | Outbound Process - Assign OB Pro Numbers | Not set |
| FCA XDock App Rewrite  | [FCXDR-13](https://universalsoftware.atlassian.net/browse/FCXDR-13) | Inventory On-Hand Report - Arrived Routes Not Showing | Not set |
| FCA XDock App Rewrite  | [FCXDR-12](https://universalsoftware.atlassian.net/browse/FCXDR-12) | Route Snapshot Report - Add Manually Worked Fields | Not set |
| FCA XDock App Rewrite  | [FCXDR-11](https://universalsoftware.atlassian.net/browse/FCXDR-11) | OB Route Management - Duplicate Routes | Not set |
| FCA XDock App Rewrite  | [FCXDR-10](https://universalsoftware.atlassian.net/browse/FCXDR-10) | Close OB Trailer - Trailer Number and SCAC | Not set |
| FCA XDock App Rewrite  | [FCXDR-9](https://universalsoftware.atlassian.net/browse/FCXDR-9) | Remove Part From Zone - Not Updating PS Status Correctly | Not set |
| FCA XDock App Rewrite  | [FCXDR-8](https://universalsoftware.atlassian.net/browse/FCXDR-8) | Site Master - Fix Repeated System Configuration Checks | Not set |
| FCA XDock App Rewrite  | [FCXDR-7](https://universalsoftware.atlassian.net/browse/FCXDR-7) | Add Daily Route - Stored Procedure Optimization | Not set |
| FCA XDock App Rewrite  | [FCXDR-6](https://universalsoftware.atlassian.net/browse/FCXDR-6) | Add Manual ASN - Detail Level ShipTo Value | Not set |
| FCA XDock App Rewrite  | [FCXDR-5](https://universalsoftware.atlassian.net/browse/FCXDR-5) | Packing Slip Creation - Data Validation - BOL/Packing Slip | Not set |
| FCA XDock App Rewrite  | [FCXDR-4](https://universalsoftware.atlassian.net/browse/FCXDR-4) | Scanner Activity Log - Export to Excel Bug | Not set |
| FCA XDock App Rewrite  | [FCXDR-3](https://universalsoftware.atlassian.net/browse/FCXDR-3) | Edit Outbound Route - AETC Not Updating | Not set |
| FCA XDock App Rewrite  | [FCXDR-2](https://universalsoftware.atlassian.net/browse/FCXDR-2) | Part ASC Resubmission - Update All Values | Not set |
| FCA XDock App Rewrite  | [FCXDR-1](https://universalsoftware.atlassian.net/browse/FCXDR-1) | ASC 824 Errors - ShipTo Field Different For Some Parts | Not set |
| Container Management System | [CMS-16](https://universalsoftware.atlassian.net/browse/CMS-16) | Pick List Edit - Containers Have Invalid Stop | Not set |
| Container Management System | [CMS-15](https://universalsoftware.atlassian.net/browse/CMS-15) | ASN LookUp Bill Creation - Container List Crashing | Not set |
| Container Management System | [CMS-14](https://universalsoftware.atlassian.net/browse/CMS-14) | Supplier History - Data Set Pre Filters Not Persisting | Not set |
| Container Management System | [CMS-13](https://universalsoftware.atlassian.net/browse/CMS-13) | Route Manifest Packing Slip Report - Fix State Column Size | Not set |
| Container Management System | [CMS-12](https://universalsoftware.atlassian.net/browse/CMS-12) | Updated Drop Down Lists - Minor Adjustments | Not set |
| Container Management System | [CMS-11](https://universalsoftware.atlassian.net/browse/CMS-11) | ASC Generation - Include Trailer AETC Number | Not set |
| Container Management System | [CMS-10](https://universalsoftware.atlassian.net/browse/CMS-10) | Supplier List - Implement New Supplier List | Not set |
| Container Management System | [CMS-9](https://universalsoftware.atlassian.net/browse/CMS-9) | Supplier History - Column Filters | Not set |
| Container Management System | [CMS-8](https://universalsoftware.atlassian.net/browse/CMS-8) | Edit Route Manifest - Add Loaded By Field | Not set |
| Container Management System | [CMS-7](https://universalsoftware.atlassian.net/browse/CMS-7) | Supplier History - Grid Size And Columns | Not set |
| Container Management System | [CMS-6](https://universalsoftware.atlassian.net/browse/CMS-6) | Route Manifest Packing Slip Report - Update Site Addresses | Not set |
| Container Management System | [CMS-5](https://universalsoftware.atlassian.net/browse/CMS-5) | New Plant Management Page | Not set |
| Container Management System | [CMS-4](https://universalsoftware.atlassian.net/browse/CMS-4) | Management Pages - Route Info Management | Not set |
| Clark Street - General Tickets  | [CLAR-9](https://universalsoftware.atlassian.net/browse/CLAR-9) | Open Outbound Trailers - Missing Open Trailer | Not set |
| Clark Street - General Tickets  | [CLAR-8](https://universalsoftware.atlassian.net/browse/CLAR-8) | Web Reports - Adjust Report Viewer Dimensions | Not set |
| Clark Street - General Tickets  | [CLAR-7](https://universalsoftware.atlassian.net/browse/CLAR-7) | Scanner App Error - Service Map Retrieval | Not set |
| Clark Street - General Tickets  | [CLAR-4](https://universalsoftware.atlassian.net/browse/CLAR-4) | 2860 BOL change | Not set |
| Clark Street - General Tickets  | [CLAR-3](https://universalsoftware.atlassian.net/browse/CLAR-3) | Create Shift Management Page | Not set |
| Clark Street - General Tickets  | [CLAR-1](https://universalsoftware.atlassian.net/browse/CLAR-1) | Report Change for Clark St - WMS | Not set |
| BMW Spartanburg | [BMWS-336](https://universalsoftware.atlassian.net/browse/BMWS-336) | EDI processor issue | Not set |

### Alex DeLuca

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | GM - Flint Continental - General Tickets  | [GFCGT-77](https://universalsoftware.atlassian.net/browse/GFCGT-77) | Dashboard Time Range | 2025-07-07 | Approved | Due today |
| 📋 | Warehouse Management Systerm | [WMS-18](https://universalsoftware.atlassian.net/browse/WMS-18) | Bug in dbo.GetInventoryLocations | Not set | Approved | No due date |
| 📋 | GM - Arlington - General Tickets  | [RLNGTN-127](https://universalsoftware.atlassian.net/browse/RLNGTN-127) | Error Logging, Building the error log viewer tool | Not set | Backlog | No due date |
| 📋 | GM - Arlington - General Tickets  | [RLNGTN-125](https://universalsoftware.atlassian.net/browse/RLNGTN-125) | Error Logging, set up error logging so that runtime errors can be monitored and investigated as reported.  | Not set | Approved | No due date |
| 📋 | BMW Spartanburg | [BMWS-453](https://universalsoftware.atlassian.net/browse/BMWS-453) | Migrate Error Logging from Arlington | Not set | Backlog | No due date |
| 📋 | BMW Spartanburg | [BMWS-441](https://universalsoftware.atlassian.net/browse/BMWS-441) | Update Stored Procedures Supporting the 'Dashboard' Process | Not set | Ready | No due date |
| 📋 | Alliance Laundry - General Tickets  | [AL-269](https://universalsoftware.atlassian.net/browse/AL-269) | Pick Mins to Stock | Not set | Approved | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| SubZero  | [SBZ-27](https://universalsoftware.atlassian.net/browse/SBZ-27) | Generic Data Source as a Standalone Class | 2024-12-10 |
| SubZero  | [SBZ-54](https://universalsoftware.atlassian.net/browse/SBZ-54) | Migrate GFZGT-218 to SubZero | 2024-10-10 |
| SubZero  | [SBZ-101](https://universalsoftware.atlassian.net/browse/SBZ-101) | InventoryNoPick page. removing a private variable that is not being used and is causing a runtime error.  | Not set |
| SubZero  | [SBZ-100](https://universalsoftware.atlassian.net/browse/SBZ-100) | PartLookup Page, fix paging and indexing when column's filter is active | Not set |
| SubZero  | [SBZ-99](https://universalsoftware.atlassian.net/browse/SBZ-99) | PartLookup Page, Shipped Orders Tab. Assigned Sproc to be converted to utilize Paging/Indexing | Not set |
| SubZero  | [SBZ-98](https://universalsoftware.atlassian.net/browse/SBZ-98) | PartLookup Page, Shipped Containers tab, turn on paging/indexing, and sorting for assigned stored procedure. | Not set |
| SubZero  | [SBZ-97](https://universalsoftware.atlassian.net/browse/SBZ-97) | Part Lookup Page - Convert GET Sproc for Part Receipt Tab over to accept Paging/Indexing, and Sorting | Not set |
| SubZero  | [SBZ-95](https://universalsoftware.atlassian.net/browse/SBZ-95) | Fix Performance Issue with Stored Procedure "GetOutboundTrailerHistory" | Not set |
| SubZero  | [SBZ-92](https://universalsoftware.atlassian.net/browse/SBZ-92) | Trailer History Outbound - No Trailers Bug | Not set |
| SubZero  | [SBZ-91](https://universalsoftware.atlassian.net/browse/SBZ-91) | Fix Creation Date Column on PPS No Requirements Orders Page | Not set |
| SubZero  | [SBZ-86](https://universalsoftware.atlassian.net/browse/SBZ-86) | Bug - OutboundTrailerHistory Page, fix issues related to filtering by column and address cosmetic issue with Preview Button. | Not set |
| SubZero  | [SBZ-75](https://universalsoftware.atlassian.net/browse/SBZ-75) | Migrate GFZGT-213 to Sub Zero | Not set |
| SubZero  | [SBZ-74](https://universalsoftware.atlassian.net/browse/SBZ-74) | Migrate GFZGT-191 to Sub Zero | Not set |
| SubZero  | [SBZ-73](https://universalsoftware.atlassian.net/browse/SBZ-73) | Migrate GFZGT-233 to Sub Zero | Not set |
| SubZero  | [SBZ-63](https://universalsoftware.atlassian.net/browse/SBZ-63) | Migrate GFZGT-224 to Sub Zero | Not set |
| SubZero  | [SBZ-55](https://universalsoftware.atlassian.net/browse/SBZ-55) | Migrate GFZGT-230 to Sub Zero | Not set |
| SubZero  | [SBZ-40](https://universalsoftware.atlassian.net/browse/SBZ-40) | Migrate GFZGT-222 to Sub Zero | Not set |
| SubZero  | [SBZ-39](https://universalsoftware.atlassian.net/browse/SBZ-39) | Migrate GFZGT-223 to Sub Zero | Not set |
| SubZero  | [SBZ-38](https://universalsoftware.atlassian.net/browse/SBZ-38) | Migrate GFZGT-182 | Not set |
| SubZero  | [SBZ-35](https://universalsoftware.atlassian.net/browse/SBZ-35) | TrailerHistoryOutbound Page, add filtering by Day in custom filter above grid. | Not set |
| SubZero  | [SBZ-33](https://universalsoftware.atlassian.net/browse/SBZ-33) | UniversalDataSource, add Filtering interface to make it so that the class handles filtering from the grids instead of the page's code-behind file. | Not set |
| SubZero  | [SBZ-31](https://universalsoftware.atlassian.net/browse/SBZ-31) | Part Lookup Page - Integrate Universal Data Source into page to replace legacy DataSource binding. | Not set |
| SubZero  | [SBZ-29](https://universalsoftware.atlassian.net/browse/SBZ-29) | Outbound Trailer History - Replace Drop Down Date filter with DatePicker Widget. | Not set |
| SubZero  | [SBZ-21](https://universalsoftware.atlassian.net/browse/SBZ-21) | Outbound Trailer History Page - Integrate page with Universal Data Source class to replace backend calls for NeedsDataSource events for all grids on page. | Not set |
| SubZero  | [SBZ-20](https://universalsoftware.atlassian.net/browse/SBZ-20) | Part Lookup page - Remove page index, item count footer controls from expanded sub-grid from RadGrid7 | Not set |
| SubZero  | [SBZ-19](https://universalsoftware.atlassian.net/browse/SBZ-19) | PartLookup Page - Turn Paging/Indexing, and Sorting ON For Main Grid and potentially the Part Receipts Detail Grid | Not set |
| SubZero  | [SBZ-15](https://universalsoftware.atlassian.net/browse/SBZ-15) | Migrate Updates for PartLookup page from FactoryZero to SubZero | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-119](https://universalsoftware.atlassian.net/browse/RLNGTN-119) | PartLookup page, migrate partlookup functionality over from GM FactoryZero and into GM Arlington.  | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-118](https://universalsoftware.atlassian.net/browse/RLNGTN-118) | Column Filter Validation Helper Class | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-51](https://universalsoftware.atlassian.net/browse/RLNGTN-51) | GM - Arlington - Migrate PPS No Requirements Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-291](https://universalsoftware.atlassian.net/browse/GMFZGT-291) | WMS Part Lookup Error | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-290](https://universalsoftware.atlassian.net/browse/GMFZGT-290) | Part Lookup, Main Grid, Obsolete parts are omitted from reporting.  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-287](https://universalsoftware.atlassian.net/browse/GMFZGT-287) | SSRS Report Viewer, improvement where query string parameters are parsed without generating exceptions.  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-286](https://universalsoftware.atlassian.net/browse/GMFZGT-286) | Part Lookup - Obsolete and Pre-Filter Issues | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-282](https://universalsoftware.atlassian.net/browse/GMFZGT-282) | Part Lookup page, Need to convert exporting on main grid to utilize SSRS reporting and report viewer so that the client(s) may be able to export all rows in the grid without long wait times or performance degradation  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-281](https://universalsoftware.atlassian.net/browse/GMFZGT-281) | Part Lookup page Part Receipt Tab exporting issue.  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-280](https://universalsoftware.atlassian.net/browse/GMFZGT-280) | Universal Data Source, add a component that uploads Grid Objects via json config file as opposed to hard coded on code-behind file.  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-279](https://universalsoftware.atlassian.net/browse/GMFZGT-279) | Part Lookup page, Remove feature where Grids reset sort expression on Grid load.  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-278](https://universalsoftware.atlassian.net/browse/GMFZGT-278) | Part Lookup Page, Fix sorting on all RadGrids so that the ENTIRE data set is sorted before paging/indexing and that ORDER is maintained throughout page clicks.  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-277](https://universalsoftware.atlassian.net/browse/GMFZGT-277) | Part Lookup - Generic Data Source - Editor Dialog Not Closing on Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-276](https://universalsoftware.atlassian.net/browse/GMFZGT-276) | Telerik WebUI Skins not working after WebUI Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-248](https://universalsoftware.atlassian.net/browse/GMFZGT-248) | Inventory Locations - Delete Feature Logging Mismatch | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-245](https://universalsoftware.atlassian.net/browse/GMFZGT-245) | Inventory In Locations / Inventory Locations - Delete Record Feature | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-232](https://universalsoftware.atlassian.net/browse/GMFZGT-232) | Route Column Missing - Shipped Orders/Containers - Part lookup | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-231](https://universalsoftware.atlassian.net/browse/GMFZGT-231) | PartLookup Page/ PartReceipts tab, nested grid sort order is missing for column "Received Date" | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-210](https://universalsoftware.atlassian.net/browse/GMFZGT-210) | MGO SID Response Page - Log Export CSV Bug | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-206](https://universalsoftware.atlassian.net/browse/GMFZGT-206) | Trailer Status - Move Parts Attached To Trailer | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-203](https://universalsoftware.atlassian.net/browse/GMFZGT-203) | Manage Order Priority, Verbiage change on error message | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-183](https://universalsoftware.atlassian.net/browse/GMFZGT-183) | SQL - GetPPSNoReq | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-171](https://universalsoftware.atlassian.net/browse/GMFZGT-171) | Manual Order Due Date Settings | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-162](https://universalsoftware.atlassian.net/browse/GMFZGT-162) | Inventory Location / In Location Pages Delete Method - New Model Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-105](https://universalsoftware.atlassian.net/browse/GMFZGT-105) | Printing SSRS Report | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-97](https://universalsoftware.atlassian.net/browse/GMFZGT-97) | Inbound Trailers Dashboard - Missing Data | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-90](https://universalsoftware.atlassian.net/browse/GMFZGT-90) | Add Transaction Type to Shipped Containers | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-89](https://universalsoftware.atlassian.net/browse/GMFZGT-89) | Manual Order Entry Reason Options | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-83](https://universalsoftware.atlassian.net/browse/GMFZGT-83) | Support No Stock - New Reason Code / Priority Option | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-82](https://universalsoftware.atlassian.net/browse/GMFZGT-82) | Delete Function added to Inventory in Location | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-81](https://universalsoftware.atlassian.net/browse/GMFZGT-81) | Manual Order - Due Date Settings - PPS Imported Orders Implementation | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-80](https://universalsoftware.atlassian.net/browse/GMFZGT-80) | Manual Order - Due Date Settings - Manual Order Implementation | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-78](https://universalsoftware.atlassian.net/browse/GMFZGT-78) | PPS Print Qty Implementation | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-69](https://universalsoftware.atlassian.net/browse/GMFZGT-69) | Non Conforming Parts Log | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-59](https://universalsoftware.atlassian.net/browse/GMFZGT-59) | Part Lookup - Show CLOC | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-58](https://universalsoftware.atlassian.net/browse/GMFZGT-58) | Need to create a web page to display the PPSNoRequirementsOrder Table Data | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-54](https://universalsoftware.atlassian.net/browse/GMFZGT-54) | PartLookup.aspx - On Hand Inventory - Needs Page Control | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-44](https://universalsoftware.atlassian.net/browse/GMFZGT-44) | Print PPS Report Quantity By Route Management Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-40](https://universalsoftware.atlassian.net/browse/GMFZGT-40) | Add DLOC column and populate to Order Detail | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-27](https://universalsoftware.atlassian.net/browse/GMFZGT-27) | Manual Order vs Imported Order Identification | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-14](https://universalsoftware.atlassian.net/browse/GMFZGT-14) | Inventory Details FIFO Reporting - pt 1 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-12](https://universalsoftware.atlassian.net/browse/GMFZGT-12) | Part Information Shipped Orders ReleaseMasterID Link Does Not Filter the Resulting Tab | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-71](https://universalsoftware.atlassian.net/browse/GFCGT-71) | Toggle Obsolete Parts | Not set |
| BMW Spartanburg | [BMWS-61](https://universalsoftware.atlassian.net/browse/BMWS-61) | Partlookup - Grid Filter for Partnumber Not Working | Not set |
| Alliance Laundry - General Tickets  | [AL-273](https://universalsoftware.atlassian.net/browse/AL-273) | Part Lookup page, resolve bugs with Obsolete column, Toggle Obsolete Button, and Part Number Search | Not set |
| Alliance Laundry - General Tickets  | [AL-268](https://universalsoftware.atlassian.net/browse/AL-268) | Restore Full Data Export Functionality for Part Lookup | Not set |

### Alex Gonzalez

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | Westport - Painted Parts - General Tickets   | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | 2025-06-10 | Validated | 27 days overdue |
| 📋 | BMW Spartanburg | [BMWS-439](https://universalsoftware.atlassian.net/browse/BMWS-439) | Update Stored Procedures Supporting the 'Consumption Feed' Process | Not set | Ready | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-328](https://universalsoftware.atlassian.net/browse/BMWS-328) | Create BMW Order Fulfillment By Part SSRS Report  | 2025-06-20 |
| FCA XDock  | [FX-20](https://universalsoftware.atlassian.net/browse/FX-20) | Check For Open Routes Before Deleting | 2025-06-18 |
| SubZero  | [SBZ-102](https://universalsoftware.atlassian.net/browse/SBZ-102) | SQL - OpenOrders and Dashboard - Open Order Count Does Not Align | Not set |
| Navistar San Antonio | [NVSSA-71](https://universalsoftware.atlassian.net/browse/NVSSA-71) | Receiving Report for Scanner App | Not set |
| GM Flint Torrey Rd | [GMFNT-70](https://universalsoftware.atlassian.net/browse/GMFNT-70) | Condense Display for Car Load Summary Results | Not set |
| BMW Spartanburg | [BMWS-228](https://universalsoftware.atlassian.net/browse/BMWS-228) | Display TOD data | Not set |
| BMW Spartanburg | [BMWS-216](https://universalsoftware.atlassian.net/browse/BMWS-216) | Render Empty Report Grids with Zeroed PartFamilies | Not set |
| BMW Spartanburg | [BMWS-215](https://universalsoftware.atlassian.net/browse/BMWS-215) | Suppress Non-TOD Data in Stored Procedures | Not set |
| BMW Spartanburg | [BMWS-30](https://universalsoftware.atlassian.net/browse/BMWS-30) | Implement iJIX Daily Package processing | Not set |
| BMW Spartanburg | [BMWS-1](https://universalsoftware.atlassian.net/browse/BMWS-1) | Create processor to extract BMW data feed into WMS dev DB | Not set |

### Amy DeRousha

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-07-03 | Review | 4 days overdue |
| 📋 | BMW Spartanburg | [BMWS-455](https://universalsoftware.atlassian.net/browse/BMWS-455) | changes to packout open trailer process | Not set | In Progress | No due date |
| 📋 | BMW Spartanburg | [BMWS-442](https://universalsoftware.atlassian.net/browse/BMWS-442) | Update Stored Procedures Supporting the 'Inbound ASN' Process | Not set | Ready | No due date |
| 📋 | BMW Spartanburg | [BMWS-433](https://universalsoftware.atlassian.net/browse/BMWS-433) | Enable and Validate Skipped Record Handling in Packout Process | Not set | Backlog | No due date |
| 📋 | BMW Spartanburg | [BMWS-427](https://universalsoftware.atlassian.net/browse/BMWS-427) | Support SequenceFeedId on Reorder_LineItems table | Not set | In Progress | No due date |
| 📋 | BMW Spartanburg | [BMWS-422](https://universalsoftware.atlassian.net/browse/BMWS-422) | Reorder Screen Update | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-390](https://universalsoftware.atlassian.net/browse/BMWS-390) | ASN JIS importer error | 2025-06-27 |
| BMW Spartanburg | [BMWS-396](https://universalsoftware.atlassian.net/browse/BMWS-396) | Hotfix to prevent merging of trailers that are not A-D Pillars | 2025-06-25 |
| BMW Spartanburg | [BMWS-410](https://universalsoftware.atlassian.net/browse/BMWS-410) | InventoryASNPreSeq RackID error | 2025-06-24 |
| BMW Spartanburg | [BMWS-371](https://universalsoftware.atlassian.net/browse/BMWS-371) | Outbound Trailer History - Parts attached to trailer missing | 2025-06-19 |
| BMW Spartanburg | [BMWS-385](https://universalsoftware.atlassian.net/browse/BMWS-385) | OutboundOpenClose Dead Locking | 2025-06-17 |
| BMW Spartanburg | [BMWS-378](https://universalsoftware.atlassian.net/browse/BMWS-378) | BMW - Outbound Open Close - Printing Reports | 2025-06-16 |
| BMW Spartanburg | [BMWS-366](https://universalsoftware.atlassian.net/browse/BMWS-366) | Permission Issue | 2025-06-11 |
| BMW Spartanburg | [BMWS-331](https://universalsoftware.atlassian.net/browse/BMWS-331) | Packout Template Manager changes | 2025-06-10 |
| BMW Spartanburg | [BMWS-324](https://universalsoftware.atlassian.net/browse/BMWS-324) | Modify the outbound scan app page | 2025-06-10 |
| BMW Spartanburg | [BMWS-339](https://universalsoftware.atlassian.net/browse/BMWS-339) | Enable Reprinting of BMW Sequence Starter Label on Outbound Racks Page | 2025-06-05 |
| BMW Spartanburg | [BMWS-320](https://universalsoftware.atlassian.net/browse/BMWS-320) | Differentiate Reorders and Non-Reorder Racks on Rack Master Page | 2025-06-02 |
| ValAdd Time and Attendance Mobile | [TAMA-7](https://universalsoftware.atlassian.net/browse/TAMA-7) | Tablet Application Development | 2023-07-23 |
| ValAdd Time and Attendance Mobile | [TAMA-9](https://universalsoftware.atlassian.net/browse/TAMA-9) | Setup Development Platform For Android Development | 2023-06-11 |
| WMS 3.0 Research  | [YJ3-56](https://universalsoftware.atlassian.net/browse/YJ3-56) | RESEARCH ABP framework | Not set |
| WMS 3.0 Research  | [YJ3-52](https://universalsoftware.atlassian.net/browse/YJ3-52) | RESEARCH Angular and JavaScript | Not set |
| WMS 3.0 Research  | [YJ3-35](https://universalsoftware.atlassian.net/browse/YJ3-35) | modify inventorydynamic.razor to show all existing columns for any part | Not set |
| WMS 3.0 Research  | [YJ3-30](https://universalsoftware.atlassian.net/browse/YJ3-30) | create link from part lookup page to inventory.razor | Not set |
| WMS 3.0 Research  | [YJ3-23](https://universalsoftware.atlassian.net/browse/YJ3-23) | Review ASP.NET Zero - Amy | Not set |
| WMS 3.0 Research  | [YJ3-21](https://universalsoftware.atlassian.net/browse/YJ3-21) | Research ABP.IO - Amy | Not set |
| WMS 3.0 Research  | [YJ3-17](https://universalsoftware.atlassian.net/browse/YJ3-17) | dynamic column refactor | Not set |
| WMS 3.0 Research  | [YJ3-16](https://universalsoftware.atlassian.net/browse/YJ3-16) | delete dynamic column value for part | Not set |
| WMS 3.0 Research  | [YJ3-11](https://universalsoftware.atlassian.net/browse/YJ3-11) | display column values | Not set |
| WMS 3.0 Research  | [YJ3-10](https://universalsoftware.atlassian.net/browse/YJ3-10) | edit column values | Not set |
| WMS 3.0 Research  | [YJ3-7](https://universalsoftware.atlassian.net/browse/YJ3-7) | add dynamic column with value | Not set |
| WMS 3.0 Research  | [YJ3-2](https://universalsoftware.atlassian.net/browse/YJ3-2) | Inventory Dynamic Columns | Not set |
| Warehouse Management Systerm | [WMS-17](https://universalsoftware.atlassian.net/browse/WMS-17) | Build a trailer (Trailer) | Not set |
| Warehouse Management Systerm | [WMS-15](https://universalsoftware.atlassian.net/browse/WMS-15) | Arrive the trailer on a day (TrailerInstance) | Not set |
| Warehouse Management Systerm | [WMS-11](https://universalsoftware.atlassian.net/browse/WMS-11) | Remove a packing slip from a trailer (AssignedPackingSlip) | Not set |
| Warehouse Management Systerm | [WMS-8](https://universalsoftware.atlassian.net/browse/WMS-8) | Remove items from packing slips (PackingSlipItem) | Not set |
| Warehouse Management Systerm | [WMS-7](https://universalsoftware.atlassian.net/browse/WMS-7) | Assign packing slip to trailer (AssignedPackingSlip) | Not set |
| Warehouse Management Systerm | [WMS-6](https://universalsoftware.atlassian.net/browse/WMS-6) | Create packing slips (PackingSlip) | Not set |
| Warehouse Management Systerm | [WMS-5](https://universalsoftware.atlassian.net/browse/WMS-5) | Add items to packing slips (PackingSlipItem) | Not set |
| Warehouse Management Systerm | [WMS-4](https://universalsoftware.atlassian.net/browse/WMS-4) | Depart the trailer (TrailerInstance) | Not set |
| Warehouse Management Systerm | [WMS-2](https://universalsoftware.atlassian.net/browse/WMS-2) | WMS 3.0 - Inventory, Packing Slips and Trailers | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-87](https://universalsoftware.atlassian.net/browse/TAMA-87) | Install Android Development Software | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-84](https://universalsoftware.atlassian.net/browse/TAMA-84) | Review Current Android Platform And Come Up to Speed on Design | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-83](https://universalsoftware.atlassian.net/browse/TAMA-83) | Login Verification Requirements | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-73](https://universalsoftware.atlassian.net/browse/TAMA-73) | User Login Report Missing Punch Date/Time Data | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-71](https://universalsoftware.atlassian.net/browse/TAMA-71) | fix employeeid in reporting | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-70](https://universalsoftware.atlassian.net/browse/TAMA-70) | record location activity | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-69](https://universalsoftware.atlassian.net/browse/TAMA-69) | create document for troubleshooting | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-68](https://universalsoftware.atlassian.net/browse/TAMA-68) | add PlantLocation table  | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-67](https://universalsoftware.atlassian.net/browse/TAMA-67) | merge location table to plants table | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-66](https://universalsoftware.atlassian.net/browse/TAMA-66) | fix time restraint on time punch in sp RecordTimePunchActivity | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-64](https://universalsoftware.atlassian.net/browse/TAMA-64) | error on GetLastTimePunch for new users | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-63](https://universalsoftware.atlassian.net/browse/TAMA-63) | add column for display name in PlantLocation table | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-62](https://universalsoftware.atlassian.net/browse/TAMA-62) | Split up the Punch Buttons In/Out | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-60](https://universalsoftware.atlassian.net/browse/TAMA-60) | drop unused tables | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-59](https://universalsoftware.atlassian.net/browse/TAMA-59) | fix locationid/plantid match up | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-58](https://universalsoftware.atlassian.net/browse/TAMA-58) | General Administrator Dashboard Cleanup | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-57](https://universalsoftware.atlassian.net/browse/TAMA-57) | add check for valid long/lat on AddPlant & editing on PlantManagement | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-54](https://universalsoftware.atlassian.net/browse/TAMA-54) | set camera fragment Cancelable=false | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-53](https://universalsoftware.atlassian.net/browse/TAMA-53) | add logging activity for user management | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-52](https://universalsoftware.atlassian.net/browse/TAMA-52) | update error message in PlantManagement to stay on edited item | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-50](https://universalsoftware.atlassian.net/browse/TAMA-50) | remove single address restriction from sp TA_InsertNewScannerPlant | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-48](https://universalsoftware.atlassian.net/browse/TAMA-48) | modify GetLastTimePunch to auto insert an OUT record when the last IN punch was greater than 15 hours | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-47](https://universalsoftware.atlassian.net/browse/TAMA-47) | Service - Time Query Method | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-46](https://universalsoftware.atlassian.net/browse/TAMA-46) | Desktop - Reporting | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-43](https://universalsoftware.atlassian.net/browse/TAMA-43) | update sp RecordTimePunchActivity to order the select from plants table | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-42](https://universalsoftware.atlassian.net/browse/TAMA-42) | Modify Location table | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-41](https://universalsoftware.atlassian.net/browse/TAMA-41) | Passing time to database | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-40](https://universalsoftware.atlassian.net/browse/TAMA-40) | update stored procedures to get time punch in eastern time zone | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-39](https://universalsoftware.atlassian.net/browse/TAMA-39) | rotate image in reports | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-38](https://universalsoftware.atlassian.net/browse/TAMA-38) | modify RecordTimePunchActivity to use preference 'pref_punch_threshold_seconds' when checking last punch | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-37](https://universalsoftware.atlassian.net/browse/TAMA-37) | location manager page | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-36](https://universalsoftware.atlassian.net/browse/TAMA-36) | add user info to SP: AddUserActivityLog to track who made changes | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-35](https://universalsoftware.atlassian.net/browse/TAMA-35) | add time restraint on time punch sp RecordTimePunchActivity | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-34](https://universalsoftware.atlassian.net/browse/TAMA-34) | admin role restriction - remove from default, add to UserManagement and PlantManagement | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-33](https://universalsoftware.atlassian.net/browse/TAMA-33) | login refresh issue | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-32](https://universalsoftware.atlassian.net/browse/TAMA-32) | fix filters on UserManagement.aspx | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-31](https://universalsoftware.atlassian.net/browse/TAMA-31) | add export to UserManagement.aspx | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-30](https://universalsoftware.atlassian.net/browse/TAMA-30) | exclude admin account employeid 000000 from file transfer | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-29](https://universalsoftware.atlassian.net/browse/TAMA-29) | fix shift time in ClockedInUsers | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-27](https://universalsoftware.atlassian.net/browse/TAMA-27) | add javascript to filtering so it works on enter key | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-26](https://universalsoftware.atlassian.net/browse/TAMA-26) | Setup the Desktop Application | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-25](https://universalsoftware.atlassian.net/browse/TAMA-25) | Time Punch View - Garbage Collection | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-23](https://universalsoftware.atlassian.net/browse/TAMA-23) | update file processor to exclude header row | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-22](https://universalsoftware.atlassian.net/browse/TAMA-22) | Add North Carolina Locations | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-21](https://universalsoftware.atlassian.net/browse/TAMA-21) | add plant info to ClockedInUsers and UserLoginReport | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-20](https://universalsoftware.atlassian.net/browse/TAMA-20) | add user info to Plant Management logging details | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-19](https://universalsoftware.atlassian.net/browse/TAMA-19) | TA_EditUserInfo - SQL Cleanup | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-16](https://universalsoftware.atlassian.net/browse/TAMA-16) | Setup the Android Business Service | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-15](https://universalsoftware.atlassian.net/browse/TAMA-15) | Setup the Android Application Service | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-14](https://universalsoftware.atlassian.net/browse/TAMA-14) | add Mountain Standard Time to drop downs in PlantManagement and AddPlant | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-11](https://universalsoftware.atlassian.net/browse/TAMA-11) | Change RadGrid filters to be case insensitive | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-10](https://universalsoftware.atlassian.net/browse/TAMA-10) | Change UserLoginReport.aspx to use RadFilter for searching | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-8](https://universalsoftware.atlassian.net/browse/TAMA-8) | Request Tablets For Testing | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-6](https://universalsoftware.atlassian.net/browse/TAMA-6) | Time formatting for Shift Time on ClockedINUsers.aspx page needs to cleaned up | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-5](https://universalsoftware.atlassian.net/browse/TAMA-5) | Looking to reduce the number of clicks to get at user images through the implementation of hyperlinks in RadGrids throughout entire site | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-2](https://universalsoftware.atlassian.net/browse/TAMA-2) | Need to setup a UAT instance of the HR TnA | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-1](https://universalsoftware.atlassian.net/browse/TAMA-1) | Time Zone Project | Not set |
| BMW Spartanburg | [BMWS-363](https://universalsoftware.atlassian.net/browse/BMWS-363) | WMS Outbound on JIS50  | Not set |
| BMW Spartanburg | [BMWS-335](https://universalsoftware.atlassian.net/browse/BMWS-335) | Confirm what we are printing on the rack sheet  | Not set |
| BMW Spartanburg | [BMWS-310](https://universalsoftware.atlassian.net/browse/BMWS-310) | Create a management/maintenance page to build packout template | Not set |
| BMW Spartanburg | [BMWS-309](https://universalsoftware.atlassian.net/browse/BMWS-309) | Take outbound trailer packout feature and scan outbound racks into | Not set |
| BMW Spartanburg | [BMWS-305](https://universalsoftware.atlassian.net/browse/BMWS-305) | Update page with rack status | Not set |
| BMW Spartanburg | [BMWS-304](https://universalsoftware.atlassian.net/browse/BMWS-304) | Updates to Trailer status, Trailer History and Inbound by plant | Not set |
| BMW Spartanburg | [BMWS-280](https://universalsoftware.atlassian.net/browse/BMWS-280) | Add a column to the Outbound Trailer History page to display ASN status | Not set |
| BMW Spartanburg | [BMWS-278](https://universalsoftware.atlassian.net/browse/BMWS-278) | Operators are forgetting to load racks onto the trailer | Not set |
| BMW Spartanburg | [BMWS-261](https://universalsoftware.atlassian.net/browse/BMWS-261) | reprint of the inbound merge label for inbound racks | Not set |
| BMW Spartanburg | [BMWS-254](https://universalsoftware.atlassian.net/browse/BMWS-254) | Issue with Inbound JISASN HVAC, Delivery Note 19998, Deleted ASN Information and Processed Again | Not set |
| BMW Spartanburg | [BMWS-231](https://universalsoftware.atlassian.net/browse/BMWS-231) | Adjust Android_BoxMove_PostUpdateCheck procedure | Not set |
| BMW Spartanburg | [BMWS-209](https://universalsoftware.atlassian.net/browse/BMWS-209) | Seq Web consumption files import | Not set |
| BMW Spartanburg | [BMWS-204](https://universalsoftware.atlassian.net/browse/BMWS-204) | Remove requirement for the TOD on finalizing a trailer | Not set |
| BMW Spartanburg | [BMWS-195](https://universalsoftware.atlassian.net/browse/BMWS-195) | Design a Page for Operators to Manage Outbound Racks (SequenceOrders). | Not set |
| BMW Spartanburg | [BMWS-191](https://universalsoftware.atlassian.net/browse/BMWS-191) | rebind grids after btnClicks | Not set |
| BMW Spartanburg | [BMWS-181](https://universalsoftware.atlassian.net/browse/BMWS-181) | Create script that backfills the TOD data from the DCO table | Not set |
| BMW Spartanburg | [BMWS-180](https://universalsoftware.atlassian.net/browse/BMWS-180) | Fix stored procedure Android_AddPresequencedMaterialByRack | Not set |
| BMW Spartanburg | [BMWS-179](https://universalsoftware.atlassian.net/browse/BMWS-179) | Test PreSequence data generating it from the DCO | Not set |
| BMW Spartanburg | [BMWS-163](https://universalsoftware.atlassian.net/browse/BMWS-163) | Inbound By Plant - rebind grid for exporting csv | Not set |
| BMW Spartanburg | [BMWS-162](https://universalsoftware.atlassian.net/browse/BMWS-162) | Add column to check for inbound trailers   | Not set |
| BMW Spartanburg | [BMWS-155](https://universalsoftware.atlassian.net/browse/BMWS-155) | Utilize the SequenceEventLog for receiving pre-sequence material | Not set |
| BMW Spartanburg | [BMWS-149](https://universalsoftware.atlassian.net/browse/BMWS-149) | Assign slot number from a pre-sequence-material detail record into the inventory detail record. | Not set |
| BMW Spartanburg | [BMWS-147](https://universalsoftware.atlassian.net/browse/BMWS-147) | Updates to Outbound Trailer History page | Not set |
| BMW Spartanburg | [BMWS-142](https://universalsoftware.atlassian.net/browse/BMWS-142) | Inbound By Plant -remove buttons | Not set |
| BMW Spartanburg | [BMWS-141](https://universalsoftware.atlassian.net/browse/BMWS-141) | Adjustments to the presequence material post | Not set |
| BMW Spartanburg | [BMWS-137](https://universalsoftware.atlassian.net/browse/BMWS-137) | Pre Sequence Material Detail - ManualAsnDetail Assignment issue | Not set |
| BMW Spartanburg | [BMWS-136](https://universalsoftware.atlassian.net/browse/BMWS-136) | Updates to Inbound by Plant page | Not set |
| BMW Spartanburg | [BMWS-126](https://universalsoftware.atlassian.net/browse/BMWS-126) | Inbound By Plant - BMW - Pre Sequence Material Adjustment | Not set |
| BMW Spartanburg | [BMWS-118](https://universalsoftware.atlassian.net/browse/BMWS-118) | Update the PreSequenceMaterial_BulkCopyPostProcess to support generating the Inbound Trailer record | Not set |

### Andres Marcelo Garza Cantu

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | Westport - Seats - General Tickets  | [WSSG-49](https://universalsoftware.atlassian.net/browse/WSSG-49) | Change the BOL for Seats | Not set | Validated | No due date |
| 📋 | Westport - Painted Parts - General Tickets   | [WSPPGT-29](https://universalsoftware.atlassian.net/browse/WSPPGT-29) | Inventory Status Page Performance | Not set | Validated | No due date |
| 📋 | SubZero  | [SBZ-104](https://universalsoftware.atlassian.net/browse/SBZ-104) | Update Order History Page to Show Line Items, Picks, and Date Range Selection | Not set | Code Complete | No due date |
| 📋 | DTNA CMS | [DTCMS-89](https://universalsoftware.atlassian.net/browse/DTCMS-89) | New Vendors RMS Report Saltillo Specific | Not set | Test | No due date |
| 📋 | DTNA CMS | [DTCMS-85](https://universalsoftware.atlassian.net/browse/DTCMS-85) | DTNA Account - Not Receiving All ASNs | Not set | Code Complete | No due date |
| 📋 | BMW Spartanburg | [BMWS-447](https://universalsoftware.atlassian.net/browse/BMWS-447) | Update Stored Procedures Supporting the 'Website Changes' Process | Not set | Ready | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| FCA XDock  | [FX-21](https://universalsoftware.atlassian.net/browse/FX-21) | Check For Active Destination Before Plant Removal | 2025-06-18 |
| Westport - Seats - General Tickets  | [WSSG-52](https://universalsoftware.atlassian.net/browse/WSSG-52) | ChangeBOl for Seats, on wms_iosscanapp | Not set |
| Westport - Seats - General Tickets  | [WSSG-51](https://universalsoftware.atlassian.net/browse/WSSG-51) | ChangeBOL for Seats, on wms_website | Not set |
| SubZero  | [SBZ-96](https://universalsoftware.atlassian.net/browse/SBZ-96) | SQL - CreateOrder - Unify Release Order Creation Process Between Desktop and Scanner | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-284](https://universalsoftware.atlassian.net/browse/GMFZGT-284) |  clicking on a column to filter it low-high or high-low sort order is not functioning properly. | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-270](https://universalsoftware.atlassian.net/browse/GMFZGT-270) | Enhance Search Results Display for Partlookup Filters | Not set |
| BMW Spartanburg | [BMWS-287](https://universalsoftware.atlassian.net/browse/BMWS-287) | Trailer ID on trailer level | Not set |
| BMW Spartanburg | [BMWS-46](https://universalsoftware.atlassian.net/browse/BMWS-46) | EDI Import Procedure Updates to update ImportedFileOrderNumberLinker | Not set |

### Antonio Silva

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | Review | 7 days overdue |
| 🚨 | BMW Spartanburg | [BMWS-440](https://universalsoftware.atlassian.net/browse/BMWS-440) | Update Stored Procedures Supporting the 'ASN Generation' Process | 2025-07-07 | In Progress | Due today |
| 📋 | Boeing - XDock - General Tickets  | [BOEIN-6](https://universalsoftware.atlassian.net/browse/BOEIN-6) | UpdateShipmentToDelivered_BoeingXdock Review | Not set | Code Complete | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| FCA XDock  | [FX-19](https://universalsoftware.atlassian.net/browse/FX-19) | Prevent Plant Adding To Empty Route | 2025-06-18 |
| GM - Factory Zero - General Tickets  | [GMFZGT-269](https://universalsoftware.atlassian.net/browse/GMFZGT-269) | System Error When Searching by Trailer in 'Outbound Trailer History' Page | Not set |
| DTNA CMS | [DTCMS-88](https://universalsoftware.atlassian.net/browse/DTCMS-88) | RMS - Expendable Reimbursement Report: SIDs with different dates | Not set |
| DTNA CMS | [DTCMS-82](https://universalsoftware.atlassian.net/browse/DTCMS-82) | Billing Report to pull Network Transactions Not Functioning Properly | Not set |
| BMW Spartanburg | [BMWS-288](https://universalsoftware.atlassian.net/browse/BMWS-288) | Adjust the default sorting for the InboundByPlant_BMWSpartanburg page  | Not set |
| BMW Spartanburg | [BMWS-239](https://universalsoftware.atlassian.net/browse/BMWS-239) | Need the Ability to Re-Print a Label for an Outbound Rack Sheet | Not set |
| BMW Spartanburg | [BMWS-235](https://universalsoftware.atlassian.net/browse/BMWS-235) | Prevent Finalization of Parts Without Defined Location in Inbound ASN Screen | Not set |
| BMW Spartanburg | [BMWS-123](https://universalsoftware.atlassian.net/browse/BMWS-123) | Develop IJIX Reorder Pack List System Based on Shipping and Receiving Requirements | Not set |
| BMW Spartanburg | [BMWS-121](https://universalsoftware.atlassian.net/browse/BMWS-121) | Create Pack List for IJIX Shipping and Receiving Requirements | Not set |
| AppDev | [AP-21](https://universalsoftware.atlassian.net/browse/AP-21) | Write an application that can broadcast the Scan Intent to the android app | Not set |
| Alliance Laundry - General Tickets  | [AL-199](https://universalsoftware.atlassian.net/browse/AL-199) | Min Violation Email - Better format | Not set |

### Ben Blazy

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | Control Tower | [CTP-128](https://universalsoftware.atlassian.net/browse/CTP-128) | R&D Control Tower Notes | 2025-07-04 | In Progress | 3 days overdue |
| 📋 | Control Tower | [CTP-127](https://universalsoftware.atlassian.net/browse/CTP-127) | Backend attachment controller creation | Not set | In Progress | No due date |
| 📋 | Control Tower | [CTP-125](https://universalsoftware.atlassian.net/browse/CTP-125) | Database modifications for note Attachments | Not set | Review | No due date |
| 📋 | Control Tower | [CTP-121](https://universalsoftware.atlassian.net/browse/CTP-121) | Database modifications for Control Tower Notes | Not set | Review | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Control Tower | [CTP-119](https://universalsoftware.atlassian.net/browse/CTP-119) | Dashboard Reports - Select/Unselect All | 2025-06-27 |
| AppDev | [AP-71](https://universalsoftware.atlassian.net/browse/AP-71) | DMS Document handling - Threading & Stability | 2025-06-27 |
| Control Tower | [CTP-115](https://universalsoftware.atlassian.net/browse/CTP-115) | Dashboard - Average Time in Geofence Since Last Move (BACKEND) | 2025-06-19 |
| Control Tower | [CTP-114](https://universalsoftware.atlassian.net/browse/CTP-114) | Dashboard - Inactive Asset >24 HR by Location (BACKEND) | 2025-06-19 |
| Control Tower | [CTP-113](https://universalsoftware.atlassian.net/browse/CTP-113) | Dashboard - Trailer Count by Location (BACKEND) | 2025-06-19 |
| Control Tower | [CTP-112](https://universalsoftware.atlassian.net/browse/CTP-112) | Dashboard - Average Dwell Time by Location (BACKEND) | 2025-06-19 |
| Control Tower | [CTP-116](https://universalsoftware.atlassian.net/browse/CTP-116) | New dwell table for geolocation histories | 2025-06-10 |
| Control Tower | [CTP-111](https://universalsoftware.atlassian.net/browse/CTP-111) | Geofence polygons | 2025-05-23 |
| Control Tower | [CTP-83](https://universalsoftware.atlassian.net/browse/CTP-83) | Control Tower import and insert Power Unit & Equipment data  | 2025-05-16 |
| Control Tower | [CTP-109](https://universalsoftware.atlassian.net/browse/CTP-109) | Retrieve gps data from data warehouse | 2025-05-09 |
| Control Tower | [CTP-86](https://universalsoftware.atlassian.net/browse/CTP-86) | Update database to support geofences | 2025-05-09 |
| Control Tower | [CTP-85](https://universalsoftware.atlassian.net/browse/CTP-85) | Tenancy for Control Tower | 2025-05-02 |
| Control Tower | [CTP-66](https://universalsoftware.atlassian.net/browse/CTP-66) | Normalize portal database to the task list  | 2025-05-02 |
| Control Tower | [CTP-65](https://universalsoftware.atlassian.net/browse/CTP-65) | Update project name to be Control Tower | 2025-04-18 |
| Control Tower | [CTP-36](https://universalsoftware.atlassian.net/browse/CTP-36) |  Filter Orders by Criteria | 2025-04-18 |
| MobileApp | [MA-6](https://universalsoftware.atlassian.net/browse/MA-6) | Fork AtlasMobile & push new repository | Not set |
| Control Tower | [CTP-120](https://universalsoftware.atlassian.net/browse/CTP-120) | Average Dwell bug | Not set |
| Control Tower | [CTP-118](https://universalsoftware.atlassian.net/browse/CTP-118) | Update TMWOrdersToMobile to match the current DB architecture | Not set |
| Control Tower | [CTP-60](https://universalsoftware.atlassian.net/browse/CTP-60) | Adjust pagination to not overlap the grid | Not set |
| Control Tower | [CTP-59](https://universalsoftware.atlassian.net/browse/CTP-59) | Dont show error on page load | Not set |
| Control Tower | [CTP-58](https://universalsoftware.atlassian.net/browse/CTP-58) | Remove the default page and make it the dashboard | Not set |
| Control Tower | [CTP-57](https://universalsoftware.atlassian.net/browse/CTP-57) | Merge poc ui with abp ui  | Not set |
| Control Tower | [CTP-50](https://universalsoftware.atlassian.net/browse/CTP-50) | get ABP development environment working | Not set |
| Control Tower | [CTP-47](https://universalsoftware.atlassian.net/browse/CTP-47) | Sorting - Back end | Not set |
| Control Tower | [CTP-45](https://universalsoftware.atlassian.net/browse/CTP-45) | Highlight Row that is being updated - Back End Signal R | Not set |
| Control Tower | [CTP-41](https://universalsoftware.atlassian.net/browse/CTP-41) |  Data Accuracy and Real-Time Sync | Not set |
| Control Tower | [CTP-40](https://universalsoftware.atlassian.net/browse/CTP-40) | Responsive User Interface | Not set |
| Control Tower | [CTP-39](https://universalsoftware.atlassian.net/browse/CTP-39) | Real-Time Order Status Updates | Not set |
| Control Tower | [CTP-35](https://universalsoftware.atlassian.net/browse/CTP-35) | Display Raw Order Data | Not set |
| Control Tower | [CTP-15](https://universalsoftware.atlassian.net/browse/CTP-15) | Create Dispatch domain repository | Not set |
| Control Tower | [CTP-14](https://universalsoftware.atlassian.net/browse/CTP-14) | Create Universal Portal front-end project and push to git | Not set |

### Cameron Rye

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | DTNA CMS | [DTCMS-92](https://universalsoftware.atlassian.net/browse/DTCMS-92) | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | Not set | Review | No due date |
| 📋 | BMW Spartanburg | [BMWS-443](https://universalsoftware.atlassian.net/browse/BMWS-443) | Update Stored Procedures Supporting the 'Label Printing' Process | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-27 |
| BMW Spartanburg | [BMWS-229](https://universalsoftware.atlassian.net/browse/BMWS-229) | Add editor feature to Outbound Rack management page | 2025-06-16 |
| BMW Spartanburg | [BMWS-226](https://universalsoftware.atlassian.net/browse/BMWS-226) | Fix the Order Management page | 2025-06-16 |
| WMS 3.0 Research  | [YJ3-55](https://universalsoftware.atlassian.net/browse/YJ3-55) | RESEARCH ABP topics | Not set |
| WMS 3.0 Research  | [YJ3-51](https://universalsoftware.atlassian.net/browse/YJ3-51) | REASEARCH - convert ABP.IO pages to Angular | Not set |
| BMW Spartanburg | [BMWS-319](https://universalsoftware.atlassian.net/browse/BMWS-319) | ASN quantity not showing correctly | Not set |
| BMW Spartanburg | [BMWS-257](https://universalsoftware.atlassian.net/browse/BMWS-257) | Add missing columns to TrailerHistoryOutbound grids | Not set |
| BMW Spartanburg | [BMWS-222](https://universalsoftware.atlassian.net/browse/BMWS-222) | Issue with refresh grid for Outbound Rack page | Not set |
| BMW Spartanburg | [BMWS-200](https://universalsoftware.atlassian.net/browse/BMWS-200) | Create page to display BMWSequence.OutboundRackMaster data | Not set |
| BMW Spartanburg | [BMWS-169](https://universalsoftware.atlassian.net/browse/BMWS-169) | Fix Job Management page's export to csv feature | Not set |
| BMW Spartanburg | [BMWS-164](https://universalsoftware.atlassian.net/browse/BMWS-164) | Create Job Management page | Not set |
| BMW Spartanburg | [BMWS-156](https://universalsoftware.atlassian.net/browse/BMWS-156) | Utilize the SequenceEventLog for Box Moving pre-sequence material | Not set |
| BMW Spartanburg | [BMWS-148](https://universalsoftware.atlassian.net/browse/BMWS-148) | Update to Part Lookup page | Not set |
| BMW Spartanburg | [BMWS-117](https://universalsoftware.atlassian.net/browse/BMWS-117) | When AddManualASNtoTrailer_BMWSpartanburg is called, stop updating the Trailer information of the ManualAsnMaster. | Not set |
| BMW Spartanburg | [BMWS-116](https://universalsoftware.atlassian.net/browse/BMWS-116) | Remove Add ASN from the menu to avoid creating bad asn records | Not set |
| BMW Spartanburg | [BMWS-115](https://universalsoftware.atlassian.net/browse/BMWS-115) | Remove the drop down list for trailers for Inbound by Plant BMW | Not set |
| BMW Spartanburg | [BMWS-113](https://universalsoftware.atlassian.net/browse/BMWS-113) | Stop showing bad records on the inbound by plant page | Not set |
| BMW Spartanburg | [BMWS-76](https://universalsoftware.atlassian.net/browse/BMWS-76) | Part Lookup Improvements | Not set |

### Charles Fritz

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | YMS Project | [YP-113](https://universalsoftware.atlassian.net/browse/YP-113) | QA Testing of YP-20: Trailer Check In Process | Not set | Ready | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| YMS Project | [YP-86](https://universalsoftware.atlassian.net/browse/YP-86) | QA Testing of YP-59: Sorting By Categories - Trailer - Dock Section | 2025-07-11 |
| YMS Project | [YP-77](https://universalsoftware.atlassian.net/browse/YP-77) | QA Testing of YP-32: Sorting By Categories - Carrier Report | 2025-07-11 |
| YMS Project | [YP-75](https://universalsoftware.atlassian.net/browse/YP-75) | QA Testing of YP-31: Sorting By Categories - Daily Report | 2025-07-11 |
| YMS Project | [YP-71](https://universalsoftware.atlassian.net/browse/YP-71) | QA Testing of YP-29: Sorting By Categories - Outbound Log | 2025-07-11 |
| YMS Project | [YP-68](https://universalsoftware.atlassian.net/browse/YP-68) | QA Testing of YP-28: Sorting By Categories - Inbound Log | 2025-07-11 |
| YMS Project | [YP-84](https://universalsoftware.atlassian.net/browse/YP-84) | QA Testing of YP-35: Sorting By Categories - Outbound Status Report | Not set |
| YMS Project | [YP-82](https://universalsoftware.atlassian.net/browse/YP-82) | QA Unit  Testing of YP-34: Sorting By Categories - Outbound Status | Not set |
| YMS Project | [YP-79](https://universalsoftware.atlassian.net/browse/YP-79) | QA Testing of YP-33: Sorting By Categories - Aging Report | Not set |
| YMS Project | [YP-73](https://universalsoftware.atlassian.net/browse/YP-73) | QA Testing of YP-30: Sorting By Categories - Trailer Search Report | Not set |
| YMS Project | [YP-66](https://universalsoftware.atlassian.net/browse/YP-66) | QA Testing of YP-27: Sorting By Categories - Trailer - Yard Section | Not set |

### Chris Collins

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | Boeing Mesa - General Tickets  | [BN-122](https://universalsoftware.atlassian.net/browse/BN-122) | Blind Audit Manager Override Feature MESA Boeing  | 2025-07-16 | In Progress | 9 days remaining |
| ✅ | Boeing Mesa - General Tickets  | [BN-38](https://universalsoftware.atlassian.net/browse/BN-38) | Kitted Picked Orders Blind Audit Boeing Mesa UAT | 2025-07-16 | In Progress | 9 days remaining |
| ✅ | Boeing Mesa - General Tickets  | [BN-20](https://universalsoftware.atlassian.net/browse/BN-20) | Boeing Mesa - Outbound - Blind BOM item count | 2025-07-16 | Rework | 9 days remaining |
| 📋 | Boeing - XDock - General Tickets  | [BOEIN-10](https://universalsoftware.atlassian.net/browse/BOEIN-10) | TMS Communication | Not set | Packaged | No due date |
| 📋 | Boeing Mesa - General Tickets  | [BN-135](https://universalsoftware.atlassian.net/browse/BN-135) | Add Option (Paper Work Hold) to Select Grief Status (Mesa Boeing) | Not set | Backlog | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Boeing Mesa - General Tickets  | [BN-134](https://universalsoftware.atlassian.net/browse/BN-134) | Merged PO's on Pack Slip | 2025-06-04 |
| Boeing - XDock - General Tickets  | [BOEIN-38](https://universalsoftware.atlassian.net/browse/BOEIN-38) | WMS Issue - Pallet Scan Function Not Displaying Shipment Information | 2025-05-14 |
| Boeing Mesa - General Tickets  | [BN-123](https://universalsoftware.atlassian.net/browse/BN-123) | WMS Boeing Mesa Production Change Request (MILS Serial Numbers) | 2025-05-07 |
| Boeing Mesa - General Tickets  | [BN-132](https://universalsoftware.atlassian.net/browse/BN-132) | Boeing Mesa - Receiving Page (WMS Website) In-Transit ShipmentQty Screen (Submit Locked) | 2025-05-06 |
| Boeing Mesa - General Tickets  | [BN-131](https://universalsoftware.atlassian.net/browse/BN-131) | Repack Label Missing the "U" Prefix on Receiver No. (Boeing Mesa Production) | 2025-05-02 |
| Boeing Mesa - General Tickets  | [BN-129](https://universalsoftware.atlassian.net/browse/BN-129) | Pick Request Import Process - Line Item Merge Bug | 2025-05-02 |
| Boeing Mesa - General Tickets  | [BN-94](https://universalsoftware.atlassian.net/browse/BN-94) | Boeing Mesa - Grief History And Resolution - SCACCode ASN retrieval column removal | 2024-04-02 |
| Boeing Mesa - General Tickets  | [BN-114](https://universalsoftware.atlassian.net/browse/BN-114) | Boeing Mesa - PO Auth Importer Serials Check | 2023-11-30 |
| Boeing Mesa - General Tickets  | [BN-97](https://universalsoftware.atlassian.net/browse/BN-97) | Boeing Mesa - Gold Pick Request Duplicates (Artifacts) | 2023-11-01 |
| Boeing Mesa - General Tickets  | [BN-34](https://universalsoftware.atlassian.net/browse/BN-34) | Add Ship/Aircraft Number to outbound license plate | 2023-10-27 |
| Boeing Mesa - General Tickets  | [BN-15](https://universalsoftware.atlassian.net/browse/BN-15) | Boeing Mesa - FOB_DATE for GOLDMSA receipts (Priority 1) | 2023-10-20 |
| Boeing Mesa - General Tickets  | [BN-116](https://universalsoftware.atlassian.net/browse/BN-116) | Boeing Mesa - Receiving Process - Serial Unique Check | 2023-10-12 |
| Value Added  | [VAL-296](https://universalsoftware.atlassian.net/browse/VAL-296) | Update Purge Mechanics in Boeing Mesa | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-260](https://universalsoftware.atlassian.net/browse/GMFZGT-260) | Obsolete Column is not updating from the import process | Not set |
| DTNA CMS | [DTCMS-57](https://universalsoftware.atlassian.net/browse/DTCMS-57) | File Mover (Billing) | Not set |
| DTNA CMS | [DTCMS-56](https://universalsoftware.atlassian.net/browse/DTCMS-56) | File Mover (Transactions) | Not set |
| DTNA CMS | [DTCMS-52](https://universalsoftware.atlassian.net/browse/DTCMS-52) | Desktop | Not set |
| DTNA CMS | [DTCMS-48](https://universalsoftware.atlassian.net/browse/DTCMS-48) | Generate Compliance Processor | Not set |
| DTNA CMS | [DTCMS-46](https://universalsoftware.atlassian.net/browse/DTCMS-46) | Generate Inventory Processor | Not set |
| DTNA CMS | [DTCMS-45](https://universalsoftware.atlassian.net/browse/DTCMS-45) | Generate Shipment Processor | Not set |
| DTNA CMS | [DTCMS-41](https://universalsoftware.atlassian.net/browse/DTCMS-41) | ProcessShipment | Not set |
| DTNA CMS | [DTCMS-40](https://universalsoftware.atlassian.net/browse/DTCMS-40) | Webservice | Not set |
| DTNA CMS | [DTCMS-39](https://universalsoftware.atlassian.net/browse/DTCMS-39) | ProcessSuppliers | Not set |
| DTNA CMS | [DTCMS-34](https://universalsoftware.atlassian.net/browse/DTCMS-34) | ProcessASN | Not set |
| DTNA CMS | [DTCMS-33](https://universalsoftware.atlassian.net/browse/DTCMS-33) | ProcessCatalog | Not set |
| DTNA CMS | [DTCMS-32](https://universalsoftware.atlassian.net/browse/DTCMS-32) | ExpendableRequestNotifier | Not set |
| DTNA CMS | [DTCMS-31](https://universalsoftware.atlassian.net/browse/DTCMS-31) | ProcessForecast | Not set |
| DTNA CMS | [DTCMS-30](https://universalsoftware.atlassian.net/browse/DTCMS-30) | ProcessPEFP | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-29](https://universalsoftware.atlassian.net/browse/BOEIN-29) | CMZ BizService | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-28](https://universalsoftware.atlassian.net/browse/BOEIN-28) | CMS AppService | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-24](https://universalsoftware.atlassian.net/browse/BOEIN-24) | Desktop - UAT | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-22](https://universalsoftware.atlassian.net/browse/BOEIN-22) | File Mover | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-21](https://universalsoftware.atlassian.net/browse/BOEIN-21) | Business Webservice | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-20](https://universalsoftware.atlassian.net/browse/BOEIN-20) | Desktop | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-19](https://universalsoftware.atlassian.net/browse/BOEIN-19) | File Importer | Not set |
| Boeing Portland - General Tickets  | [BNG-17](https://universalsoftware.atlassian.net/browse/BNG-17) | Desktop | Not set |
| Boeing Portland - General Tickets  | [BNG-16](https://universalsoftware.atlassian.net/browse/BNG-16) | RFID Biz Service | Not set |
| Boeing Portland - General Tickets  | [BNG-14](https://universalsoftware.atlassian.net/browse/BNG-14) | RFID App Service | Not set |
| Boeing Portland - General Tickets  | [BNG-12](https://universalsoftware.atlassian.net/browse/BNG-12) | Spares Desktop | Not set |
| Boeing Portland - General Tickets  | [BNG-10](https://universalsoftware.atlassian.net/browse/BNG-10) | Webservice | Not set |
| Boeing Mesa - General Tickets  | [BN-130](https://universalsoftware.atlassian.net/browse/BN-130) | Disable Automated Report (GetReport SerializedInventory.xlsx) Boeing mesa | Not set |
| Boeing Mesa - General Tickets  | [BN-128](https://universalsoftware.atlassian.net/browse/BN-128) | WMS Site not working on HoneyWell Scanner in BoeingMesa | Not set |
| Boeing Mesa - General Tickets  | [BN-124](https://universalsoftware.atlassian.net/browse/BN-124) | WMS Receiving Report Reciept # to have the U in front of the numbers | Not set |
| Boeing Mesa - General Tickets  | [BN-120](https://universalsoftware.atlassian.net/browse/BN-120) | Mesa_ItemMasterImporter - Exception (02/21-02/23) | Not set |
| Boeing Mesa - General Tickets  | [BN-118](https://universalsoftware.atlassian.net/browse/BN-118) | Characters in Ares WMS Boeing Mesa (Reference Ticket#293778)  | Not set |
| Boeing Mesa - General Tickets  | [BN-117](https://universalsoftware.atlassian.net/browse/BN-117) | Boeing Mesa - MTR Number Missing from Receiving Labels | Not set |
| Boeing Mesa - General Tickets  | [BN-115](https://universalsoftware.atlassian.net/browse/BN-115) | Boeing Mesa - Desktop - Dashboard Widgets | Not set |
| Boeing Mesa - General Tickets  | [BN-113](https://universalsoftware.atlassian.net/browse/BN-113) | Boeing Mesa - Reporting - Grief History and Resolutions Report - Resolution Delta Datetime Issue | Not set |
| Boeing Mesa - General Tickets  | [BN-112](https://universalsoftware.atlassian.net/browse/BN-112) | Boeing Mesa - InventoryDetails Record, ZERO ContainerQty | Not set |
| Boeing Mesa - General Tickets  | [BN-111](https://universalsoftware.atlassian.net/browse/BN-111) | Boeing Mesa UAT - Biz Service Pref Key not Updated | Not set |
| Boeing Mesa - General Tickets  | [BN-110](https://universalsoftware.atlassian.net/browse/BN-110) | Boeing Mesa - Gold PO Voucher Auto Grief Issue | Not set |
| Boeing Mesa - General Tickets  | [BN-109](https://universalsoftware.atlassian.net/browse/BN-109) | Boeing Mesa - Order 0009292906 Manifest | Not set |
| Boeing Mesa - General Tickets  | [BN-108](https://universalsoftware.atlassian.net/browse/BN-108) | Boeing Mesa - Receiving Process - Serial Number Character Limit Adjustment | Not set |
| Boeing Mesa - General Tickets  | [BN-107](https://universalsoftware.atlassian.net/browse/BN-107) | Boeing Mesa - Receiving Timestamps Report Delta | Not set |
| Boeing Mesa - General Tickets  | [BN-106](https://universalsoftware.atlassian.net/browse/BN-106) | Boeing Mesa - UAT - Inspection Flag Verification | Not set |
| Boeing Mesa - General Tickets  | [BN-105](https://universalsoftware.atlassian.net/browse/BN-105) | Duplicate Packing Slip # | Not set |
| Boeing Mesa - General Tickets  | [BN-103](https://universalsoftware.atlassian.net/browse/BN-103) | Boeing Mesa - Carrier Possession Report Issue | Not set |
| Boeing Mesa - General Tickets  | [BN-102](https://universalsoftware.atlassian.net/browse/BN-102) | Boeing Mesa - Aircraft Number Missing on Kitting Picked Orders page | Not set |
| Boeing Mesa - General Tickets  | [BN-101](https://universalsoftware.atlassian.net/browse/BN-101) | Boeing Mesa - Receiving Process - Part Number/Serial Unique | Not set |
| Boeing Mesa - General Tickets  | [BN-100](https://universalsoftware.atlassian.net/browse/BN-100) | WMS Receiving Report Barcode (Boeing MESA) | Not set |
| Boeing Mesa - General Tickets  | [BN-99](https://universalsoftware.atlassian.net/browse/BN-99) | Boeing Mesa - Pick Request Item Master Issue (Part_Type) | Not set |
| Boeing Mesa - General Tickets  | [BN-96](https://universalsoftware.atlassian.net/browse/BN-96) | Boeing Mesa - Receiving - Line item added to Old PO Auth | Not set |
| Boeing Mesa - General Tickets  | [BN-93](https://universalsoftware.atlassian.net/browse/BN-93) | Boeing Mesa - Label Printer Timeout Issue | Not set |
| Boeing Mesa - General Tickets  | [BN-92](https://universalsoftware.atlassian.net/browse/BN-92) | Non Net Inventory Report | Not set |
| Boeing Mesa - General Tickets  | [BN-89](https://universalsoftware.atlassian.net/browse/BN-89) | Mesa In Transit Import Exception - Thursday 5:03 PM  | Not set |
| Boeing Mesa - General Tickets  | [BN-88](https://universalsoftware.atlassian.net/browse/BN-88) | Boeing Mesa - "Order Priority" Role - Adding right "Toggle Order Priority" | Not set |
| Boeing Mesa - General Tickets  | [BN-87](https://universalsoftware.atlassian.net/browse/BN-87) | Shelf Life Report \| Default End Date Adjustment (No Data) | Not set |
| Boeing Mesa - General Tickets  | [BN-86](https://universalsoftware.atlassian.net/browse/BN-86) | Boeing Mesa - New UAT Account - Luke Call | Not set |
| Boeing Mesa - General Tickets  | [BN-83](https://universalsoftware.atlassian.net/browse/BN-83) | Re-ID and Receiving Inspection Receiving Reports | Not set |
| Boeing Mesa - General Tickets  | [BN-81](https://universalsoftware.atlassian.net/browse/BN-81) | Boeing Mesa - Print Spooler Repeated Prints  | Not set |
| Boeing Mesa - General Tickets  | [BN-79](https://universalsoftware.atlassian.net/browse/BN-79) | Grief Initiation UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-78](https://universalsoftware.atlassian.net/browse/BN-78) | Grief Resolution | Not set |
| Boeing Mesa - General Tickets  | [BN-77](https://universalsoftware.atlassian.net/browse/BN-77) | Grief Initiation | Not set |
| Boeing Mesa - General Tickets  | [BN-76](https://universalsoftware.atlassian.net/browse/BN-76) | Receipt Generator | Not set |
| Boeing Mesa - General Tickets  | [BN-75](https://universalsoftware.atlassian.net/browse/BN-75) | Hot Log  | Not set |
| Boeing Mesa - General Tickets  | [BN-74](https://universalsoftware.atlassian.net/browse/BN-74) | Hot Log UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-73](https://universalsoftware.atlassian.net/browse/BN-73) | Receipt Generator UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-72](https://universalsoftware.atlassian.net/browse/BN-72) | Boeing Mesa - RE-ID Fields and Reports - New In-Transit Field | Not set |
| Boeing Mesa - General Tickets  | [BN-71](https://universalsoftware.atlassian.net/browse/BN-71) | Grief Resolution UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-70](https://universalsoftware.atlassian.net/browse/BN-70) | Boeing Mesa UAT - Restore Database | Not set |
| Boeing Mesa - General Tickets  | [BN-69](https://universalsoftware.atlassian.net/browse/BN-69) | ASNImporter | Not set |
| Boeing Mesa - General Tickets  | [BN-68](https://universalsoftware.atlassian.net/browse/BN-68) | Filemover UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-67](https://universalsoftware.atlassian.net/browse/BN-67) | Pick Request UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-66](https://universalsoftware.atlassian.net/browse/BN-66) | Boeing Mesa - Label Print Spooler Program Exit | Not set |
| Boeing Mesa - General Tickets  | [BN-65](https://universalsoftware.atlassian.net/browse/BN-65) | Grief Completion UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-64](https://universalsoftware.atlassian.net/browse/BN-64) | Boeing Mesa - Android - Pick Sorting Logic Improvement for Electrical Kits | Not set |
| Boeing Mesa - General Tickets  | [BN-63](https://universalsoftware.atlassian.net/browse/BN-63) | ASNImporter_UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-62](https://universalsoftware.atlassian.net/browse/BN-62) | Filemover | Not set |
| Boeing Mesa - General Tickets  | [BN-61](https://universalsoftware.atlassian.net/browse/BN-61) | PTA Tracker UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-60](https://universalsoftware.atlassian.net/browse/BN-60) | Grief Completion | Not set |
| Boeing Mesa - General Tickets  | [BN-59](https://universalsoftware.atlassian.net/browse/BN-59) | PTA Tracking Inbound UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-58](https://universalsoftware.atlassian.net/browse/BN-58) | PTA Tracker | Not set |
| Boeing Mesa - General Tickets  | [BN-57](https://universalsoftware.atlassian.net/browse/BN-57) | Boeing Mesa - New UAT Account Alec Nelson  | Not set |
| Boeing Mesa - General Tickets  | [BN-56](https://universalsoftware.atlassian.net/browse/BN-56) | Correcting NextCycleCountDate fields in Production | Not set |
| Boeing Mesa - General Tickets  | [BN-55](https://universalsoftware.atlassian.net/browse/BN-55) | Reporting Service Configuration Manager (Mesa DB Server) | Not set |
| Boeing Mesa - General Tickets  | [BN-54](https://universalsoftware.atlassian.net/browse/BN-54) | Pick Confirm | Not set |
| Boeing Mesa - General Tickets  | [BN-53](https://universalsoftware.atlassian.net/browse/BN-53) | PO Auth | Not set |
| Boeing Mesa - General Tickets  | [BN-52](https://universalsoftware.atlassian.net/browse/BN-52) | PO Auth UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-51](https://universalsoftware.atlassian.net/browse/BN-51) | Boeing Mesa - Remove Page 2 from Grief Report | Not set |
| Boeing Mesa - General Tickets  | [BN-50](https://universalsoftware.atlassian.net/browse/BN-50) | Pick Confirm UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-49](https://universalsoftware.atlassian.net/browse/BN-49) | Boeing Mesa - WMS Receipts Transaction | Not set |
| Boeing Mesa - General Tickets  | [BN-48](https://universalsoftware.atlassian.net/browse/BN-48) | Pick Request | Not set |
| Boeing Mesa - General Tickets  | [BN-47](https://universalsoftware.atlassian.net/browse/BN-47) | Boeing Mesa - PO Receipts Page | Not set |
| Boeing Mesa - General Tickets  | [BN-46](https://universalsoftware.atlassian.net/browse/BN-46) | Boeing Mesa - Receiving Process (Modal) Supplier Date Check | Not set |
| Boeing Mesa - General Tickets  | [BN-45](https://universalsoftware.atlassian.net/browse/BN-45) | Boeing Mesa Database Indexes | Not set |
| Boeing Mesa - General Tickets  | [BN-44](https://universalsoftware.atlassian.net/browse/BN-44) | App Service | Not set |
| Boeing Mesa - General Tickets  | [BN-43](https://universalsoftware.atlassian.net/browse/BN-43) | UAT Desktop Application | Not set |
| Boeing Mesa - General Tickets  | [BN-42](https://universalsoftware.atlassian.net/browse/BN-42) | UAT Business Service | Not set |
| Boeing Mesa - General Tickets  | [BN-41](https://universalsoftware.atlassian.net/browse/BN-41) | UAT App Service | Not set |
| Boeing Mesa - General Tickets  | [BN-40](https://universalsoftware.atlassian.net/browse/BN-40) | Item Master | Not set |
| Boeing Mesa - General Tickets  | [BN-39](https://universalsoftware.atlassian.net/browse/BN-39) | Item Master UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-37](https://universalsoftware.atlassian.net/browse/BN-37) | Receiving Time Stamp Report Duplicates (Boeing Mesa) | Not set |
| Boeing Mesa - General Tickets  | [BN-36](https://universalsoftware.atlassian.net/browse/BN-36) | Mesa In Transit Import Exception - Friday 1:33pm | Not set |
| Boeing Mesa - General Tickets  | [BN-35](https://universalsoftware.atlassian.net/browse/BN-35) | Mesa In Transit Import Exception - Friday 2:18pm | Not set |
| Boeing Mesa - General Tickets  | [BN-33](https://universalsoftware.atlassian.net/browse/BN-33) | Boeing Mesa - WMS Manifest Line (Deletion) | Not set |
| Boeing Mesa - General Tickets  | [BN-32](https://universalsoftware.atlassian.net/browse/BN-32) | Mesa In Transit Import Exception - Thursday 6:33 PM | Not set |
| Boeing Mesa - General Tickets  | [BN-31](https://universalsoftware.atlassian.net/browse/BN-31) | Business Service | Not set |
| Boeing Mesa - General Tickets  | [BN-30](https://universalsoftware.atlassian.net/browse/BN-30) | Desktop Application | Not set |
| Boeing Mesa - General Tickets  | [BN-28](https://universalsoftware.atlassian.net/browse/BN-28) | Mesa In Transit Import Exception - Friday 11:56am | Not set |
| Boeing Mesa - General Tickets  | [BN-27](https://universalsoftware.atlassian.net/browse/BN-27) | ABC Strat Code Via MFTS | Not set |
| Boeing Mesa - General Tickets  | [BN-26](https://universalsoftware.atlassian.net/browse/BN-26) | Delete Option | Not set |
| Boeing Mesa - General Tickets  | [BN-25](https://universalsoftware.atlassian.net/browse/BN-25) | Boeing Mesa - Supervisor Override Feature Adjustment | Not set |
| Boeing Mesa - General Tickets  | [BN-24](https://universalsoftware.atlassian.net/browse/BN-24) | Boeing Mesa - 45 Day Purge intransit receipts | Not set |
| Boeing Mesa - General Tickets  | [BN-23](https://universalsoftware.atlassian.net/browse/BN-23) | Boeing Mesa - Role Management Implementation + Adjusting Customers WMS Access | Not set |
| Boeing Mesa - General Tickets  | [BN-22](https://universalsoftware.atlassian.net/browse/BN-22) | Boeing Mesa - Create a WMS Grief Label | Not set |
| Boeing Mesa - General Tickets  | [BN-21](https://universalsoftware.atlassian.net/browse/BN-21) | Boeing Mesa - WMS Receipt Label/Page | Not set |
| Boeing Mesa - General Tickets  | [BN-19](https://universalsoftware.atlassian.net/browse/BN-19) | Boeing Mesa - Boeing Mesa - MTR_Number Repack Label | Not set |
| Boeing Mesa - General Tickets  | [BN-18](https://universalsoftware.atlassian.net/browse/BN-18) | Boeing Mesa - Shelf Life Report Improvement | Not set |
| Boeing Mesa - General Tickets  | [BN-17](https://universalsoftware.atlassian.net/browse/BN-17) | Receiving Time Stamps Report - Additional Column (PO Number) | Not set |
| Boeing Mesa - General Tickets  | [BN-16](https://universalsoftware.atlassian.net/browse/BN-16) | Boeing Mesa UAT - WMS Desktop Site Slowdown | Not set |
| Boeing Mesa - General Tickets  | [BN-14](https://universalsoftware.atlassian.net/browse/BN-14) | Boeing Mesa - Receiving Process - Project 25 Auto-Grief logic | Not set |
| Boeing Mesa - General Tickets  | [BN-13](https://universalsoftware.atlassian.net/browse/BN-13) | PTA Tracker Inbound | Not set |
| Boeing Mesa - General Tickets  | [BN-12](https://universalsoftware.atlassian.net/browse/BN-12) | Timestamp In-Transit Files (Boeing Mesa Production) | Not set |
| Boeing Mesa - General Tickets  | [BN-11](https://universalsoftware.atlassian.net/browse/BN-11) | Updated Grief Column | Not set |
| Boeing Mesa - General Tickets  | [BN-9](https://universalsoftware.atlassian.net/browse/BN-9) | Open Receipts on Dashboard [Website] - Mesa WMS | Not set |
| Boeing Mesa - General Tickets  | [BN-8](https://universalsoftware.atlassian.net/browse/BN-8) | InTransit | Not set |
| Boeing Mesa - General Tickets  | [BN-7](https://universalsoftware.atlassian.net/browse/BN-7) | InTransit UAT | Not set |
| Boeing Mesa - General Tickets  | [BN-6](https://universalsoftware.atlassian.net/browse/BN-6) | Next Cycle Count Setup Date Due (Boeing Mesa Production)  | Not set |
| Boeing Mesa - General Tickets  | [BN-5](https://universalsoftware.atlassian.net/browse/BN-5) | Mesa WMS Report Change: HOT Order by Date Report | Not set |
| Boeing Mesa - General Tickets  | [BN-4](https://universalsoftware.atlassian.net/browse/BN-4) | Mesa WMS Report Adjustment: Pick Request History | Not set |
| Boeing Mesa - General Tickets  | [BN-3](https://universalsoftware.atlassian.net/browse/BN-3) | Boeing Mesa - Adding Control Number Variable | Not set |
| Boeing Mesa - General Tickets  | [BN-2](https://universalsoftware.atlassian.net/browse/BN-2) | Cycle Counts (Cycle Count Dates Calculation) | Not set |
| BMW Spartanburg | [BMWS-303](https://universalsoftware.atlassian.net/browse/BMWS-303) | BMW SSRS Reports - JIS53 Rack Sheet - Extra Blank Page | Not set |
| BMW Spartanburg | [BMWS-208](https://universalsoftware.atlassian.net/browse/BMWS-208) | Rerouting Environment Toggle (UAT/PROD) | Not set |
| BMW Spartanburg | [BMWS-13](https://universalsoftware.atlassian.net/browse/BMWS-13) | BOM Verification/Plausibility 'Recheck' | Not set |
| BMW Spartanburg | [BMWS-6](https://universalsoftware.atlassian.net/browse/BMWS-6) | Sequential File Processing | Not set |

### Duncan Miller

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | PortPro Integrations  | [POR-7](https://universalsoftware.atlassian.net/browse/POR-7) | PortPro API integration and data mapping | Not set | Ready | No due date |
| 📋 | Control Tower | [CTP-130](https://universalsoftware.atlassian.net/browse/CTP-130) | Pagination bug in grid | Not set | Code Complete | No due date |
| 📋 | AppDev | [AP-69](https://universalsoftware.atlassian.net/browse/AP-69) | MXExcelToEDI change | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Control Tower | [CTP-80](https://universalsoftware.atlassian.net/browse/CTP-80) | Dashboard - Average Time in Geofence Since Last Move (UI) | 2025-06-19 |
| Control Tower | [CTP-79](https://universalsoftware.atlassian.net/browse/CTP-79) | Dashboard - Inactive Asset >24 HR by Location (UI) | 2025-06-19 |
| Control Tower | [CTP-78](https://universalsoftware.atlassian.net/browse/CTP-78) | Dashboard - Trailer Count by Location (UI) | 2025-06-19 |
| Control Tower | [CTP-77](https://universalsoftware.atlassian.net/browse/CTP-77) | Dashboard - Average Dwell Time by Location (UI) | 2025-06-19 |
| Control Tower | [CTP-87](https://universalsoftware.atlassian.net/browse/CTP-87) | Control Tower - Display gps trail (location history) on map  | 2025-05-30 |
| Control Tower | [CTP-100](https://universalsoftware.atlassian.net/browse/CTP-100) | Localization for Control Tower UI/UX  | 2025-05-13 |
| Control Tower | [CTP-84](https://universalsoftware.atlassian.net/browse/CTP-84) | Update UI to display map with equipment location based on last GPS ping | 2025-05-09 |
| Control Tower | [CTP-67](https://universalsoftware.atlassian.net/browse/CTP-67) | Condense Orders in Grid (front-end) | 2025-04-18 |
| AppDev | [AP-12](https://universalsoftware.atlassian.net/browse/AP-12) | Update MXExcelToEDI service: Add Folio# reference to EDI 204 flat file | 2025-03-14 |
| Control Tower | [CTP-117](https://universalsoftware.atlassian.net/browse/CTP-117) | DTO structure for fetching geolocation data | Not set |
| Control Tower | [CTP-74](https://universalsoftware.atlassian.net/browse/CTP-74) | Re-establish Control Tower functionality around updated database | Not set |
| Control Tower | [CTP-72](https://universalsoftware.atlassian.net/browse/CTP-72) | Adjust TMW Order Details query | Not set |
| Control Tower | [CTP-64](https://universalsoftware.atlassian.net/browse/CTP-64) | Remove Fetch New Grid Data button | Not set |
| Control Tower | [CTP-63](https://universalsoftware.atlassian.net/browse/CTP-63) | Use higher contrast color on the grid header | Not set |
| Control Tower | [CTP-62](https://universalsoftware.atlassian.net/browse/CTP-62) | Use alternating colors for rows in grid | Not set |
| Control Tower | [CTP-49](https://universalsoftware.atlassian.net/browse/CTP-49) | get ABP development environment working | Not set |
| Control Tower | [CTP-46](https://universalsoftware.atlassian.net/browse/CTP-46) | Control Tower - Sorting (front-end) | Not set |
| Control Tower | [CTP-44](https://universalsoftware.atlassian.net/browse/CTP-44) | Highlight Row that is being updated - Front End | Not set |
| Control Tower | [CTP-43](https://universalsoftware.atlassian.net/browse/CTP-43) | Research and resolve excessive X6 creations | Not set |

### Emily Gamble

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🟡 | YMS Project | [YP-37](https://universalsoftware.atlassian.net/browse/YP-37) | In Yard Status Changes | 2025-07-09 | In Progress | 2 days remaining |
| 🟡 | AppDev | [AP-48](https://universalsoftware.atlassian.net/browse/AP-48) | Project Lockdown - Atlas Settlements | 2025-07-09 | In Progress | 2 days remaining |
| ✅ | YMS Project | [YP-124](https://universalsoftware.atlassian.net/browse/YP-124) | Deployment #1 for Phase 2 | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | YMS Project | [YP-50](https://universalsoftware.atlassian.net/browse/YP-50) | Outbound Log Requesed Changes | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | YMS Project | [YP-38](https://universalsoftware.atlassian.net/browse/YP-38) | Trailer Check In Process | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | YMS Project | [YP-36](https://universalsoftware.atlassian.net/browse/YP-36) | Verbiage Changes | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | YMS Project | [YP-21](https://universalsoftware.atlassian.net/browse/YP-21) | Sorting By Categories | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | Highway | [HIG-10](https://universalsoftware.atlassian.net/browse/HIG-10) | Implement Carrier Sync | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | Highway | [HIG-7](https://universalsoftware.atlassian.net/browse/HIG-7) | Implement Carrier Full Update Endpoint | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | Highway | [HIG-4](https://universalsoftware.atlassian.net/browse/HIG-4) | Implement Carrier Alerts Endpoint | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | Highway | [HIG-1](https://universalsoftware.atlassian.net/browse/HIG-1) | Implement Carrier Onboarding Endpoint | 2025-07-11 | In Progress | 4 days remaining |
| ✅ | AppDev | [AP-52](https://universalsoftware.atlassian.net/browse/AP-52) | Atlas EDI bugs | 2025-07-18 | In Progress | 11 days remaining |
| ✅ | Highway | [HIG-28](https://universalsoftware.atlassian.net/browse/HIG-28) | Items still needed | 2025-07-24 | In Progress | 17 days remaining |
| ✅ | YMS Project | [YP-46](https://universalsoftware.atlassian.net/browse/YP-46) | Yard Check Module | 2025-07-25 | Ready | 18 days remaining |
| ✅ | YMS Project | [YP-44](https://universalsoftware.atlassian.net/browse/YP-44) | Move Queue Module for Switchers | 2025-07-28 | In Progress | 21 days remaining |
| ✅ | YMS Project | [YP-22](https://universalsoftware.atlassian.net/browse/YP-22) | Requested Changes v1.1 | 2025-07-28 | In Progress | 21 days remaining |
| ✅ | PortPro Integrations  | [POR-16](https://universalsoftware.atlassian.net/browse/POR-16) | Phase 1 items | 2025-08-11 | In Progress | 35 days remaining |
| ✅ | PortPro Integrations  | [POR-3](https://universalsoftware.atlassian.net/browse/POR-3) | Workday Integration | 2025-08-11 | In Progress | 35 days remaining |
| ✅ | YMS Project | [YP-61](https://universalsoftware.atlassian.net/browse/YP-61) | Switch from AWS to local for pictures and documents | 2025-08-29 | Ready | 53 days remaining |
| ✅ | Control Tower | [CTP-103](https://universalsoftware.atlassian.net/browse/CTP-103) | Adding users to various locations | 2025-08-29 | Refining | 53 days remaining |
| ✅ | YMS Project | [YP-106](https://universalsoftware.atlassian.net/browse/YP-106) | last movement changed to check in date and time | 2025-09-30 | Ready | 85 days remaining |
| ✅ | YMS Project | [YP-94](https://universalsoftware.atlassian.net/browse/YP-94) | Inbound Log Requested Change | 2025-09-30 | Ready | 85 days remaining |
| ✅ | YMS Project | [YP-104](https://universalsoftware.atlassian.net/browse/YP-104) | Placeholder Epic for RFID YOTTA hardware | 2025-12-31 | Refining | 177 days remaining |
| 📋 | YMS Project | [YP-141](https://universalsoftware.atlassian.net/browse/YP-141) | Deployment #2 for Phase 2 | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-139](https://universalsoftware.atlassian.net/browse/YP-139) | Requested Changes phase 3 | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-125](https://universalsoftware.atlassian.net/browse/YP-125) | Adding Zones to system as another master | Not set | In Progress | No due date |
| 📋 | AppDev | [AP-70](https://universalsoftware.atlassian.net/browse/AP-70) | MX Excel to EDI Change | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| YMS Project | [YP-55](https://universalsoftware.atlassian.net/browse/YP-55) | Production Issues/Bugs | 2025-08-29 |
| Control Tower | [CTP-102](https://universalsoftware.atlassian.net/browse/CTP-102) | Solar Winds tickets - requests | 2025-07-31 |
| WES | [WES-207](https://universalsoftware.atlassian.net/browse/WES-207) | CRUD tickets | 2025-06-13 |
| AppDev | [AP-37](https://universalsoftware.atlassian.net/browse/AP-37) | ULCarrierImporter Service requests | 2025-06-06 |
| AppDev | [AP-44](https://universalsoftware.atlassian.net/browse/AP-44) | ULH requests for website | 2025-06-02 |
| AppDev | [AP-24](https://universalsoftware.atlassian.net/browse/AP-24) | Atlas Settlement Changes | 2025-05-06 |
| YMS Project | [YP-54](https://universalsoftware.atlassian.net/browse/YP-54) | Deploy YMS to AWS | 2025-04-24 |
| YMS Project | [YP-2](https://universalsoftware.atlassian.net/browse/YP-2) | Research on YMS | Not set |
| WES | [WES-77](https://universalsoftware.atlassian.net/browse/WES-77) | Database Restructure for YMS - Proposal | Not set |
| Highway | [HIG-30](https://universalsoftware.atlassian.net/browse/HIG-30) | Emily to make sure all tickets are updated to reflect all information | Not set |
| Highway | [HIG-24](https://universalsoftware.atlassian.net/browse/HIG-24) | Blocker - how to tell "is certified" | Not set |
| AppDev | [AP-77](https://universalsoftware.atlassian.net/browse/AP-77) | RMIS carrier issues | Not set |

### Hunter Vallad

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | Refining | 4 days remaining |
| 📋 | DTNA CMS | [DTCMS-29](https://universalsoftware.atlassian.net/browse/DTCMS-29) | Phase 2 - 2 Layered Approval | Not set | In Progress | No due date |
| 📋 | DTNA CMS | [DTCMS-21](https://universalsoftware.atlassian.net/browse/DTCMS-21) | DTNA RMS Scrap Tracking | Not set | Packaged | No due date |
| 📋 | BMW Spartanburg | [BMWS-454](https://universalsoftware.atlassian.net/browse/BMWS-454) | BMW - Refactor Inventory Update Procedures to Use Table Types | Not set | Backlog | No due date |
| 📋 | BMW Spartanburg | [BMWS-207](https://universalsoftware.atlassian.net/browse/BMWS-207) | Update to x12 processor | Not set | Backlog | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-450](https://universalsoftware.atlassian.net/browse/BMWS-450) | Update Rack Sheet to Replace Description Field with Part Number Display | 2025-07-02 |
| BMW Spartanburg | [BMWS-321](https://universalsoftware.atlassian.net/browse/BMWS-321) | Prevent Seq Web Files from Being Sent When Sequence is Not Valid | 2025-06-25 |
| BMW Spartanburg | [BMWS-397](https://universalsoftware.atlassian.net/browse/BMWS-397) | Update BCA Rack Sheet to Display One Entry Per Slot with Correct Slot Numbering | 2025-06-20 |
| BMW Spartanburg | [BMWS-382](https://universalsoftware.atlassian.net/browse/BMWS-382) | ASN for D Pillar Missing L/R Rack Indicators | 2025-06-17 |
| BMW Spartanburg | [BMWS-317](https://universalsoftware.atlassian.net/browse/BMWS-317) | Adjust Sequencing Algorithm to Support Bulk & Pre-Sequenced Material | 2025-06-15 |
| BMW Spartanburg | [BMWS-355](https://universalsoftware.atlassian.net/browse/BMWS-355) | Bulk Reorders Testing in WMS | 2025-06-09 |
| BMW Spartanburg | [BMWS-2](https://universalsoftware.atlassian.net/browse/BMWS-2) | X12 Processor Updates | 2025-04-26 |
| AppDev | [AP-9](https://universalsoftware.atlassian.net/browse/AP-9) | Standardized WMS Deployment - 02/18/2025 | 2025-02-24 |
| SubZero  | [SBZ-76](https://universalsoftware.atlassian.net/browse/SBZ-76) | Add Sub-Zero to WMS User Management Utility | 2024-09-18 |
| Navistar San Antonio | [NVSSA-41](https://universalsoftware.atlassian.net/browse/NVSSA-41) | Job 133112 Can't Reset Info | 2024-02-07 |
| Value Added  | [VAL-63](https://universalsoftware.atlassian.net/browse/VAL-63) | GitSync - pass directories via arguments | 2024-02-01 |
| Navistar San Antonio | [NVSSA-6](https://universalsoftware.atlassian.net/browse/NVSSA-6) | Testing Ticket | 2023-10-05 |
| Navistar San Antonio | [NVSSA-69](https://universalsoftware.atlassian.net/browse/NVSSA-69) | Update to Wheel Positions on Job Generation | 2023-09-25 |
| WMS 3.0 Research  | [YJ3-47](https://universalsoftware.atlassian.net/browse/YJ3-47) | Setup WMS 3 Website on new 2022 Dev Server | Not set |
| WMS 3.0 Research  | [YJ3-36](https://universalsoftware.atlassian.net/browse/YJ3-36) | RouteTypes Page | Not set |
| WMS 3.0 Research  | [YJ3-32](https://universalsoftware.atlassian.net/browse/YJ3-32) | Inventory Details Page Radzen Rework | Not set |
| WMS 3.0 Research  | [YJ3-28](https://universalsoftware.atlassian.net/browse/YJ3-28) | Printers Page | Not set |
| WMS 3.0 Research  | [YJ3-26](https://universalsoftware.atlassian.net/browse/YJ3-26) | Labels Page | Not set |
| WMS 3.0 Research  | [YJ3-22](https://universalsoftware.atlassian.net/browse/YJ3-22) | Look into MAUI integration with webviews | Not set |
| WMS 3.0 Research  | [YJ3-9](https://universalsoftware.atlassian.net/browse/YJ3-9) | Routes Page | Not set |
| WMS User Management Utility | [WUMU-4](https://universalsoftware.atlassian.net/browse/WUMU-4) | Goodfield Database - Crash on Access | Not set |
| WMS User Management Utility | [WUMU-3](https://universalsoftware.atlassian.net/browse/WUMU-3) | Remove References to all three Lear Tuscaloosa Instances from User Management | Not set |
| WMS User Management Utility | [WUMU-2](https://universalsoftware.atlassian.net/browse/WUMU-2) | Remove References to All Lucid Sites In User Management Utility | Not set |
| Value Added  | [VAL-436](https://universalsoftware.atlassian.net/browse/VAL-436) | GitSync Crash | Not set |
| Value Added  | [VAL-427](https://universalsoftware.atlassian.net/browse/VAL-427) | Factory Zero RESS Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-426](https://universalsoftware.atlassian.net/browse/VAL-426) | Sub Zero Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-425](https://universalsoftware.atlassian.net/browse/VAL-425) | Factory Zero Prime Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-424](https://universalsoftware.atlassian.net/browse/VAL-424) | Sub Zero UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-415](https://universalsoftware.atlassian.net/browse/VAL-415) | HR Time And UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-413](https://universalsoftware.atlassian.net/browse/VAL-413) | FCA ClarkStreet CMS UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-398](https://universalsoftware.atlassian.net/browse/VAL-398) | FCA ClarkStreet CMS Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-397](https://universalsoftware.atlassian.net/browse/VAL-397) | Alliance Laundry Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-395](https://universalsoftware.atlassian.net/browse/VAL-395) | Data Archiver - Test Version One on an Older Site | Not set |
| Value Added  | [VAL-394](https://universalsoftware.atlassian.net/browse/VAL-394) | Factory Zero Prime UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-393](https://universalsoftware.atlassian.net/browse/VAL-393) | Factory Zero RESS UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-388](https://universalsoftware.atlassian.net/browse/VAL-388) | Alliance DMZ UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-382](https://universalsoftware.atlassian.net/browse/VAL-382) | Alliance DMZ Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-377](https://universalsoftware.atlassian.net/browse/VAL-377) | HR Time And Attendance Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-368](https://universalsoftware.atlassian.net/browse/VAL-368) | Daimler DTNA UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-366](https://universalsoftware.atlassian.net/browse/VAL-366) | WMS 3.0 | Not set |
| Value Added  | [VAL-365](https://universalsoftware.atlassian.net/browse/VAL-365) | FCA Saltillo CMS UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-364](https://universalsoftware.atlassian.net/browse/VAL-364) | Boeing XDock UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-363](https://universalsoftware.atlassian.net/browse/VAL-363) | FCA Smyrna CMS Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-361](https://universalsoftware.atlassian.net/browse/VAL-361) | Daimler DTNA Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-360](https://universalsoftware.atlassian.net/browse/VAL-360) | Daimler DTNA Production SAML  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-359](https://universalsoftware.atlassian.net/browse/VAL-359) | CNH Racine Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-358](https://universalsoftware.atlassian.net/browse/VAL-358) | CNH Racine UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-357](https://universalsoftware.atlassian.net/browse/VAL-357) | CNH Goodfield Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-356](https://universalsoftware.atlassian.net/browse/VAL-356) | CNH Goodfield UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-355](https://universalsoftware.atlassian.net/browse/VAL-355) | FCA Smyrna CMS UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-354](https://universalsoftware.atlassian.net/browse/VAL-354) | Boeing CMS Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-353](https://universalsoftware.atlassian.net/browse/VAL-353) | Boeing CMS UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-351](https://universalsoftware.atlassian.net/browse/VAL-351) | FCA ClarkStreet XDock Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-350](https://universalsoftware.atlassian.net/browse/VAL-350) | FCA ClarkStreet XDock UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-349](https://universalsoftware.atlassian.net/browse/VAL-349) | FCA Smyrna XDock Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-348](https://universalsoftware.atlassian.net/browse/VAL-348) | FCA Smyrna XDock UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-347](https://universalsoftware.atlassian.net/browse/VAL-347) | FCA Saltillo XDock Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-346](https://universalsoftware.atlassian.net/browse/VAL-346) | FCA Saltillo XDock UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-345](https://universalsoftware.atlassian.net/browse/VAL-345) | FCA Huber Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-344](https://universalsoftware.atlassian.net/browse/VAL-344) | FCA Huber UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-343](https://universalsoftware.atlassian.net/browse/VAL-343) | FCA Mack Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-342](https://universalsoftware.atlassian.net/browse/VAL-342) | FCA Mack UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-341](https://universalsoftware.atlassian.net/browse/VAL-341) | Alliance Laundry UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-338](https://universalsoftware.atlassian.net/browse/VAL-338) | Whirlpool XDock Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-337](https://universalsoftware.atlassian.net/browse/VAL-337) | Whirlpool XDock UAT- Migration to Bit Bucket | Not set |
| Value Added  | [VAL-335](https://universalsoftware.atlassian.net/browse/VAL-335) | FCA Saltillo CMS Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-333](https://universalsoftware.atlassian.net/browse/VAL-333) | Boeing XDock Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-329](https://universalsoftware.atlassian.net/browse/VAL-329) | Boeing Mesa Main UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-328](https://universalsoftware.atlassian.net/browse/VAL-328) | FCA Toluca CMS UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-327](https://universalsoftware.atlassian.net/browse/VAL-327) | Boeing Mesa Tooling UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-326](https://universalsoftware.atlassian.net/browse/VAL-326) | Boeing Mesa Main Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-325](https://universalsoftware.atlassian.net/browse/VAL-325) | FCA Toluca CMS Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-324](https://universalsoftware.atlassian.net/browse/VAL-324) | Boeing Mesa Tooling Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-323](https://universalsoftware.atlassian.net/browse/VAL-323) | Boeing Portland Spares Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-322](https://universalsoftware.atlassian.net/browse/VAL-322) | Boeing Portland Spares UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-321](https://universalsoftware.atlassian.net/browse/VAL-321) | Boeing Portland Production - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-320](https://universalsoftware.atlassian.net/browse/VAL-320) | Boeing Portland UAT - Migration To Bit Bucket | Not set |
| Value Added  | [VAL-312](https://universalsoftware.atlassian.net/browse/VAL-312) | FCA Toluca UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-310](https://universalsoftware.atlassian.net/browse/VAL-310) | GM Arlington Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-308](https://universalsoftware.atlassian.net/browse/VAL-308) | FCA Toluca Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-307](https://universalsoftware.atlassian.net/browse/VAL-307) | GM Flint Continental UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-306](https://universalsoftware.atlassian.net/browse/VAL-306) | GM FtWayne UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-305](https://universalsoftware.atlassian.net/browse/VAL-305) | GM Flint Continental Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-304](https://universalsoftware.atlassian.net/browse/VAL-304) | GM FlexNGate UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-303](https://universalsoftware.atlassian.net/browse/VAL-303) | GM FtWayne Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-302](https://universalsoftware.atlassian.net/browse/VAL-302) | GM Arlington UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-301](https://universalsoftware.atlassian.net/browse/VAL-301) | GM FlexNGate Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-295](https://universalsoftware.atlassian.net/browse/VAL-295) | Update Purge Mechanics in Whirlpool | Not set |
| Value Added  | [VAL-278](https://universalsoftware.atlassian.net/browse/VAL-278) | Boeing Mesa Tooling Scan Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-271](https://universalsoftware.atlassian.net/browse/VAL-271) | Setup Arlington Data Archive Process | Not set |
| Value Added  | [VAL-263](https://universalsoftware.atlassian.net/browse/VAL-263) | Boeing Mesa UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-257](https://universalsoftware.atlassian.net/browse/VAL-257) | Boeing Mesa Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-254](https://universalsoftware.atlassian.net/browse/VAL-254) | Boeing XDock - Driver Complete App | Not set |
| Value Added  | [VAL-252](https://universalsoftware.atlassian.net/browse/VAL-252) | Boeing XDock Driver Complete | Not set |
| Value Added  | [VAL-237](https://universalsoftware.atlassian.net/browse/VAL-237) | Boeing Mesa Tooling Scan UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-198](https://universalsoftware.atlassian.net/browse/VAL-198) | Boeing Mesa Tooling Scan UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-196](https://universalsoftware.atlassian.net/browse/VAL-196) | Boeing Mesa Tooling Scan Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-172](https://universalsoftware.atlassian.net/browse/VAL-172) | StorkRubber Dearborn Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-171](https://universalsoftware.atlassian.net/browse/VAL-171) | StorkRubber Dearborn UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-169](https://universalsoftware.atlassian.net/browse/VAL-169) | Polaris WH17 Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-167](https://universalsoftware.atlassian.net/browse/VAL-167) | Polaris WH17 UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-166](https://universalsoftware.atlassian.net/browse/VAL-166) | Polaris Shakopee XDock Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-164](https://universalsoftware.atlassian.net/browse/VAL-164) | Polaris Shakopee XDock  UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-162](https://universalsoftware.atlassian.net/browse/VAL-162) | Polaris Huntsville Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-160](https://universalsoftware.atlassian.net/browse/VAL-160) | Polaris Huntsville UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-157](https://universalsoftware.atlassian.net/browse/VAL-157) | Nissan SLP XDock Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-154](https://universalsoftware.atlassian.net/browse/VAL-154) | Nissan SLP XDock UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-153](https://universalsoftware.atlassian.net/browse/VAL-153) | Boeing Mesa UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-139](https://universalsoftware.atlassian.net/browse/VAL-139) | Westport Ford Steering Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-137](https://universalsoftware.atlassian.net/browse/VAL-137) | Westport Ford Steering UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-135](https://universalsoftware.atlassian.net/browse/VAL-135) | Westport Louisville Lear Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-133](https://universalsoftware.atlassian.net/browse/VAL-133) | Westport Louisville Lear UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-119](https://universalsoftware.atlassian.net/browse/VAL-119) | Boeing Mesa Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-118](https://universalsoftware.atlassian.net/browse/VAL-118) | Navistar SanAntonio Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-117](https://universalsoftware.atlassian.net/browse/VAL-117) | GM Flint Torrey UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-115](https://universalsoftware.atlassian.net/browse/VAL-115) | Boeing CMS Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-114](https://universalsoftware.atlassian.net/browse/VAL-114) | Boeing CMS UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-111](https://universalsoftware.atlassian.net/browse/VAL-111) | GM SLP VAA UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-108](https://universalsoftware.atlassian.net/browse/VAL-108) | GM Flint Torrey Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-105](https://universalsoftware.atlassian.net/browse/VAL-105) | GM SLP ODC UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-104](https://universalsoftware.atlassian.net/browse/VAL-104) | GM SLP VAA Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-101](https://universalsoftware.atlassian.net/browse/VAL-101) | Navistar SanAntonio UAT  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-95](https://universalsoftware.atlassian.net/browse/VAL-95) | GM SLP ODC Production  - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-81](https://universalsoftware.atlassian.net/browse/VAL-81) | Publish Service | Not set |
| Value Added  | [VAL-80](https://universalsoftware.atlassian.net/browse/VAL-80) | Emailer | Not set |
| Value Added  | [VAL-79](https://universalsoftware.atlassian.net/browse/VAL-79) | Git - Modulate the config folder to its own repo | Not set |
| Value Added  | [VAL-77](https://universalsoftware.atlassian.net/browse/VAL-77) | X12 Importer - Support for MessageWay Formatted Files | Not set |
| Value Added  | [VAL-67](https://universalsoftware.atlassian.net/browse/VAL-67) | Git Migration Tool | Not set |
| Value Added  | [VAL-40](https://universalsoftware.atlassian.net/browse/VAL-40) | X12 Improvements | Not set |
| Value Added  | [VAL-37](https://universalsoftware.atlassian.net/browse/VAL-37) | User Management - Allow Integrated Security Logins | Not set |
| Value Added  | [VAL-36](https://universalsoftware.atlassian.net/browse/VAL-36) | X12 Processor - Further Optimizations | Not set |
| Value Added  | [VAL-32](https://universalsoftware.atlassian.net/browse/VAL-32) | Git Sync - Limit Branch Knowledge onsite | Not set |
| Value Added  | [VAL-31](https://universalsoftware.atlassian.net/browse/VAL-31) | Improvement to X12 File Upload Procedure | Not set |
| Value Added  | [VAL-24](https://universalsoftware.atlassian.net/browse/VAL-24) | Universal Table Data Archiver | Not set |
| Stork Rubber - General Tickets  | [STOR-19](https://universalsoftware.atlassian.net/browse/STOR-19) | ASN Resend | Not set |
| Stork Rubber - General Tickets  | [STOR-18](https://universalsoftware.atlassian.net/browse/STOR-18) | Cancel and Resubmit 20064 | Not set |
| Stork Rubber - General Tickets  | [STOR-17](https://universalsoftware.atlassian.net/browse/STOR-17) | Cancel and Resubmit 20060 | Not set |
| Stork Rubber - General Tickets  | [STOR-16](https://universalsoftware.atlassian.net/browse/STOR-16) | Cancel and Resubmit 20056 | Not set |
| Stork Rubber - General Tickets  | [STOR-15](https://universalsoftware.atlassian.net/browse/STOR-15) | Internal/External Email Test | Not set |
| Stork Rubber - General Tickets  | [STOR-14](https://universalsoftware.atlassian.net/browse/STOR-14) | Reporting Service Configuration Manager | Not set |
| Stork Rubber - General Tickets  | [STOR-13](https://universalsoftware.atlassian.net/browse/STOR-13) | Web Configs | Not set |
| Stork Rubber - General Tickets  | [STOR-12](https://universalsoftware.atlassian.net/browse/STOR-12) | Stork Rubber - ASN Resends needed | Not set |
| Stork Rubber - General Tickets  | [STOR-11](https://universalsoftware.atlassian.net/browse/STOR-11) | Resend ASN 20040 | Not set |
| Stork Rubber - General Tickets  | [STOR-10](https://universalsoftware.atlassian.net/browse/STOR-10) | ASN Resubmittions | Not set |
| Stork Rubber - General Tickets  | [STOR-9](https://universalsoftware.atlassian.net/browse/STOR-9) | Resend ASN 20045 | Not set |
| Stork Rubber - General Tickets  | [STOR-8](https://universalsoftware.atlassian.net/browse/STOR-8) | Scheduled Tasks Check | Not set |
| Stork Rubber - General Tickets  | [STOR-7](https://universalsoftware.atlassian.net/browse/STOR-7) | ASN Cancel and resend 20057 and 20060 | Not set |
| Stork Rubber - General Tickets  | [STOR-6](https://universalsoftware.atlassian.net/browse/STOR-6) | Automatically send 824 and 997 | Not set |
| Stork Rubber - General Tickets  | [STOR-5](https://universalsoftware.atlassian.net/browse/STOR-5) | Implement the X12 processor | Not set |
| Stork Rubber - General Tickets  | [STOR-4](https://universalsoftware.atlassian.net/browse/STOR-4) | Preview Window to Validate Trailer Data | Not set |
| Stork Rubber - General Tickets  | [STOR-2](https://universalsoftware.atlassian.net/browse/STOR-2) | ASN 20048 Fix and Resend | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-107](https://universalsoftware.atlassian.net/browse/RLNGTN-107) | Duplicate CLOC Buckets | Not set |
| Polaris - Huntsville - General Tickets  | [POL-17](https://universalsoftware.atlassian.net/browse/POL-17) | ProcessHuntsville | Not set |
| Polaris - Huntsville - General Tickets  | [POL-16](https://universalsoftware.atlassian.net/browse/POL-16) | ProcessHotboard_Polaris | Not set |
| Polaris - Huntsville - General Tickets  | [POL-13](https://universalsoftware.atlassian.net/browse/POL-13) | NonWMSInvEmailer | Not set |
| Polaris - Huntsville - General Tickets  | [POL-12](https://universalsoftware.atlassian.net/browse/POL-12) | IMAP | Not set |
| Polaris - Huntsville - General Tickets  | [POL-11](https://universalsoftware.atlassian.net/browse/POL-11) | EDIFileMover | Not set |
| Polaris - Huntsville - General Tickets  | [POL-6](https://universalsoftware.atlassian.net/browse/POL-6) | Website | Not set |
| Polaris - Huntsville - General Tickets  | [POL-5](https://universalsoftware.atlassian.net/browse/POL-5) | Web/App configs | Not set |
| Polaris - Huntsville - General Tickets  | [POL-4](https://universalsoftware.atlassian.net/browse/POL-4) | Scheduled Tasks | Not set |
| Polaris - Huntsville - General Tickets  | [POL-3](https://universalsoftware.atlassian.net/browse/POL-3) | Internal/External Test | Not set |
| Polaris - Huntsville - General Tickets  | [POL-2](https://universalsoftware.atlassian.net/browse/POL-2) | Reporting Service Configuration Manager | Not set |
| Navistar San Antonio | [NVSSA-68](https://universalsoftware.atlassian.net/browse/NVSSA-68) | Navistar - Warehouse Code Outbound 856 | Not set |
| Navistar San Antonio | [NVSSA-58](https://universalsoftware.atlassian.net/browse/NVSSA-58) | Navistar - Pick to Line - Finals - Labels Overprinting | Not set |
| Navistar San Antonio | [NVSSA-57](https://universalsoftware.atlassian.net/browse/NVSSA-57) | Navistar - Pick to Line - Final-Rim Pick Refresh Bug | Not set |
| Navistar San Antonio | [NVSSA-52](https://universalsoftware.atlassian.net/browse/NVSSA-52) | Finals ASN Incorrect | Not set |
| Navistar San Antonio | [NVSSA-51](https://universalsoftware.atlassian.net/browse/NVSSA-51) | Cannot Open new Trailer | Not set |
| Navistar San Antonio | [NVSSA-49](https://universalsoftware.atlassian.net/browse/NVSSA-49) | Wheels not being generated | Not set |
| Navistar San Antonio | [NVSSA-46](https://universalsoftware.atlassian.net/browse/NVSSA-46) | Job Generation - Remove 4 Rear Wheel Special Condition | Not set |
| Navistar San Antonio | [NVSSA-44](https://universalsoftware.atlassian.net/browse/NVSSA-44) | Label Print Page Issue | Not set |
| Navistar San Antonio | [NVSSA-39](https://universalsoftware.atlassian.net/browse/NVSSA-39) | Process 856 Verification | Not set |
| Navistar San Antonio | [NVSSA-38](https://universalsoftware.atlassian.net/browse/NVSSA-38) | Add Additional '29' Codes | Not set |
| Navistar San Antonio | [NVSSA-32](https://universalsoftware.atlassian.net/browse/NVSSA-32) | Removing a line from BOL/Receiving  | Not set |
| Navistar San Antonio | [NVSSA-23](https://universalsoftware.atlassian.net/browse/NVSSA-23) | Pending Transaction Page | Not set |
| Navistar San Antonio | [NVSSA-12](https://universalsoftware.atlassian.net/browse/NVSSA-12) | Navistar Build Date Sort Update | Not set |
| Navistar San Antonio | [NVSSA-10](https://universalsoftware.atlassian.net/browse/NVSSA-10) | Change on Shipping Label | Not set |
| Lear Stanley Gault | [LSG-25](https://universalsoftware.atlassian.net/browse/LSG-25) | Close Door Function | Not set |
| Lear Stanley Gault | [LSG-24](https://universalsoftware.atlassian.net/browse/LSG-24) | 862 Release Not Processed | Not set |
| Lear Stanley Gault | [LSG-21](https://universalsoftware.atlassian.net/browse/LSG-21) | Webservice | Not set |
| Lear Stanley Gault | [LSG-20](https://universalsoftware.atlassian.net/browse/LSG-20) | Scanner | Not set |
| Lear Stanley Gault | [LSG-19](https://universalsoftware.atlassian.net/browse/LSG-19) | Website | Not set |
| Lear Stanley Gault | [LSG-12](https://universalsoftware.atlassian.net/browse/LSG-12) | Scheduled Tasks | Not set |
| Lear Stanley Gault | [LSG-11](https://universalsoftware.atlassian.net/browse/LSG-11) | App/Web Configs | Not set |
| Lear Stanley Gault | [LSG-10](https://universalsoftware.atlassian.net/browse/LSG-10) | Internal/External Check | Not set |
| Lear Stanley Gault | [LSG-9](https://universalsoftware.atlassian.net/browse/LSG-9) | Reporting Service Configuration Manager | Not set |
| GM SLP WMS | [GMSLP-9](https://universalsoftware.atlassian.net/browse/GMSLP-9) | Webservice | Not set |
| GM SLP WMS | [GMSLP-8](https://universalsoftware.atlassian.net/browse/GMSLP-8) | ScannerApp | Not set |
| GM SLP WMS | [GMSLP-7](https://universalsoftware.atlassian.net/browse/GMSLP-7) | Website | Not set |
| GM SLP WMS | [GMSLP-6](https://universalsoftware.atlassian.net/browse/GMSLP-6) | Internal/External Check | Not set |
| GM SLP WMS | [GMSLP-5](https://universalsoftware.atlassian.net/browse/GMSLP-5) | Reporting Service Configuration Manager | Not set |
| GM SLP WMS | [GMSLP-4](https://universalsoftware.atlassian.net/browse/GMSLP-4) | Scheduled Tasks | Not set |
| GM SLP WMS | [GMSLP-3](https://universalsoftware.atlassian.net/browse/GMSLP-3) | Web/App Configs | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-229](https://universalsoftware.atlassian.net/browse/GMFZGT-229) | Box Move - Generic Bucket Bug | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-7](https://universalsoftware.atlassian.net/browse/GMFZGT-7) | Obsolete Column on RadGrid Auto-enabled when filtering | Not set |
| GM Flint Torrey Rd | [GMFNT-65](https://universalsoftware.atlassian.net/browse/GMFNT-65) | Loads not Closing Out | Not set |
| GM Flint Torrey Rd | [GMFNT-52](https://universalsoftware.atlassian.net/browse/GMFNT-52) | Add handheld Honeywell to usable scanners in app | Not set |
| GM ODC Launch  | [GMDCL-65](https://universalsoftware.atlassian.net/browse/GMDCL-65) | Add Connection String for ODC to User Management Utility | Not set |
| GM ODC Launch  | [GMDCL-22](https://universalsoftware.atlassian.net/browse/GMDCL-22) | Sync with Git | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-62](https://universalsoftware.atlassian.net/browse/GFCGT-62) | Flag ASN Hot not sending emails | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-61](https://universalsoftware.atlassian.net/browse/GFCGT-61) | Remove Picks that are completed automatically | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-60](https://universalsoftware.atlassian.net/browse/GFCGT-60) | Picks not coming in DLOC Order | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-57](https://universalsoftware.atlassian.net/browse/GFCGT-57) | Route/Cycle not in scanner for picks | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-56](https://universalsoftware.atlassian.net/browse/GFCGT-56) | Missing path for no stock history | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-52](https://universalsoftware.atlassian.net/browse/GFCGT-52) | Parts moved using Generic Function CANT be picked | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-3](https://universalsoftware.atlassian.net/browse/GFCGT-3) | Various Pending Flint Continental Conversion Issues | Not set |
| Ford Steering Stanley Gault | [FSSG-42](https://universalsoftware.atlassian.net/browse/FSSG-42) | Outbound Ready- Orders removed - Steering Gear  | Not set |
| Ford Steering Stanley Gault | [FSSG-36](https://universalsoftware.atlassian.net/browse/FSSG-36) | Webservice | Not set |
| Ford Steering Stanley Gault | [FSSG-35](https://universalsoftware.atlassian.net/browse/FSSG-35) | Scanner | Not set |
| Ford Steering Stanley Gault | [FSSG-34](https://universalsoftware.atlassian.net/browse/FSSG-34) | Website | Not set |
| Ford Steering Stanley Gault | [FSSG-25](https://universalsoftware.atlassian.net/browse/FSSG-25) | Scheduled Tasks | Not set |
| Ford Steering Stanley Gault | [FSSG-23](https://universalsoftware.atlassian.net/browse/FSSG-23) | App/Web configs | Not set |
| Ford Steering Stanley Gault | [FSSG-22](https://universalsoftware.atlassian.net/browse/FSSG-22) | Internal/External Check | Not set |
| Ford Steering Stanley Gault | [FSSG-21](https://universalsoftware.atlassian.net/browse/FSSG-21) | Reporting Service Configuration Manager | Not set |
| Ford Steering Stanley Gault | [FSSG-14](https://universalsoftware.atlassian.net/browse/FSSG-14) | Inbound - 830 - Scope and Process | Not set |
| Ford Steering Stanley Gault | [FSSG-11](https://universalsoftware.atlassian.net/browse/FSSG-11) | Repacking / Merge Request | Not set |
| Ford Steering Stanley Gault | [FSSG-10](https://universalsoftware.atlassian.net/browse/FSSG-10) | Inbound - 862 - Scope and Process | Not set |
| Ford Steering Stanley Gault | [FSSG-9](https://universalsoftware.atlassian.net/browse/FSSG-9) | Outbound 856 - support for shifters | Not set |
| Ford Steering Stanley Gault | [FSSG-6](https://universalsoftware.atlassian.net/browse/FSSG-6) | Inbound 856 - China Supplier - Scope and Process | Not set |
| Ford Steering Stanley Gault | [FSSG-5](https://universalsoftware.atlassian.net/browse/FSSG-5) | Outbound - 856 - Scope and Process | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-9](https://universalsoftware.atlassian.net/browse/FNGFW-9) | Webservice | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-8](https://universalsoftware.atlassian.net/browse/FNGFW-8) | Scanner | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-7](https://universalsoftware.atlassian.net/browse/FNGFW-7) | Website | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-6](https://universalsoftware.atlassian.net/browse/FNGFW-6) | Scheduled Tasks Check | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-4](https://universalsoftware.atlassian.net/browse/FNGFW-4) | Reporting Service Configuration Manager | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-3](https://universalsoftware.atlassian.net/browse/FNGFW-3) | Web/App configs | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-2](https://universalsoftware.atlassian.net/browse/FNGFW-2) | Internal/External Email Test | Not set |
| DTNA CMS | [DTCMS-84](https://universalsoftware.atlassian.net/browse/DTCMS-84) | Restrict the dataset for the trailer history page  | Not set |
| DTNA CMS | [DTCMS-80](https://universalsoftware.atlassian.net/browse/DTCMS-80) | BOLs from KI10 to KU98 | Not set |
| DTNA CMS | [DTCMS-79](https://universalsoftware.atlassian.net/browse/DTCMS-79) | RMS and Cycle Count Crashing | Not set |
| DTNA CMS | [DTCMS-78](https://universalsoftware.atlassian.net/browse/DTCMS-78) | DTNA - Cannot Ship LW59 | Not set |
| DTNA CMS | [DTCMS-77](https://universalsoftware.atlassian.net/browse/DTCMS-77) | Scrap shipments are missing addresses on BOL | Not set |
| DTNA CMS | [DTCMS-76](https://universalsoftware.atlassian.net/browse/DTCMS-76) | Requirement Change of Currency Type column | Not set |
| DTNA CMS | [DTCMS-75](https://universalsoftware.atlassian.net/browse/DTCMS-75) | Inventory Inflation - XL-2138 | Not set |
| DTNA CMS | [DTCMS-74](https://universalsoftware.atlassian.net/browse/DTCMS-74) | In-Consistent In-transit quantities | Not set |
| DTNA CMS | [DTCMS-73](https://universalsoftware.atlassian.net/browse/DTCMS-73) | RMS SYSTEM - Need to make SID more visible | Not set |
| DTNA CMS | [DTCMS-71](https://universalsoftware.atlassian.net/browse/DTCMS-71) | Files being transferred to DTNA incorrectly | Not set |
| DTNA CMS | [DTCMS-70](https://universalsoftware.atlassian.net/browse/DTCMS-70) | RMS - CM 6 issue | Not set |
| DTNA CMS | [DTCMS-69](https://universalsoftware.atlassian.net/browse/DTCMS-69) | Quantity Mismatch between Pages | Not set |
| DTNA CMS | [DTCMS-68](https://universalsoftware.atlassian.net/browse/DTCMS-68) | Returnables duplicating in ID (004-CLE Cleveland terminal) | Not set |
| DTNA CMS | [DTCMS-65](https://universalsoftware.atlassian.net/browse/DTCMS-65) | DTNA Generate Shipment Error | Not set |
| DTNA CMS | [DTCMS-64](https://universalsoftware.atlassian.net/browse/DTCMS-64) | RMS Credits | Not set |
| DTNA CMS | [DTCMS-62](https://universalsoftware.atlassian.net/browse/DTCMS-62) | RMS Expendable Reimbursement Report - Dates changes | Not set |
| DTNA CMS | [DTCMS-61](https://universalsoftware.atlassian.net/browse/DTCMS-61) | RMS SYSTEM -  | Not set |
| DTNA CMS | [DTCMS-59](https://universalsoftware.atlassian.net/browse/DTCMS-59) | WMS Issue | Not set |
| DTNA CMS | [DTCMS-54](https://universalsoftware.atlassian.net/browse/DTCMS-54) | Missing Destination BOL Address | Not set |
| DTNA CMS | [DTCMS-53](https://universalsoftware.atlassian.net/browse/DTCMS-53) | DTNA/RMS Address AL53 | Not set |
| DTNA CMS | [DTCMS-51](https://universalsoftware.atlassian.net/browse/DTCMS-51) | RMS- Shortage for Pool Returnables Report | Not set |
| DTNA CMS | [DTCMS-50](https://universalsoftware.atlassian.net/browse/DTCMS-50) | RMS -LOAD ANOTHER ADDRESS | Not set |
| DTNA CMS | [DTCMS-49](https://universalsoftware.atlassian.net/browse/DTCMS-49) | RMS - Address no being updated | Not set |
| DTNA CMS | [DTCMS-47](https://universalsoftware.atlassian.net/browse/DTCMS-47) | DTNA RMS: Add Rack WST-9620 | Not set |
| DTNA CMS | [DTCMS-44](https://universalsoftware.atlassian.net/browse/DTCMS-44) | DTNA SAML Git Wireup | Not set |
| DTNA CMS | [DTCMS-42](https://universalsoftware.atlassian.net/browse/DTCMS-42) | Address update in database | Not set |
| DTNA CMS | [DTCMS-38](https://universalsoftware.atlassian.net/browse/DTCMS-38) | DTNA RMS - ETE Inventory Error | Not set |
| DTNA CMS | [DTCMS-37](https://universalsoftware.atlassian.net/browse/DTCMS-37) | RMS SYSTEM | Not set |
| DTNA CMS | [DTCMS-35](https://universalsoftware.atlassian.net/browse/DTCMS-35) | RMS-IMAX - Research Issue - 9/29 | Not set |
| DTNA CMS | [DTCMS-28](https://universalsoftware.atlassian.net/browse/DTCMS-28) | Post Deployment Report Issue | Not set |
| DTNA CMS | [DTCMS-26](https://universalsoftware.atlassian.net/browse/DTCMS-26) | Filter to Report Trailer by Location | Not set |
| DTNA CMS | [DTCMS-25](https://universalsoftware.atlassian.net/browse/DTCMS-25) | Returnable Catalog - Page for CRUD | Not set |
| DTNA CMS | [DTCMS-24](https://universalsoftware.atlassian.net/browse/DTCMS-24) | RMS/DTNA BUTTOM RECEIVED | Not set |
| DTNA CMS | [DTCMS-22](https://universalsoftware.atlassian.net/browse/DTCMS-22) | Currency type column needed on expendable requests | Not set |
| DTNA CMS | [DTCMS-18](https://universalsoftware.atlassian.net/browse/DTCMS-18) | RMS Training | Not set |
| DTNA CMS | [DTCMS-15](https://universalsoftware.atlassian.net/browse/DTCMS-15) | DTNA RMS Shortage Report Change | Not set |
| DTNA CMS | [DTCMS-12](https://universalsoftware.atlassian.net/browse/DTCMS-12) | RMS Training | Not set |
| DTNA CMS | [DTCMS-11](https://universalsoftware.atlassian.net/browse/DTCMS-11) | RMS - Poka yoke restrictions | Not set |
| DTNA CMS | [DTCMS-6](https://universalsoftware.atlassian.net/browse/DTCMS-6) | DTNA CMS SAML Reconciliation | Not set |
| DTNA CMS | [DTCMS-5](https://universalsoftware.atlassian.net/browse/DTCMS-5) | RMS expendable reimbursement review | Not set |
| DTNA CMS | [DTCMS-3](https://universalsoftware.atlassian.net/browse/DTCMS-3) | DTNA_GenerateShipment Configurable Timeouts | Not set |
| DTNA CMS | [DTCMS-2](https://universalsoftware.atlassian.net/browse/DTCMS-2) | DTNA Trailer Status Improvement | Not set |
| CNH Racine | [CNHR-55](https://universalsoftware.atlassian.net/browse/CNHR-55) | Ekanban Tote Issue | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-35](https://universalsoftware.atlassian.net/browse/BOEIN-35) | Status Correction For Universal Tracker on Direct Deliveries to Final Destination or Charleston Cross-Dock from Compton Cross-Dock			 | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-34](https://universalsoftware.atlassian.net/browse/BOEIN-34) | Add Additional Info to ASN Tracker | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-32](https://universalsoftware.atlassian.net/browse/BOEIN-32) | BASN issue | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-31](https://universalsoftware.atlassian.net/browse/BOEIN-31) | ETA correction on BOL from Renton to CHS | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-18](https://universalsoftware.atlassian.net/browse/BOEIN-18) | TMS Leg100 Created per Load not Unit | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-17](https://universalsoftware.atlassian.net/browse/BOEIN-17) | Adding new Handling fields to SoCAL | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-16](https://universalsoftware.atlassian.net/browse/BOEIN-16) | LoadID displayed on web | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-15](https://universalsoftware.atlassian.net/browse/BOEIN-15) | UAT-Prod Mock Push | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-14](https://universalsoftware.atlassian.net/browse/BOEIN-14) | TMS Response Files for Leg#00 Files | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-13](https://universalsoftware.atlassian.net/browse/BOEIN-13) | Add CarrierCodes to Plants table | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-12](https://universalsoftware.atlassian.net/browse/BOEIN-12) | Interface Record Deployment | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-11](https://universalsoftware.atlassian.net/browse/BOEIN-11) | EDI File Upload | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-8](https://universalsoftware.atlassian.net/browse/BOEIN-8) | Remove old Data | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-4](https://universalsoftware.atlassian.net/browse/BOEIN-4) | Boeing X Dock Requesting - BOL status tracking - new locations and existing. | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-3](https://universalsoftware.atlassian.net/browse/BOEIN-3) | Convert BoeingXdock_ProcessAsnInterop Functionality To Generic ExcelBulkImporter Process | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-2](https://universalsoftware.atlassian.net/browse/BOEIN-2) | Boeing CMS/XDock Business Service on Git | Not set |
| BMW Spartanburg | [BMWS-426](https://universalsoftware.atlassian.net/browse/BMWS-426) | Identify affected procedures and processes that changed due to BMWS-414 | Not set |
| BMW Spartanburg | [BMWS-417](https://universalsoftware.atlassian.net/browse/BMWS-417) | Update B10 Label Format to Replace 1D Barcodes with 2D Barcode and Unique Order Number Prefix | Not set |
| BMW Spartanburg | [BMWS-416](https://universalsoftware.atlassian.net/browse/BMWS-416) | Identify Required Columns for 'OutboundRackMasterTracker' Table | Not set |
| BMW Spartanburg | [BMWS-414](https://universalsoftware.atlassian.net/browse/BMWS-414) | Implement Back Order Process and Schema Updates | Not set |
| BMW Spartanburg | [BMWS-230](https://universalsoftware.atlassian.net/browse/BMWS-230) | Sequence Broadcast - Speed Improvement | Not set |
| BMW Spartanburg | [BMWS-227](https://universalsoftware.atlassian.net/browse/BMWS-227) | TOD Double LIN fix | Not set |
| BMW Spartanburg | [BMWS-119](https://universalsoftware.atlassian.net/browse/BMWS-119) | Update DESADV importer to remove records that have no packaging codes | Not set |
| BMW Spartanburg | [BMWS-33](https://universalsoftware.atlassian.net/browse/BMWS-33) | Implement Real-Time Transmission of SFG Messages | Not set |
| BMW Spartanburg | [BMWS-32](https://universalsoftware.atlassian.net/browse/BMWS-32) | Implement Reorder Handling Page | Not set |
| BMW Spartanburg | [BMWS-26](https://universalsoftware.atlassian.net/browse/BMWS-26) | Implement ASN Bulk Inbound Processing and Validation | Not set |
| BMW Spartanburg | [BMWS-25](https://universalsoftware.atlassian.net/browse/BMWS-25) | Implement SFG Reporting Structure and Integration with BMW Finished Goods Dashboard | Not set |
| BMW Spartanburg | [BMWS-23](https://universalsoftware.atlassian.net/browse/BMWS-23) | X12 - 39 Support | Not set |
| BMW Spartanburg | [BMWS-22](https://universalsoftware.atlassian.net/browse/BMWS-22) | X12 - 242 Support | Not set |
| BMW Spartanburg | [BMWS-21](https://universalsoftware.atlassian.net/browse/BMWS-21) | X12 - 221 Support | Not set |
| BMW Spartanburg | [BMWS-20](https://universalsoftware.atlassian.net/browse/BMWS-20) | ASA File Type - Integrate SCO Data with Production Scheduling System | Not set |
| BMW Spartanburg | [BMWS-17](https://universalsoftware.atlassian.net/browse/BMWS-17) | ASK File Type - Align and Validate Assembly Start Times, Locations, and Sequence Positions with MES System | Not set |
| BMW Spartanburg | [BMWS-16](https://universalsoftware.atlassian.net/browse/BMWS-16) | ATP File Type - Order Mapping and Data Validation | Not set |
| BMW Spartanburg | [BMWS-12](https://universalsoftware.atlassian.net/browse/BMWS-12) | BOM Verification/Plausibility | Not set |
| BMW Spartanburg | [BMWS-11](https://universalsoftware.atlassian.net/browse/BMWS-11) | TOD Management with SEQ+1 Message | Not set |
| BMW Spartanburg | [BMWS-10](https://universalsoftware.atlassian.net/browse/BMWS-10) | Step-by-Step Order Processing | Not set |
| BMW Spartanburg | [BMWS-9](https://universalsoftware.atlassian.net/browse/BMWS-9) | Show deletions on the TOD daily confirmation  | Not set |
| BMW Spartanburg | [BMWS-8](https://universalsoftware.atlassian.net/browse/BMWS-8) | Validation that # of Segments and UNT Control # are Equal | Not set |
| BMW Spartanburg | [BMWS-7](https://universalsoftware.atlassian.net/browse/BMWS-7) | Validation of UNB / UNZ Control Number(s) | Not set |
| BMW Spartanburg | [BMWS-4](https://universalsoftware.atlassian.net/browse/BMWS-4) | Order history tracking from new order status to shipment | Not set |
| Alliance Laundry - General Tickets  | [AL-264](https://universalsoftware.atlassian.net/browse/AL-264) | Partlookup_Alliance bug with updated procedures | Not set |
| Alliance Laundry - General Tickets  | [AL-254](https://universalsoftware.atlassian.net/browse/AL-254) | Webservice | Not set |
| Alliance Laundry - General Tickets  | [AL-253](https://universalsoftware.atlassian.net/browse/AL-253) | Scanner | Not set |
| Alliance Laundry - General Tickets  | [AL-252](https://universalsoftware.atlassian.net/browse/AL-252) | Website | Not set |
| Alliance Laundry - General Tickets  | [AL-251](https://universalsoftware.atlassian.net/browse/AL-251) | Internal/External Test | Not set |
| Alliance Laundry - General Tickets  | [AL-231](https://universalsoftware.atlassian.net/browse/AL-231) | Application/Web Config Check | Not set |
| Alliance Laundry - General Tickets  | [AL-230](https://universalsoftware.atlassian.net/browse/AL-230) | Scheduled Tasks SMTP check | Not set |
| Alliance Laundry - General Tickets  | [AL-229](https://universalsoftware.atlassian.net/browse/AL-229) | Reporting Service Configuration Manager Check | Not set |
| Alliance Laundry - General Tickets  | [AL-195](https://universalsoftware.atlassian.net/browse/AL-195) | Inventory Adjust Bug - Location Value Changed But No Change | Not set |

### Jon Taylor

#### In Progress Items

_No items in progress._

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| YMS Project | [YP-39](https://universalsoftware.atlassian.net/browse/YP-39) | Implementing AWS to deploy YMS | Not set |
| WES | [WES-47](https://universalsoftware.atlassian.net/browse/WES-47) | Research ticket for docker to run microservices  | Not set |
| PortPro Integrations  | [POR-27](https://universalsoftware.atlassian.net/browse/POR-27) | Vault Access | Not set |
| Highway | [HIG-29](https://universalsoftware.atlassian.net/browse/HIG-29) | Data research for RMIS and Highway to compare gaps | Not set |
| AppDev | [AP-40](https://universalsoftware.atlassian.net/browse/AP-40) | Fuel Surcharge Pay Update In Atlas | Not set |
| AppDev | [AP-23](https://universalsoftware.atlassian.net/browse/AP-23) | Atlas KeyBank FileSplitting Process | Not set |
| AppDev | [AP-10](https://universalsoftware.atlassian.net/browse/AP-10) | Research ticket for ABP.io | Not set |

### Justin Kerketta

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ⚠️ | BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-07-08 | In Progress | 1 days remaining |
| ✅ | BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-11 | In Progress | 4 days remaining |
| 📋 | BMW Spartanburg | [BMWS-342](https://universalsoftware.atlassian.net/browse/BMWS-342) | Adjust the inventory in locations page | Not set | Ready | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Warehouse Management Systerm | [WMS-16](https://universalsoftware.atlassian.net/browse/WMS-16) | Update Inventory Detail Application Service (class InventoryDetailAppService) to move Entity Framework LINQ code into Domain module | 2024-11-15 |
| Warehouse Management Systerm | [WMS-9](https://universalsoftware.atlassian.net/browse/WMS-9) | Update Inventory Application Service (class InventoryAppService) to move Entity Framework LINQ code into Domain module | 2024-11-14 |
| Warehouse Management Systerm | [WMS-10](https://universalsoftware.atlassian.net/browse/WMS-10) | Create InventoryRepository in Domain module and Entity Framework module | 2024-11-12 |
| WMS 3.0 Research  | [YJ3-48](https://universalsoftware.atlassian.net/browse/YJ3-48) | Create records on Inventory Details Page | 2024-07-26 |
| WMS 3.0 Research  | [YJ3-14](https://universalsoftware.atlassian.net/browse/YJ3-14) | Create records on Inventory Details Page | 2024-07-26 |
| WMS 3.0 Research  | [YJ3-62](https://universalsoftware.atlassian.net/browse/YJ3-62) | Understand how "ABP.IO" deals with attachments such as image files, PDF documents, etc. | Not set |
| WMS 3.0 Research  | [YJ3-60](https://universalsoftware.atlassian.net/browse/YJ3-60) | Database Design | Not set |
| WMS 3.0 Research  | [YJ3-58](https://universalsoftware.atlassian.net/browse/YJ3-58) | Research: Understand Docker  | Not set |
| WMS 3.0 Research  | [YJ3-57](https://universalsoftware.atlassian.net/browse/YJ3-57) | Research Microservice Solutions in "ABPO.IO" | Not set |
| WMS 3.0 Research  | [YJ3-54](https://universalsoftware.atlassian.net/browse/YJ3-54) | REASEARCH - Angular and JavaScript to support WES and WMS implementation | Not set |
| WMS 3.0 Research  | [YJ3-53](https://universalsoftware.atlassian.net/browse/YJ3-53) | RESEARCH message bus architecture for WES | Not set |
| WMS 3.0 Research  | [YJ3-34](https://universalsoftware.atlassian.net/browse/YJ3-34) | Orders Page | Not set |
| WMS 3.0 Research  | [YJ3-33](https://universalsoftware.atlassian.net/browse/YJ3-33) | Order Items Details Page | Not set |
| WMS 3.0 Research  | [YJ3-31](https://universalsoftware.atlassian.net/browse/YJ3-31) | Locations Page Radzen Rework | Not set |
| WMS 3.0 Research  | [YJ3-27](https://universalsoftware.atlassian.net/browse/YJ3-27) | Inventory Details - Get Detail Record | Not set |
| WMS 3.0 Research  | [YJ3-25](https://universalsoftware.atlassian.net/browse/YJ3-25) | Research ABP.IO - Justin | Not set |
| WMS 3.0 Research  | [YJ3-20](https://universalsoftware.atlassian.net/browse/YJ3-20) | Review ASP.NET Zero - Justin | Not set |
| WMS 3.0 Research  | [YJ3-8](https://universalsoftware.atlassian.net/browse/YJ3-8) | Order Items Page | Not set |
| Warehouse Management Systerm | [WMS-14](https://universalsoftware.atlassian.net/browse/WMS-14) | Update Inventory Detail Serial Application Service (class InventoryDetailSerialAppService) to move Entity Framework LINQ code into Domain module | Not set |
| Warehouse Management Systerm | [WMS-13](https://universalsoftware.atlassian.net/browse/WMS-13) | Investigate Temporal Tables | Not set |
| Warehouse Management Systerm | [WMS-12](https://universalsoftware.atlassian.net/browse/WMS-12) | Update Application Services (Label, Printer, Routes and Trailer) to move Entity Framework LINQ code into Domain module | Not set |
| Warehouse Management Systerm | [WMS-3](https://universalsoftware.atlassian.net/browse/WMS-3) | Update Order Item Detail Application Service (class OrderItemDetail) to move Entity Framework LINQ code into Domain module | Not set |
| Warehouse Management Systerm | [WMS-1](https://universalsoftware.atlassian.net/browse/WMS-1) | WMS 3.0 - Base Entity Support | Not set |
| BMW Spartanburg | [BMWS-329](https://universalsoftware.atlassian.net/browse/BMWS-329) | Inbound By Plant Error on Refresh | Not set |
| BMW Spartanburg | [BMWS-311](https://universalsoftware.atlassian.net/browse/BMWS-311) | Fix Flag ASN Hot Page | Not set |
| BMW Spartanburg | [BMWS-298](https://universalsoftware.atlassian.net/browse/BMWS-298) | Update column filters, Refresh button on InboundByPlant_BMWSpartanburg Page | Not set |
| BMW Spartanburg | [BMWS-262](https://universalsoftware.atlassian.net/browse/BMWS-262) | Adjust the Inbound Locations page | Not set |
| BMW Spartanburg | [BMWS-217](https://universalsoftware.atlassian.net/browse/BMWS-217) | Adjust the Trailer Status page | Not set |
| BMW Spartanburg | [BMWS-135](https://universalsoftware.atlassian.net/browse/BMWS-135) | Updates to Trailer History page | Not set |
| BMW Spartanburg | [BMWS-114](https://universalsoftware.atlassian.net/browse/BMWS-114) | ManualAsnMaster table records need to be identified as Bulk or Pre-Sequenced  | Not set |
| BMW Spartanburg | [BMWS-72](https://universalsoftware.atlassian.net/browse/BMWS-72) | Sequence Broadcast Mandatory Optional flag | Not set |

### Justin Mosley

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | SubZero  | [SBZ-44](https://universalsoftware.atlassian.net/browse/SBZ-44) | Automatic Printing Trailer Receipt Report | Not set | In Progress | No due date |
| 📋 | SubZero  | [SBZ-10](https://universalsoftware.atlassian.net/browse/SBZ-10) | ASN Location Management Page - What If Scenario - No Printer | Not set | Validated | No due date |
| 📋 | BMW Spartanburg | [BMWS-448](https://universalsoftware.atlassian.net/browse/BMWS-448) | Fix System Error on Part Number Search in TOD Line Items Menu | Not set | Review | No due date |
| 📋 | BMW Spartanburg | [BMWS-436](https://universalsoftware.atlassian.net/browse/BMWS-436) | SWET Dashboard Design and Implementation | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-408](https://universalsoftware.atlassian.net/browse/BMWS-408) | Add Missing Columns InventoryDetailsPurge | 2025-06-24 |
| BMW Spartanburg | [BMWS-380](https://universalsoftware.atlassian.net/browse/BMWS-380) | Correct Label Order for Printer Output | 2025-06-18 |
| BMW Spartanburg | [BMWS-286](https://universalsoftware.atlassian.net/browse/BMWS-286) | JIS50 PartFamilies on Outbound Rack page | 2025-06-17 |
| BMW Spartanburg | [BMWS-352](https://universalsoftware.atlassian.net/browse/BMWS-352) | Setup BMW Dev Environment with UAT | 2025-06-09 |
| BMW Spartanburg | [BMWS-145](https://universalsoftware.atlassian.net/browse/BMWS-145) | Outbound ASN Generation | 2025-04-23 |
| SubZero  | [SBZ-42](https://universalsoftware.atlassian.net/browse/SBZ-42) | Add Launch Team WMS Accounts | 2024-09-18 |
| Westport - WheelTire - General Tickets  | [WSWTGT-42](https://universalsoftware.atlassian.net/browse/WSWTGT-42) | Sprint Importer Error - Specific Jobs 10/21/24 | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-5](https://universalsoftware.atlassian.net/browse/WSWTGT-5) | BR-101 Sprint Importer - Breakout the Work Order Generation | Not set |
| Westport - Seats - General Tickets  | [WSSG-37](https://universalsoftware.atlassian.net/browse/WSSG-37) | Add Chassis Number to TrailerStatus, TrailerHistory, and InventorySeatsLogReport | Not set |
| Value Added  | [VAL-284](https://universalsoftware.atlassian.net/browse/VAL-284) | Update Purge Mechanics in FCA Mack WMS | Not set |
| Value Added  | [VAL-270](https://universalsoftware.atlassian.net/browse/VAL-270) | GM Arlington UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-267](https://universalsoftware.atlassian.net/browse/VAL-267) | GM Arlington Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-260](https://universalsoftware.atlassian.net/browse/VAL-260) | Boeing Portland Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-256](https://universalsoftware.atlassian.net/browse/VAL-256) | Polaris Shakopee XDock Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-255](https://universalsoftware.atlassian.net/browse/VAL-255) | Polaris Shakopee XDock UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-253](https://universalsoftware.atlassian.net/browse/VAL-253) | Polaris WH17 Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-251](https://universalsoftware.atlassian.net/browse/VAL-251) | Polaris WH17 UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-250](https://universalsoftware.atlassian.net/browse/VAL-250) | Polaris Huntsville Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-249](https://universalsoftware.atlassian.net/browse/VAL-249) | Polaris Huntsville UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-246](https://universalsoftware.atlassian.net/browse/VAL-246) | Boeing Portland UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-244](https://universalsoftware.atlassian.net/browse/VAL-244) | GM FtWayne Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-243](https://universalsoftware.atlassian.net/browse/VAL-243) | GM FlexNGate Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-242](https://universalsoftware.atlassian.net/browse/VAL-242) | GM FlexNGate UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-211](https://universalsoftware.atlassian.net/browse/VAL-211) | FCA Clark St UAT (XDocks) - Migrate to BitBucket | Not set |
| Value Added  | [VAL-210](https://universalsoftware.atlassian.net/browse/VAL-210) | FCA Georgia UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-208](https://universalsoftware.atlassian.net/browse/VAL-208) | FCA Clark St Production (XDocks) - Migrate to BitBucket | Not set |
| Value Added  | [VAL-194](https://universalsoftware.atlassian.net/browse/VAL-194) | GM Factory Zero RESS UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-193](https://universalsoftware.atlassian.net/browse/VAL-193) | GM Flint Continental Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-192](https://universalsoftware.atlassian.net/browse/VAL-192) | GM Factory Zero Prime UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-190](https://universalsoftware.atlassian.net/browse/VAL-190) | GM Factory Zero RESS Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-189](https://universalsoftware.atlassian.net/browse/VAL-189) | GM Arlington UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-187](https://universalsoftware.atlassian.net/browse/VAL-187) | GM Factory Zero Prime Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-186](https://universalsoftware.atlassian.net/browse/VAL-186) | GM FtWayne UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-185](https://universalsoftware.atlassian.net/browse/VAL-185) | GM Arlington Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-184](https://universalsoftware.atlassian.net/browse/VAL-184) | GM Flint Continental UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-183](https://universalsoftware.atlassian.net/browse/VAL-183) | Navistar San Antonio Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-182](https://universalsoftware.atlassian.net/browse/VAL-182) | FCA Huber Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-181](https://universalsoftware.atlassian.net/browse/VAL-181) | FCA Huber UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-180](https://universalsoftware.atlassian.net/browse/VAL-180) | Westport Axle Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-179](https://universalsoftware.atlassian.net/browse/VAL-179) | Westport Axle UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-178](https://universalsoftware.atlassian.net/browse/VAL-178) | Westport Wheel Tire Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-177](https://universalsoftware.atlassian.net/browse/VAL-177) | Westport Wheel Tire UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-176](https://universalsoftware.atlassian.net/browse/VAL-176) | Westport Seats Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-175](https://universalsoftware.atlassian.net/browse/VAL-175) | Westport Seats UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-174](https://universalsoftware.atlassian.net/browse/VAL-174) | FCA Georgia Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-173](https://universalsoftware.atlassian.net/browse/VAL-173) | Navistar San Antonio UAT - Migrate to BitBucket | Not set |
| Value Added  | [VAL-106](https://universalsoftware.atlassian.net/browse/VAL-106) | GM Arlington Production - Migrate to BitBucket | Not set |
| Value Added  | [VAL-103](https://universalsoftware.atlassian.net/browse/VAL-103) | GM Arlington UAT - Migrate to BitBucket | Not set |
| SubZero  | [SBZ-59](https://universalsoftware.atlassian.net/browse/SBZ-59) | Migrate GFZGT-215 | Not set |
| SubZero  | [SBZ-58](https://universalsoftware.atlassian.net/browse/SBZ-58) | Add Launch Team WMS Accounts Part 2 | Not set |
| SubZero  | [SBZ-43](https://universalsoftware.atlassian.net/browse/SBZ-43) | PPS Printing Concern | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-126](https://universalsoftware.atlassian.net/browse/RLNGTN-126) | 9JS route increase print | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-124](https://universalsoftware.atlassian.net/browse/RLNGTN-124) | [RLNGTN-124] Find discrepancy between ASN received time and user scan report time | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-106](https://universalsoftware.atlassian.net/browse/RLNGTN-106) | Add missing End If in ScannerWebService for Arlington | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-55](https://universalsoftware.atlassian.net/browse/RLNGTN-55) | Convert PrintTrailerReportByDB to use SRSSReportPrintingService.dll | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-54](https://universalsoftware.atlassian.net/browse/RLNGTN-54) | Update CMA EndCap, Inbound Lane, PartNumber Receipt reports to use SRSSReportViewer.aspx | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-53](https://universalsoftware.atlassian.net/browse/RLNGTN-53) | Integrate GM_PPSImporter Task Into Arlington Environment | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-50](https://universalsoftware.atlassian.net/browse/RLNGTN-50) | Migrate the SSRS Report Library Service to Arlington | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-49](https://universalsoftware.atlassian.net/browse/RLNGTN-49) | SSRS Report Viewer Framework Review | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-48](https://universalsoftware.atlassian.net/browse/RLNGTN-48) | Add TrailerNumber + MGO SID to User Scan Report | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-47](https://universalsoftware.atlassian.net/browse/RLNGTN-47) | Find the Outbound Lane Printer management page from Factory Zero and Migrate it here for use | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-46](https://universalsoftware.atlassian.net/browse/RLNGTN-46) | Adjust the Route Management page based on the most recent Factory Zero one | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-45](https://universalsoftware.atlassian.net/browse/RLNGTN-45) | Find the Laser Printer Management Page from Factory Zero and Migrate it here for use | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-44](https://universalsoftware.atlassian.net/browse/RLNGTN-44) | Find the Label Printer Management Page from Factory Zero and Migrate it here for use | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-42](https://universalsoftware.atlassian.net/browse/RLNGTN-42) | Find the ASN Location Management page from Factory Zero and Migrate is here for use | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-41](https://universalsoftware.atlassian.net/browse/RLNGTN-41) | Arlington - Updated Printing Configurations | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-36](https://universalsoftware.atlassian.net/browse/RLNGTN-36) | Add ASNLocations page from Factory Zero to Arlington | Not set |
| Lear Stanley Gault | [LSG-26](https://universalsoftware.atlassian.net/browse/LSG-26) | Ability to create sub total for Master Label | Not set |
| Lear Stanley Gault | [LSG-8](https://universalsoftware.atlassian.net/browse/LSG-8) | WMS Lear - Need to delete order but have started picking | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-235](https://universalsoftware.atlassian.net/browse/GMFZGT-235) | Cycle Count - Purge Page Feature - Drop Down List Bug | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-228](https://universalsoftware.atlassian.net/browse/GMFZGT-228) | Dashboards and Titles | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-112](https://universalsoftware.atlassian.net/browse/GMFZGT-112) | Reinstate un-purge button on the cycle purged page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-109](https://universalsoftware.atlassian.net/browse/GMFZGT-109) | Trailer History | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-108](https://universalsoftware.atlassian.net/browse/GMFZGT-108) | Site Style Updates | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-107](https://universalsoftware.atlassian.net/browse/GMFZGT-107) | Rad Grid Updates | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-98](https://universalsoftware.atlassian.net/browse/GMFZGT-98) | Hot Order Dashboard | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-63](https://universalsoftware.atlassian.net/browse/GMFZGT-63) | Website - Improvements | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-49](https://universalsoftware.atlassian.net/browse/GMFZGT-49) | Move InventoryQty & NonNetQty columns, Remove ContainerType column | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-28](https://universalsoftware.atlassian.net/browse/GMFZGT-28) | Create validation for un-purge page | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-78](https://universalsoftware.atlassian.net/browse/GFCGT-78) | Pulling Scan History | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-76](https://universalsoftware.atlassian.net/browse/GFCGT-76) | Create dashboard to show Inbound Trailers & Lanes | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-72](https://universalsoftware.atlassian.net/browse/GFCGT-72) | Fix incorrect MFG/Ship Dates and Last Scan User set during InventoryOut procedures | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-48](https://universalsoftware.atlassian.net/browse/GFCGT-48) | Completed Orders vs Picked Report - 24 Hour Time | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-45](https://universalsoftware.atlassian.net/browse/GFCGT-45) | Carry these changes to the PartLookup_Alliance version of the page too | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-41](https://universalsoftware.atlassian.net/browse/GFCGT-41) | Desktop - Dashboard - Inbounds | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-30](https://universalsoftware.atlassian.net/browse/GFCGT-30) | Add InventoryDescription to PartLookup Editor | Not set |
| DTNA CMS | [DTCMS-27](https://universalsoftware.atlassian.net/browse/DTCMS-27) | Add Transaction Types to Container Tracking Report | Not set |
| DTNA CMS | [DTCMS-20](https://universalsoftware.atlassian.net/browse/DTCMS-20) | [DTNA] Add comments column to Trailer by Time and Locations report | Not set |
| DTNA CMS | [DTCMS-16](https://universalsoftware.atlassian.net/browse/DTCMS-16) | Cycle Count History - Generate Scores | Not set |
| DTNA CMS | [DTCMS-13](https://universalsoftware.atlassian.net/browse/DTCMS-13) | RMS - Waive ASN Discrepancies | Not set |
| Boeing Portland - General Tickets  | [BNG-21](https://universalsoftware.atlassian.net/browse/BNG-21) | Inventory ASN Scanner Not Working | Not set |
| Boeing Portland - General Tickets  | [BNG-6](https://universalsoftware.atlassian.net/browse/BNG-6) | Update ReleaseInfoGrid page to use new printing DLL | Not set |
| Boeing Portland - General Tickets  | [BNG-5](https://universalsoftware.atlassian.net/browse/BNG-5) | Add Laser Printer Management page to PortlandWMS | Not set |
| Boeing Portland - General Tickets  | [BNG-4](https://universalsoftware.atlassian.net/browse/BNG-4) | Update ReleaseInfoHistory page to use new printing DLL | Not set |
| Boeing Portland - General Tickets  | [BNG-3](https://universalsoftware.atlassian.net/browse/BNG-3) | Update ClearInventoryLocations page to use new printing DLL | Not set |
| Boeing Portland - General Tickets  | [BNG-2](https://universalsoftware.atlassian.net/browse/BNG-2) | Boeing Portland and Portland Spares - Laser Printer Management Page | Not set |
| BMW Spartanburg | [BMWS-423](https://universalsoftware.atlassian.net/browse/BMWS-423) | B10 2D Label Reformat | Not set |
| BMW Spartanburg | [BMWS-412](https://universalsoftware.atlassian.net/browse/BMWS-412) | Outbound Racks Page Improvements | Not set |
| BMW Spartanburg | [BMWS-377](https://universalsoftware.atlassian.net/browse/BMWS-377) | Allow B10 Label Reprint Regardless of Rack Sequencing in Outbound Racks App | Not set |
| BMW Spartanburg | [BMWS-356](https://universalsoftware.atlassian.net/browse/BMWS-356) | Active Testing for New Sequencing Page | Not set |
| BMW Spartanburg | [BMWS-354](https://universalsoftware.atlassian.net/browse/BMWS-354) | Improve Result Messages for Label Generation Methods | Not set |
| BMW Spartanburg | [BMWS-327](https://universalsoftware.atlassian.net/browse/BMWS-327) | Generate Fake Pre-Sequenced ASNs for Bulk/JIS Testing | Not set |
| BMW Spartanburg | [BMWS-295](https://universalsoftware.atlassian.net/browse/BMWS-295) | Vulnerability testing for TranslatorOuput | Not set |
| BMW Spartanburg | [BMWS-293](https://universalsoftware.atlassian.net/browse/BMWS-293) | Bug in stored procedure GetASNDetailsForOBTrailer | Not set |
| BMW Spartanburg | [BMWS-194](https://universalsoftware.atlassian.net/browse/BMWS-194) | Implement the PrintSequenceOrder Endpoint in the Business Service | Not set |
| BMW Spartanburg | [BMWS-146](https://universalsoftware.atlassian.net/browse/BMWS-146) | Outbound ASN Requirements | Not set |
| Alliance Laundry - General Tickets  | [AL-185](https://universalsoftware.atlassian.net/browse/AL-185) | Route Types - No Printer as an Option | Not set |

### Kyle Mundie

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | In Progress | 55 days remaining |
| ✅ | BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | In Progress | 55 days remaining |
| ✅ | BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | In Progress | 55 days remaining |
| ✅ | BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | In Progress | 55 days remaining |
| ✅ | BMW Spartanburg | [BMWS-370](https://universalsoftware.atlassian.net/browse/BMWS-370) | Application Improvements | 2025-08-31 | In Progress | 55 days remaining |
| 📋 | BMW Spartanburg | [BMWS-421](https://universalsoftware.atlassian.net/browse/BMWS-421) | Align on Communication Patterns and Data Structures | Not set | Refining | No due date |
| 📋 | BMW Spartanburg | [BMWS-420](https://universalsoftware.atlassian.net/browse/BMWS-420) | Outline Expected Responses from Web Service | Not set | Refining | No due date |
| 📋 | BMW Spartanburg | [BMWS-419](https://universalsoftware.atlassian.net/browse/BMWS-419) | Define Robot-Initiated Request Payloads | Not set | Backlog | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-349](https://universalsoftware.atlassian.net/browse/BMWS-349) | Sequencing & Order Management | 2025-07-02 |
| BMW Spartanburg | [BMWS-348](https://universalsoftware.atlassian.net/browse/BMWS-348) | Rack & Label Printing | 2025-07-02 |
| BMW Spartanburg | [BMWS-347](https://universalsoftware.atlassian.net/browse/BMWS-347) | Post-Launch Requestions  | 2025-07-02 |
| BMW Spartanburg | [BMWS-346](https://universalsoftware.atlassian.net/browse/BMWS-346) | Error Handling & Logging | 2025-07-02 |
| BMW Spartanburg | [BMWS-345](https://universalsoftware.atlassian.net/browse/BMWS-345) | Inbound/Outbound Processing | 2025-07-02 |
| BMW Spartanburg | [BMWS-344](https://universalsoftware.atlassian.net/browse/BMWS-344) | Report Generation & Analytics | 2025-07-02 |
| BMW Spartanburg | [BMWS-238](https://universalsoftware.atlassian.net/browse/BMWS-238) | Scanner Improvements | 2025-07-02 |
| BMW Spartanburg | [BMWS-171](https://universalsoftware.atlassian.net/browse/BMWS-171) | Implement Idle Timer for Monitoring DCO Signal Processing | 2025-06-05 |
| BMW Spartanburg | [BMWS-202](https://universalsoftware.atlassian.net/browse/BMWS-202) | Rack Status Updates | 2025-05-13 |
| BMW Spartanburg | [BMWS-84](https://universalsoftware.atlassian.net/browse/BMWS-84) | Android OS 13 support  | 2025-05-06 |
| BMW Spartanburg | [BMWS-150](https://universalsoftware.atlassian.net/browse/BMWS-150) | Scanner Outbound | 2025-05-01 |
| BMW Spartanburg | [BMWS-158](https://universalsoftware.atlassian.net/browse/BMWS-158) | Label Creation | 2025-04-28 |
| BMW Spartanburg | [BMWS-3](https://universalsoftware.atlassian.net/browse/BMWS-3) | Implementation of WMS at BMW | 2025-04-28 |
| BMW Spartanburg | [BMWS-78](https://universalsoftware.atlassian.net/browse/BMWS-78) | Implement ASN JIS 50 receiving process | 2025-04-27 |
| BMW Spartanburg | [BMWS-105](https://universalsoftware.atlassian.net/browse/BMWS-105) | Scanner Inbound | 2025-04-07 |

### Ludmil Gueorguiev

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | YMS Project | [YP-122](https://universalsoftware.atlassian.net/browse/YP-122) | Carrier Master update needed for this epic - edit - adding SCAC | 2025-07-11 | Code Complete | 4 days remaining |
| ✅ | YMS Project | [YP-120](https://universalsoftware.atlassian.net/browse/YP-120) | Carrier Master update needed for this epic - adding SCAC into carrier creation | 2025-07-11 | Code Complete | 4 days remaining |
| ✅ | YMS Project | [YP-64](https://universalsoftware.atlassian.net/browse/YP-64) | Attachment column to be added to outbound log | 2025-07-20 | Validated | 13 days remaining |
| ✅ | YMS Project | [YP-20](https://universalsoftware.atlassian.net/browse/YP-20) | Trailer Check In Process | 2025-08-05 | Validated | 29 days remaining |
| 📋 | YMS Project | [YP-146](https://universalsoftware.atlassian.net/browse/YP-146) | In Yard Status Changes - Outbound Closed - at trailer | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-145](https://universalsoftware.atlassian.net/browse/YP-145) | In Yard Status Changes - Empty Options - at trailer | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-144](https://universalsoftware.atlassian.net/browse/YP-144) | In Yard Status Changes - Outbound Loading - at trailer | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-143](https://universalsoftware.atlassian.net/browse/YP-143) | In Yard Status Changes - Inbound Actions - at trailer | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-142](https://universalsoftware.atlassian.net/browse/YP-142) | additions to dock status changes | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-138](https://universalsoftware.atlassian.net/browse/YP-138) | Zone Master - Yard Location search by zone | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-137](https://universalsoftware.atlassian.net/browse/YP-137) | Zone Master - yard location delete  | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-136](https://universalsoftware.atlassian.net/browse/YP-136) | Zone Master - delete zones | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-135](https://universalsoftware.atlassian.net/browse/YP-135) | Zone Master - view yard locations and sort | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-134](https://universalsoftware.atlassian.net/browse/YP-134) | Zone Master - edit yard locations | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-133](https://universalsoftware.atlassian.net/browse/YP-133) | Zone Master - add yard locations | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-132](https://universalsoftware.atlassian.net/browse/YP-132) |  Zone Mater - view zones and sort | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-130](https://universalsoftware.atlassian.net/browse/YP-130) | Need to add zone to action menu | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-129](https://universalsoftware.atlassian.net/browse/YP-129) | Zone Master - adding to database | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-128](https://universalsoftware.atlassian.net/browse/YP-128) | Zone Master - edit zone | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-127](https://universalsoftware.atlassian.net/browse/YP-127) | Zone Master - add zone | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-126](https://universalsoftware.atlassian.net/browse/YP-126) | Menu to add zone master | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-123](https://universalsoftware.atlassian.net/browse/YP-123) | add scac to the display, sort and search by Scac in Carrier Master | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-121](https://universalsoftware.atlassian.net/browse/YP-121) | Zone Master: Main page | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-119](https://universalsoftware.atlassian.net/browse/YP-119) | Add SCAC column to carrier | Not set | Review | No due date |
| 📋 | YMS Project | [YP-105](https://universalsoftware.atlassian.net/browse/YP-105) | Aging Report - advanced filter | Not set | Ready | No due date |
| 📋 | YMS Project | [YP-97](https://universalsoftware.atlassian.net/browse/YP-97) | In Yard Status Changes - Empty Options - at docks | Not set | Test | No due date |
| 📋 | YMS Project | [YP-96](https://universalsoftware.atlassian.net/browse/YP-96) | In Yard Status Changes - Outbound Loading - at docks | Not set | Test | No due date |
| 📋 | YMS Project | [YP-93](https://universalsoftware.atlassian.net/browse/YP-93) | Create a switcher queue policy | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-92](https://universalsoftware.atlassian.net/browse/YP-92) | Research how to accommodate the switcher queue in the database and generate the data structure | Not set | Code Complete | No due date |
| 📋 | YMS Project | [YP-89](https://universalsoftware.atlassian.net/browse/YP-89) | Move Queue Module for Switchers - Main Page | Not set | In Progress | No due date |
| 📋 | YMS Project | [YP-59](https://universalsoftware.atlassian.net/browse/YP-59) | Sorting By Categories - Trailer - Dock Section | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-53](https://universalsoftware.atlassian.net/browse/YP-53) | Outbound Log Download excel changes | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-52](https://universalsoftware.atlassian.net/browse/YP-52) | Ability to edit and save Outbound Log lines | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-51](https://universalsoftware.atlassian.net/browse/YP-51) | Outbound Log Requesed Changes | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-45](https://universalsoftware.atlassian.net/browse/YP-45) |   Move Queue Module for Switchers - Menu | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-35](https://universalsoftware.atlassian.net/browse/YP-35) | Sorting By Categories - Outbound Status Report | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-34](https://universalsoftware.atlassian.net/browse/YP-34) | Sorting By Categories - Outbound Status | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-33](https://universalsoftware.atlassian.net/browse/YP-33) | Sorting By Categories - Aging Report | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-32](https://universalsoftware.atlassian.net/browse/YP-32) | Sorting By Categories - Carrier Report | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-31](https://universalsoftware.atlassian.net/browse/YP-31) | Sorting By Categories - Daily Report | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-30](https://universalsoftware.atlassian.net/browse/YP-30) | Sorting By Categories - Trailer Search Report | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-29](https://universalsoftware.atlassian.net/browse/YP-29) | Sorting By Categories - Outbound Log | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-28](https://universalsoftware.atlassian.net/browse/YP-28) | Sorting By Categories - Inbound Log | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-27](https://universalsoftware.atlassian.net/browse/YP-27) | Sorting By Categories - Trailer - Yard Section | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-26](https://universalsoftware.atlassian.net/browse/YP-26) | Sorting By Categories - Destination Master | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-25](https://universalsoftware.atlassian.net/browse/YP-25) | Sorting By Categories - Carrier Master | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-24](https://universalsoftware.atlassian.net/browse/YP-24) | Sorting By Categories - Docks Master | Not set | Validated | No due date |
| 📋 | YMS Project | [YP-19](https://universalsoftware.atlassian.net/browse/YP-19) | In Yard Status Changes - Inbound Actions - at docks | Not set | Test | No due date |
| 📋 | YMS Project | [YP-18](https://universalsoftware.atlassian.net/browse/YP-18) | Verbiage Changes | Not set | Test | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| AppDev | [AP-33](https://universalsoftware.atlassian.net/browse/AP-33) | Carrier Sync Service | 2025-05-05 |
| YMS Project | [YP-56](https://universalsoftware.atlassian.net/browse/YP-56) | Issue with adding/uploading and saving a picture  | 2025-04-30 |
| YMS Project | [YP-1](https://universalsoftware.atlassian.net/browse/YP-1) | Migration of Repository | 2025-02-07 |
| YMS Project | [YP-70](https://universalsoftware.atlassian.net/browse/YP-70) | Photo evidence not saving to be viewed after uploading | Not set |
| YMS Project | [YP-60](https://universalsoftware.atlassian.net/browse/YP-60) | Not able to view pictures after saving | Not set |
| YMS Project | [YP-49](https://universalsoftware.atlassian.net/browse/YP-49) | Changing YMS to use AWS and not Azure | Not set |
| YMS Project | [YP-48](https://universalsoftware.atlassian.net/browse/YP-48) | Convert Azure Data Storage to AWS | Not set |
| YMS Project | [YP-23](https://universalsoftware.atlassian.net/browse/YP-23) | Sorting By Categories - Supplier Master | Not set |
| WES | [WES-79](https://universalsoftware.atlassian.net/browse/WES-79) | ContainerType - Proposal | Not set |
| WES | [WES-76](https://universalsoftware.atlassian.net/browse/WES-76) | LocationPreferences - Proposal | Not set |
| WES | [WES-74](https://universalsoftware.atlassian.net/browse/WES-74) | Container - Proposal | Not set |
| WES | [WES-69](https://universalsoftware.atlassian.net/browse/WES-69) | PowerUnitHistory - Proposal | Not set |
| WES | [WES-67](https://universalsoftware.atlassian.net/browse/WES-67) | Docks - Proposal | Not set |
| WES | [WES-64](https://universalsoftware.atlassian.net/browse/WES-64) | DockType - Proposal | Not set |
| WES | [WES-46](https://universalsoftware.atlassian.net/browse/WES-46) | Research ticket for docker to run microservices  | Not set |
| AppDev | [AP-38](https://universalsoftware.atlassian.net/browse/AP-38) | Refactor Carrier sync service | Not set |
| AppDev | [AP-7](https://universalsoftware.atlassian.net/browse/AP-7) | Research ticket for ABP.io | Not set |

### Matthew Berryhill

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | BMW Spartanburg | [BMWS-449](https://universalsoftware.atlassian.net/browse/BMWS-449) | Enable "Set Aside" Functionality on Outbound Pre Sequence Page for COS Rack Routing | Not set | In Progress | No due date |
| 📋 | BMW Spartanburg | [BMWS-444](https://universalsoftware.atlassian.net/browse/BMWS-444) | Update Stored Procedures Supporting the 'Sequencing Screen' Process | Not set | Ready | No due date |
| 📋 | BMW Spartanburg | [BMWS-428](https://universalsoftware.atlassian.net/browse/BMWS-428) | Remove Duplicated Serials - 06/30 | Not set | Review | No due date |
| 📋 | BMW Spartanburg | [BMWS-407](https://universalsoftware.atlassian.net/browse/BMWS-407) | SCHEDULE_EXACT_ALARM Permission and Implement Restart Notification | Not set | In Progress | No due date |
| 📋 | BMW Spartanburg | [BMWS-395](https://universalsoftware.atlassian.net/browse/BMWS-395) | Resolve Permission Issues for Android 14 in Xamarin App | Not set | Review | No due date |
| 📋 | BMW Spartanburg | [BMWS-383](https://universalsoftware.atlassian.net/browse/BMWS-383) | Replace NOLOCK with READ UNCOMMITTED in Stored Procedures for OutboundSeq Page | Not set | Ready | No due date |
| 📋 | BMW Spartanburg | [BMWS-214](https://universalsoftware.atlassian.net/browse/BMWS-214) | Add a list of reason codes to the skipped feature  | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| DTNA RFID | [DR-2](https://universalsoftware.atlassian.net/browse/DR-2) | Test RFID scanner for compatibility | 2025-06-20 |
| BMW Spartanburg | [BMWS-381](https://universalsoftware.atlassian.net/browse/BMWS-381) | Normalize BCA Slot Values for Scanning Comparison | 2025-06-17 |
| WMS Android Future Features | [WAFF-59](https://universalsoftware.atlassian.net/browse/WAFF-59) | Need support for emulators to receive the scan intent | Not set |
| WMS Android Future Features | [WAFF-16](https://universalsoftware.atlassian.net/browse/WAFF-16) | Scan History Module | Not set |
| Value Added  | [VAL-440](https://universalsoftware.atlassian.net/browse/VAL-440) | [FCAHuberProd] Fix shift productivity report to show the proper end times of shift | Not set |
| Value Added  | [VAL-439](https://universalsoftware.atlassian.net/browse/VAL-439) | [FCAHuber] KPIMetric bug for Inbound%Reciept | Not set |
| Value Added  | [VAL-432](https://universalsoftware.atlassian.net/browse/VAL-432) | [Arlington_GetShiftStartAndEnd] runtime SQL error with adding time to a datetime | Not set |
| Value Added  | [VAL-376](https://universalsoftware.atlassian.net/browse/VAL-376) | [SubZero] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-374](https://universalsoftware.atlassian.net/browse/VAL-374) | [GMFlexNGate] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-373](https://universalsoftware.atlassian.net/browse/VAL-373) | [GMFactoryZero] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-372](https://universalsoftware.atlassian.net/browse/VAL-372) | [GMArlington] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-371](https://universalsoftware.atlassian.net/browse/VAL-371) | [GMFtWayne] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-370](https://universalsoftware.atlassian.net/browse/VAL-370) | [FCAGeorgia] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-369](https://universalsoftware.atlassian.net/browse/VAL-369) | [FCAHuber] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-352](https://universalsoftware.atlassian.net/browse/VAL-352) | [FlintContinental] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-294](https://universalsoftware.atlassian.net/browse/VAL-294) | Migrate/Identify the required repositories in bit bucket | Not set |
| Value Added  | [VAL-276](https://universalsoftware.atlassian.net/browse/VAL-276) | TC8300 New Hardware Model - TC83BH | Not set |
| Value Added  | [VAL-191](https://universalsoftware.atlassian.net/browse/VAL-191) | [AllianceV2] Update reports and supporting SQL for KPI metrics | Not set |
| Value Added  | [VAL-50](https://universalsoftware.atlassian.net/browse/VAL-50) | [FCAHuber] kpiMetrics Improve report metrics groups | Not set |
| Value Added  | [VAL-46](https://universalsoftware.atlassian.net/browse/VAL-46) | [Subzero] Update KPI SQL with FCAHuber changes | Not set |
| Value Added  | [VAL-45](https://universalsoftware.atlassian.net/browse/VAL-45) | [GMFactoryZero] Update KPI SQL with FCAHuber changes | Not set |
| Value Added  | [VAL-44](https://universalsoftware.atlassian.net/browse/VAL-44) | [AllianceV2] Update KPI SQL with FCAHuber changes | Not set |
| Value Added  | [VAL-43](https://universalsoftware.atlassian.net/browse/VAL-43) | [FlintConti] Update KPI SQL with FCAHuber changes | Not set |
| Value Added  | [VAL-35](https://universalsoftware.atlassian.net/browse/VAL-35) | [FCAHuber] TelerikReporting UserProductivityReport allow up to 12 hour shifts. | Not set |
| Value Added  | [VAL-13](https://universalsoftware.atlassian.net/browse/VAL-13) | KPI Report - Update Shift Schedules | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-122](https://universalsoftware.atlassian.net/browse/RLNGTN-122) | WMS Productivity By Shift Reports | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-93](https://universalsoftware.atlassian.net/browse/RLNGTN-93) | Backfill missing kpi data | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-15](https://universalsoftware.atlassian.net/browse/RLNGTN-15) | CLOC Feature - Putaway | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-36](https://universalsoftware.atlassian.net/browse/GMFZGT-36) | Change Date field on Productivity by shift by user report WMS | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-75](https://universalsoftware.atlassian.net/browse/GFCGT-75) | KPI Metric Reports | Not set |
| BMW Spartanburg | [BMWS-424](https://universalsoftware.atlassian.net/browse/BMWS-424) | B10 U-prefix support on Outbound Presequence Page | Not set |
| BMW Spartanburg | [BMWS-401](https://universalsoftware.atlassian.net/browse/BMWS-401) | Add Supervisor Scan Prompt for Printing on OutboundPreSeq Page | Not set |
| BMW Spartanburg | [BMWS-369](https://universalsoftware.atlassian.net/browse/BMWS-369) | WMS Scanner shows Permissions required | Not set |
| BMW Spartanburg | [BMWS-368](https://universalsoftware.atlassian.net/browse/BMWS-368) | Make Reorder Racks More Noticeable in Rack Selection List | Not set |
| BMW Spartanburg | [BMWS-367](https://universalsoftware.atlassian.net/browse/BMWS-367) | Add Support for Scanning to Cells | Not set |
| BMW Spartanburg | [BMWS-361](https://universalsoftware.atlassian.net/browse/BMWS-361) | Change Inbound Location Text Color to Red if Item Is Not in Inventory | Not set |
| BMW Spartanburg | [BMWS-360](https://universalsoftware.atlassian.net/browse/BMWS-360) | Remove Print Rack Option if Preference Is Set to Print After Rack Completion | Not set |
| BMW Spartanburg | [BMWS-359](https://universalsoftware.atlassian.net/browse/BMWS-359) | Add Supervisor Requirement to Print Options | Not set |
| BMW Spartanburg | [BMWS-358](https://universalsoftware.atlassian.net/browse/BMWS-358) | Add Supervisor Requirement to Skip Processes | Not set |
| BMW Spartanburg | [BMWS-337](https://universalsoftware.atlassian.net/browse/BMWS-337) | b10 starter label needs to be scanned on the outbound pre sequence page | Not set |
| BMW Spartanburg | [BMWS-332](https://universalsoftware.atlassian.net/browse/BMWS-332) | Update Outbound Sequencing page to prevent skips | Not set |
| BMW Spartanburg | [BMWS-326](https://universalsoftware.atlassian.net/browse/BMWS-326) | Improve Error Logging on the Outbound Pre-Sequence Page | Not set |
| BMW Spartanburg | [BMWS-315](https://universalsoftware.atlassian.net/browse/BMWS-315) | Remove B10 Micro Label Printing from Outbound Presequencing Page | Not set |
| BMW Spartanburg | [BMWS-314](https://universalsoftware.atlassian.net/browse/BMWS-314) | Add Print Rack Sheet Functionality to Outbound Pre-Sequencing App | Not set |
| BMW Spartanburg | [BMWS-313](https://universalsoftware.atlassian.net/browse/BMWS-313) | Add Scan History to Outbound Presequencing Page | Not set |
| BMW Spartanburg | [BMWS-312](https://universalsoftware.atlassian.net/browse/BMWS-312) | Enable Bulk Orders on Outbound Pre-Sequencing Page | Not set |
| BMW Spartanburg | [BMWS-273](https://universalsoftware.atlassian.net/browse/BMWS-273) | Scanner UI Changes  | Not set |
| BMW Spartanburg | [BMWS-240](https://universalsoftware.atlassian.net/browse/BMWS-240) | Query is misreporting the inbound racks | Not set |
| BMW Spartanburg | [BMWS-203](https://universalsoftware.atlassian.net/browse/BMWS-203) | Modify CompleteOutbound SequenceOrder to Update Overpack Value on Inventory Details | Not set |
| BMW Spartanburg | [BMWS-196](https://universalsoftware.atlassian.net/browse/BMWS-196) | Update the Sequencing Page to Accommodate Rack Status Changes | Not set |
| BMW Spartanburg | [BMWS-151](https://universalsoftware.atlassian.net/browse/BMWS-151) | Outbound sequencing page | Not set |
| BMW Spartanburg | [BMWS-120](https://universalsoftware.atlassian.net/browse/BMWS-120) | Pre-sequenced Material Scanner (front-end) | Not set |
| BMW Spartanburg | [BMWS-85](https://universalsoftware.atlassian.net/browse/BMWS-85) | Identify and Fix Android OS 13 Compatibility Issues | Not set |

### Mike Semanson

#### In Progress Items

_No items in progress._

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Flint - Torrey - General Tickets  | [FLIN-5](https://universalsoftware.atlassian.net/browse/FLIN-5) | Update Distribution List for GETMENLOSCANSBYDATE Report | 2025-06-24 |
| SubZero  | [SBZ-2](https://universalsoftware.atlassian.net/browse/SBZ-2) | Create EDI File Share For Inbound Files | 2024-10-23 |
| GM ODC Launch  | [GMDCL-60](https://universalsoftware.atlassian.net/browse/GMDCL-60) | Complete low-level testing on CreateManifest in Debug Environment | 2024-10-11 |
| SubZero  | [SBZ-1](https://universalsoftware.atlassian.net/browse/SBZ-1) | Configure Servers For Launch Support | 2024-09-17 |
| GM - Factory Zero - General Tickets  | [GMFZGT-208](https://universalsoftware.atlassian.net/browse/GMFZGT-208) | MGO Status Updater Failing - Error Occurred While Parsing EntityName | 2024-07-31 |
| GM ODC Launch  | [GMDCL-4](https://universalsoftware.atlassian.net/browse/GMDCL-4) | The ODC has requested help in supporting Hazardous Material in XDock | 2024-03-10 |
| GM - Factory Zero - General Tickets  | [GMFZGT-5](https://universalsoftware.atlassian.net/browse/GMFZGT-5) | Standup WMS Dashboard Instance | 2024-03-03 |
| GM ODC Launch  | [GMDCL-9](https://universalsoftware.atlassian.net/browse/GMDCL-9) | Launch Setup | 2023-09-14 |
| GM ODC Launch  | [GMDCL-3](https://universalsoftware.atlassian.net/browse/GMDCL-3) | Develop EDI Importers | 2023-08-27 |
| GM ODC Launch  | [GMDCL-1](https://universalsoftware.atlassian.net/browse/GMDCL-1) | XDock Web Application Development | 2023-08-27 |
| GM ODC Launch  | [GMDCL-5](https://universalsoftware.atlassian.net/browse/GMDCL-5) | SSRS Reports | 2023-08-09 |
| GM ODC Launch  | [GMDCL-7](https://universalsoftware.atlassian.net/browse/GMDCL-7) | Requirements Gathering | 2023-08-06 |
| FCA XDock App Rewrite  | [FCXDR-118](https://universalsoftware.atlassian.net/browse/FCXDR-118) | Sync Database Changes | 2023-05-01 |
| FCA XDock App Rewrite  | [FCXDR-117](https://universalsoftware.atlassian.net/browse/FCXDR-117) | Sync Application Code | 2023-05-01 |
| FCA XDock App Rewrite  | [FCXDR-95](https://universalsoftware.atlassian.net/browse/FCXDR-95) | XDock FindASN Page Isn't Showing All ASNs | 2023-04-24 |
| FCA XDock App Rewrite  | [FCXDR-113](https://universalsoftware.atlassian.net/browse/FCXDR-113) | Optimize and Improve Stored Proc XDock_GetAvailablePlantCodes | 2023-04-21 |
| Value Added  | [VAL-52](https://universalsoftware.atlassian.net/browse/VAL-52) | Flint Torrey Road Willow Run trailer creation - Bug Fix For Existing/Provided Trailer Numbers | 2023-04-19 |
| Value Added  | [VAL-444](https://universalsoftware.atlassian.net/browse/VAL-444) | GenericExcelBulkImporter Inserting Bad Data From Empty CSV Files | Not set |
| Value Added  | [VAL-435](https://universalsoftware.atlassian.net/browse/VAL-435) | IMAP Downloader - Delete Emails Exception | Not set |
| Value Added  | [VAL-207](https://universalsoftware.atlassian.net/browse/VAL-207) | GenericExcelBulkImporter - Add Code To Create Database Table If It Doesn't Exist | Not set |
| Value Added  | [VAL-75](https://universalsoftware.atlassian.net/browse/VAL-75) | GenericExcelBulkImporter - Add Support For CSV Files | Not set |
| Value Added  | [VAL-51](https://universalsoftware.atlassian.net/browse/VAL-51) | FCA XDock Silverlight Rewrite - Door/Zone Status Report - Missing Functionality - Need to be able to remove part from zone | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-86](https://universalsoftware.atlassian.net/browse/TAMA-86) | Submit Solar Winds Ticket | Not set |
| SubZero  | [SBZ-78](https://universalsoftware.atlassian.net/browse/SBZ-78) | Copy SSRS Reports From Factory Zero as Startup Template | Not set |
| SubZero  | [SBZ-77](https://universalsoftware.atlassian.net/browse/SBZ-77) | Copy and mount the Factory Zero Database on Sub-Zero server | Not set |
| SubZero  | [SBZ-47](https://universalsoftware.atlassian.net/browse/SBZ-47) | Configure database server | Not set |
| SubZero  | [SBZ-46](https://universalsoftware.atlassian.net/browse/SBZ-46) | Host WMS in IIS | Not set |
| SubZero  | [SBZ-45](https://universalsoftware.atlassian.net/browse/SBZ-45) | Configure application server | Not set |
| Nissan SLP XDock  | [NSLPX-1](https://universalsoftware.atlassian.net/browse/NSLPX-1) | Not All RAN Weights From Routing Team Available To XDock App | Not set |
| Lear Stanley Gault | [LSG-27](https://universalsoftware.atlassian.net/browse/LSG-27) | Late posting ASN issue | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-240](https://universalsoftware.atlassian.net/browse/GMFZGT-240) | Report - CurrentInventory_Basic - Missing Parts | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-234](https://universalsoftware.atlassian.net/browse/GMFZGT-234) | Inventory Qty / NonNet Qty Calculations - Purge Cycle Count | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-233](https://universalsoftware.atlassian.net/browse/GMFZGT-233) | MGO Status Updater - Failing to Commit Logfile When Nothing To Process | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-184](https://universalsoftware.atlassian.net/browse/GMFZGT-184) | Add Flag ASN Hot Pages from FCA Mack | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-182](https://universalsoftware.atlassian.net/browse/GMFZGT-182) | Add Non Conforming Parts Pages From FCA Mack | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-157](https://universalsoftware.atlassian.net/browse/GMFZGT-157) | Configure WMS as needed to reference GM Factory Zero | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-156](https://universalsoftware.atlassian.net/browse/GMFZGT-156) | Copy the GM Flint Continental SSRS reports to the Factory Zero pre-launch location and configure for new database | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-155](https://universalsoftware.atlassian.net/browse/GMFZGT-155) | Convert GM Flint Continental Open Orders page so it uses the best features from FCA Mack in a more generic way | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-144](https://universalsoftware.atlassian.net/browse/GMFZGT-144) | Setup Inventory Recon report | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-131](https://universalsoftware.atlassian.net/browse/GMFZGT-131) | Implement SeqWeb Consumption Data Importer - ProcessSequence Process | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-130](https://universalsoftware.atlassian.net/browse/GMFZGT-130) | Remove all references to dead pages or old UI components | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-61](https://universalsoftware.atlassian.net/browse/GMFZGT-61) | Partlookup - Shipped Orders - Change Sort Order of Displayed Data | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-51](https://universalsoftware.atlassian.net/browse/GMFZGT-51) | Modify Arl_PPS_Importer To Pull Assigned Printer IP By RouteName | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-50](https://universalsoftware.atlassian.net/browse/GMFZGT-50) | Remove old DH Specific Logic from PPS Importer | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-20](https://universalsoftware.atlassian.net/browse/GMFZGT-20) | MGOStatusUpdater Logging Requests/Responses For Interfaces That Are Disabled | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-4](https://universalsoftware.atlassian.net/browse/GMFZGT-4) | Standup WMS For Pilot Work | Not set |
| GM ODC Launch  | [GMDCL-73](https://universalsoftware.atlassian.net/browse/GMDCL-73) | Rewrite importer to use SQL Bulk Copy to improve performance. | Not set |
| GM ODC Launch  | [GMDCL-72](https://universalsoftware.atlassian.net/browse/GMDCL-72) | Rewrite importer to use SQL Bulk Copy to improve performance. | Not set |
| GM ODC Launch  | [GMDCL-71](https://universalsoftware.atlassian.net/browse/GMDCL-71) | ExcelBulkImporter - Fix Logging Statement For Archived Files | Not set |
| GM ODC Launch  | [GMDCL-69](https://universalsoftware.atlassian.net/browse/GMDCL-69) | Create CutTime Lookup Table Structure | Not set |
| GM ODC Launch  | [GMDCL-68](https://universalsoftware.atlassian.net/browse/GMDCL-68) | Create SSIS Importer For Routing Team Data | Not set |
| GM ODC Launch  | [GMDCL-66](https://universalsoftware.atlassian.net/browse/GMDCL-66) | Setup DB Maintenance Plans | Not set |
| GM ODC Launch  | [GMDCL-64](https://universalsoftware.atlassian.net/browse/GMDCL-64) | Data Outputs | Not set |
| GM ODC Launch  | [GMDCL-58](https://universalsoftware.atlassian.net/browse/GMDCL-58) | Standup FCA XDock Version of XDock Application | Not set |
| GM ODC Launch  | [GMDCL-56](https://universalsoftware.atlassian.net/browse/GMDCL-56) | Import HazMat Info Into Database | Not set |
| GM ODC Launch  | [GMDCL-51](https://universalsoftware.atlassian.net/browse/GMDCL-51) | Create MasterPartsList Importer | Not set |
| GM ODC Launch  | [GMDCL-50](https://universalsoftware.atlassian.net/browse/GMDCL-50) | DELFOR Importer | Not set |
| GM ODC Launch  | [GMDCL-49](https://universalsoftware.atlassian.net/browse/GMDCL-49) | DELJIT Importer | Not set |
| GM ODC Launch  | [GMDCL-48](https://universalsoftware.atlassian.net/browse/GMDCL-48) | Deploy FCA SSRS Reports | Not set |
| GM ODC Launch  | [GMDCL-47](https://universalsoftware.atlassian.net/browse/GMDCL-47) | Add Error Handling and Email Notification To GenericExcelBulkImporter Solution | Not set |
| GM ODC Launch  | [GMDCL-46](https://universalsoftware.atlassian.net/browse/GMDCL-46) | DESADV Importer | Not set |
| GM ODC Launch  | [GMDCL-42](https://universalsoftware.atlassian.net/browse/GMDCL-42) | ASN Auto-Link To DailyRoute Not Working During ASN Import | Not set |
| FCA XDock App Rewrite  | [FCXDR-116](https://universalsoftware.atlassian.net/browse/FCXDR-116) | Packing Slip Quantity Modification Needs To Update Status | Not set |
| FCA XDock App Rewrite  | [FCXDR-114](https://universalsoftware.atlassian.net/browse/FCXDR-114) | Test Issue | Not set |
| FCA XDock App Rewrite  | [FCXDR-112](https://universalsoftware.atlassian.net/browse/FCXDR-112) | FCA XDock Silverlight Rewrite - Need To Clear Inbound Late Arrival Code Link When Setting To Pending | Not set |
| FCA Saltillo Launch Support  | [FCSTL-20](https://universalsoftware.atlassian.net/browse/FCSTL-20) | Develop new SSIS package that runs on the CIT server to copy OutboundRouteAssign info from all XDocks | Not set |
| FCA Saltillo Launch Support  | [FCSTL-19](https://universalsoftware.atlassian.net/browse/FCSTL-19) | Details to be implemented 2 | Not set |
| FCA Saltillo Launch Support  | [FCSTL-18](https://universalsoftware.atlassian.net/browse/FCSTL-18) | Copy CMS From Different XDock | Not set |
| FCA Saltillo Launch Support  | [FCSTL-17](https://universalsoftware.atlassian.net/browse/FCSTL-17) | Setup DB Maintenance Backup Jobs | Not set |
| FCA Saltillo Launch Support  | [FCSTL-16](https://universalsoftware.atlassian.net/browse/FCSTL-16) | Deploy XDock SSRS Reports | Not set |
| FCA Saltillo Launch Support  | [FCSTL-13](https://universalsoftware.atlassian.net/browse/FCSTL-13) | Deploy CMS SSRS Reports | Not set |
| FCA Saltillo Launch Support  | [FCSTL-12](https://universalsoftware.atlassian.net/browse/FCSTL-12) | Setup SSIS Import/Export Packages For CIT Integration | Not set |
| FCA Saltillo Launch Support  | [FCSTL-11](https://universalsoftware.atlassian.net/browse/FCSTL-11) | Configure Saltillo XDock Application For Saltillo Use | Not set |
| FCA Saltillo Launch Support  | [FCSTL-10](https://universalsoftware.atlassian.net/browse/FCSTL-10) | Copy Toluca XDock Database To Saltillo Server | Not set |
| FCA Saltillo Launch Support  | [FCSTL-9](https://universalsoftware.atlassian.net/browse/FCSTL-9) | Configure ASN Importer | Not set |
| FCA Saltillo Launch Support  | [FCSTL-7](https://universalsoftware.atlassian.net/browse/FCSTL-7) | Copy Toluca XDock Instance To Saltillo Server | Not set |
| FCA Saltillo Launch Support  | [FCSTL-6](https://universalsoftware.atlassian.net/browse/FCSTL-6) | Configure 824 Importer | Not set |
| FCA Saltillo Launch Support  | [FCSTL-5](https://universalsoftware.atlassian.net/browse/FCSTL-5) | Configure ASN Generator | Not set |
| FCA Saltillo Launch Support  | [FCSTL-1](https://universalsoftware.atlassian.net/browse/FCSTL-1) | XDock Web Application Stand-Up | Not set |
| FCA Import Tool | [FCAIT-10](https://universalsoftware.atlassian.net/browse/FCAIT-10) | CIT - Remove UserRole Requirement for Login | Not set |
| FCA Import Tool | [FCAIT-9](https://universalsoftware.atlassian.net/browse/FCAIT-9) | Add Support For Maintaining StdContainers Table Directly in CIT | Not set |
| FCA Import Tool | [FCAIT-8](https://universalsoftware.atlassian.net/browse/FCAIT-8) | Integrate Boris's A/C/D Flag logic fix | Not set |
| FCA Import Tool | [FCAIT-7](https://universalsoftware.atlassian.net/browse/FCAIT-7) | CIT File Import Broken For WeeklyAddRequest Files | Not set |
| FCA Import Tool | [FCAIT-6](https://universalsoftware.atlassian.net/browse/FCAIT-6) | CIT - Add/Change/Delete Issue After Latest Update | Not set |
| FCA Import Tool | [FCAIT-5](https://universalsoftware.atlassian.net/browse/FCAIT-5) | CIT Isn't Generating Output for Add/Change/Delete Records | Not set |
| FCA Import Tool | [FCAIT-4](https://universalsoftware.atlassian.net/browse/FCAIT-4) | Can't delete Routed Suppliers from Routes | Not set |
| FCA Import Tool | [FCAIT-3](https://universalsoftware.atlassian.net/browse/FCAIT-3) | CIT - Edit Routed Suppliers Bug | Not set |
| FCA Import Tool | [FCAIT-2](https://universalsoftware.atlassian.net/browse/FCAIT-2) | CIT Handles Deleting of Suppliers Differently for 00176, 00189 and 00276 | Not set |
| FCA Import Tool | [FCAIT-1](https://universalsoftware.atlassian.net/browse/FCAIT-1) | CIT Doesn't Allow Editing Suppliers For Saltillo XDock | Not set |
| CNH Racine | [CNHR-59](https://universalsoftware.atlassian.net/browse/CNHR-59) | Remove Invalid Serial Numbers from WMS Inventory | Not set |
| BMW Spartanburg | [BMWS-90](https://universalsoftware.atlassian.net/browse/BMWS-90) | Implement Outbound Trailer Manifest & ASN Generation | Not set |
| BMW Spartanburg | [BMWS-82](https://universalsoftware.atlassian.net/browse/BMWS-82) | Implement Sequencing from Inbound to Outbound Racks | Not set |
| BMW Spartanburg | [BMWS-81](https://universalsoftware.atlassian.net/browse/BMWS-81) | Implement ASN JIS 50 Replenishment to CLOC | Not set |

### Rakhi Garg

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | Westport - WheelTire - General Tickets  | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | 2025-06-11 | Validated | 26 days overdue |
| 📋 | GM - Factory Zero - General Tickets  | [GMFZGT-285](https://universalsoftware.atlassian.net/browse/GMFZGT-285) | Add Feature to View Completed but Not Dispatched Orders | Not set | Review | No due date |
| 📋 | GM - Factory Zero - General Tickets  | [GMFZGT-271](https://universalsoftware.atlassian.net/browse/GMFZGT-271) | 2025-02-13 - Schema Investigation | Not set | Code Complete | No due date |
| 📋 | BMW Spartanburg | [BMWS-438](https://universalsoftware.atlassian.net/browse/BMWS-438) | Update Stored Procedures Supporting the 'Reports' Process | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Westport - Seats - General Tickets  | [WSSG-42](https://universalsoftware.atlassian.net/browse/WSSG-42) | Report sent for seats inventory feed status | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-261](https://universalsoftware.atlassian.net/browse/GMFZGT-261) | Obsolete Column on Partlookup needs to be defaulted to no filter | Not set |
| BMW Spartanburg | [BMWS-389](https://universalsoftware.atlassian.net/browse/BMWS-389) | Reorders not appearing | Not set |
| BMW Spartanburg | [BMWS-291](https://universalsoftware.atlassian.net/browse/BMWS-291) | Adjust SQL Updates for Box Move and Departure Processes | Not set |
| BMW Spartanburg | [BMWS-266](https://universalsoftware.atlassian.net/browse/BMWS-266) | SWET Dashboard - Pre-Sequenced | Not set |
| BMW Spartanburg | [BMWS-255](https://universalsoftware.atlassian.net/browse/BMWS-255) | HVAC Inbound Closed Without All Racks Scanned (Rack 205), Add Function to WMS to Reopen Inbound | Not set |
| BMW Spartanburg | [BMWS-252](https://universalsoftware.atlassian.net/browse/BMWS-252) | Timeouts on RePrinting Paperwork from Trailer History Outbound | Not set |
| BMW Spartanburg | [BMWS-233](https://universalsoftware.atlassian.net/browse/BMWS-233) | Archive a 'Reprinted Rack' | Not set |
| BMW Spartanburg | [BMWS-232](https://universalsoftware.atlassian.net/browse/BMWS-232) | Remove 'Depart Trailers Filter By Location' IF No Inventory is Present / Sort Chronologically if Present | Not set |
| BMW Spartanburg | [BMWS-223](https://universalsoftware.atlassian.net/browse/BMWS-223) | Status update for outbound rack | Not set |
| BMW Spartanburg | [BMWS-221](https://universalsoftware.atlassian.net/browse/BMWS-221) | Outbound History and Trailer Departure Changes | Not set |
| BMW Spartanburg | [BMWS-212](https://universalsoftware.atlassian.net/browse/BMWS-212) | Improve the logging for marking box moves | Not set |
| BMW Spartanburg | [BMWS-205](https://universalsoftware.atlassian.net/browse/BMWS-205) | Modify the outbound trailer departure page | Not set |
| BMW Spartanburg | [BMWS-201](https://universalsoftware.atlassian.net/browse/BMWS-201) | Prevent deleting of laser printers from the printer management table | Not set |
| BMW Spartanburg | [BMWS-134](https://universalsoftware.atlassian.net/browse/BMWS-134) | Updates to Trailer Status page | Not set |
| BMW Spartanburg | [BMWS-89](https://universalsoftware.atlassian.net/browse/BMWS-89) | Implement Outbound Trailer Creation | Not set |
| BMW Spartanburg | [BMWS-45](https://universalsoftware.atlassian.net/browse/BMWS-45) | Dynamic Radgrid Research | Not set |
| BMW Spartanburg | [BMWS-34](https://universalsoftware.atlassian.net/browse/BMWS-34) | Inbound Trailer Status Page - Clear Scans and Delete Serial Features | Not set |
| BMW Spartanburg | [BMWS-29](https://universalsoftware.atlassian.net/browse/BMWS-29) | Implement real-time tracking dashboards for EDI transactions | Not set |
| BMW Spartanburg | [BMWS-5](https://universalsoftware.atlassian.net/browse/BMWS-5) | Component (raw) material availability  | Not set |
| Alliance Laundry - General Tickets  | [AL-272](https://universalsoftware.atlassian.net/browse/AL-272) | Search Bar Feature | Not set |

### Ryan Martinek

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 📋 | FoxproRewrite | [FOX-4](https://universalsoftware.atlassian.net/browse/FOX-4) | Document Login Screen | Not set | In Progress | No due date |
| 📋 | FoxproRewrite | [FOX-2](https://universalsoftware.atlassian.net/browse/FOX-2) | meet with business for foxpro process discovery | Not set | In Progress | No due date |

### Sean Hogg

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| 🚨 | Fleet Tracker | [FLEET-56](https://universalsoftware.atlassian.net/browse/FLEET-56) | Asset detail - Side-panel Features (Cancel & Redomicile) | 2025-07-07 | In Progress | Due today |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| Fleet Tracker | [FLEET-105](https://universalsoftware.atlassian.net/browse/FLEET-105) | Add Readme to main branch. | 2025-07-01 |
| AppDev | [AP-74](https://universalsoftware.atlassian.net/browse/AP-74) | DMS Document handling - Compression & Load Times | 2025-06-27 |

### Shayne Vallad

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | In Progress | 55 days remaining |
| 📋 | WMS 3.0 Research  | [YJ3-29](https://universalsoftware.atlassian.net/browse/YJ3-29) | Inventory Detail - Create Record | Not set | Refining | No due date |
| 📋 | WMS 3.0 Research  | [YJ3-15](https://universalsoftware.atlassian.net/browse/YJ3-15) | Trailers Page | Not set | Refining | No due date |
| 📋 | WMS 3.0 Research  | [YJ3-13](https://universalsoftware.atlassian.net/browse/YJ3-13) | Inventory Detail - Update Location | Not set | Refining | No due date |
| 📋 | WMS 3.0 Research  | [YJ3-12](https://universalsoftware.atlassian.net/browse/YJ3-12) | Inventory Detail - Update Quantity | Not set | Refining | No due date |
| 📋 | WMS 3.0 Research  | [YJ3-3](https://universalsoftware.atlassian.net/browse/YJ3-3) | WMS 3.0 - Lear Ford Steering - API Layer Calls | Not set | Ready | No due date |
| 📋 | WMS 3.0 Research  | [YJ3-1](https://universalsoftware.atlassian.net/browse/YJ3-1) | WMS 3.0 - Lear Ford Steering Conversion | Not set | Ready | No due date |
| 📋 | Westport - WheelTire - General Tickets  | [WSWTGT-47](https://universalsoftware.atlassian.net/browse/WSWTGT-47) | Support TPMS Sensor | Not set | In Progress | No due date |
| 📋 | Westport - Painted Parts - General Tickets   | [WSPPGT-32](https://universalsoftware.atlassian.net/browse/WSPPGT-32) | Implement New Requested File Format | Not set | Refining | No due date |
| 📋 | Value Added  | [VAL-445](https://universalsoftware.atlassian.net/browse/VAL-445) | Change the BOL for Seats, Painted Parts, and Wheel Tire | Not set | In Progress | No due date |
| 📋 | Value Added  | [VAL-9](https://universalsoftware.atlassian.net/browse/VAL-9) | General support between security and Value-Added WMS development team to patch vulnerabilities in our applications | Not set | Ready | No due date |
| 📋 | Navistar San Antonio | [NVSSA-11](https://universalsoftware.atlassian.net/browse/NVSSA-11) | Navistar - Job 699849 Issue | Not set | Backlog | No due date |
| 📋 | Navistar San Antonio | [NVSSA-9](https://universalsoftware.atlassian.net/browse/NVSSA-9) | Navistar - Laser Printing Process Issue | Not set | Backlog | No due date |
| 📋 | Navistar San Antonio | [NVSSA-5](https://universalsoftware.atlassian.net/browse/NVSSA-5) | Navistar - Printer Managment Page | Not set | Backlog | No due date |
| 📋 | GM - Factory Zero - General Tickets  | [GMFZGT-8](https://universalsoftware.atlassian.net/browse/GMFZGT-8) | Efforts for cleaning up SQL and dead code found in the app | Not set | In Progress | No due date |
| 📋 | GM Flint Torrey Rd | [GMFNT-81](https://universalsoftware.atlassian.net/browse/GMFNT-81) | Carload - Data Collision Issue | Not set | In Progress | No due date |
| 📋 | CNH Goodfield | [CNHG-2](https://universalsoftware.atlassian.net/browse/CNHG-2) | Goodfield DMZ - Website and Reporting Service Connection | Not set | Refining | No due date |
| 📋 | Boeing Portland - General Tickets  | [BNG-23](https://universalsoftware.atlassian.net/browse/BNG-23) | Portland WMS - Label Printing Research Task | Not set | Backlog | No due date |
| 📋 | BMW Spartanburg | [BMWS-425](https://universalsoftware.atlassian.net/browse/BMWS-425) | Inbound issue | Not set | Refining | No due date |
| 📋 | BMW Spartanburg | [BMWS-405](https://universalsoftware.atlassian.net/browse/BMWS-405) | BMW - Packout Old Data Cleanup | Not set | Review | No due date |
| 📋 | BMW Spartanburg | [BMWS-83](https://universalsoftware.atlassian.net/browse/BMWS-83) | Implement Set-Aside tracking for BMW Adjustments | Not set | Refining | No due date |
| 📋 | BMW Spartanburg | [BMWS-71](https://universalsoftware.atlassian.net/browse/BMWS-71) | Orders table improvements | Not set | Backlog | No due date |
| 📋 | Alliance Laundry - General Tickets  | [AL-31](https://universalsoftware.atlassian.net/browse/AL-31) | Alliance Laundry V2 Migration Cleanup | Not set | In Progress | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| BMW Spartanburg | [BMWS-388](https://universalsoftware.atlassian.net/browse/BMWS-388) | BMW - DCO Trigger Issues | 2025-06-19 |
| BMW Spartanburg | [BMWS-364](https://universalsoftware.atlassian.net/browse/BMWS-364) | Juneteenth JIS 50 Bulk | 2025-06-13 |
| BMW Spartanburg | [BMWS-36](https://universalsoftware.atlassian.net/browse/BMWS-36) | WMS Updates for Launch Support | 2025-04-27 |
| SubZero  | [SBZ-94](https://universalsoftware.atlassian.net/browse/SBZ-94) | Android Operating System 11 - SSL Certificate Issue WMS | 2024-10-28 |
| SubZero  | [SBZ-4](https://universalsoftware.atlassian.net/browse/SBZ-4) | Launch Support for Sub Zero | 2024-10-04 |
| SubZero  | [SBZ-41](https://universalsoftware.atlassian.net/browse/SBZ-41) | Import Starting Inventory File | 2024-09-20 |
| Alliance Laundry - General Tickets  | [AL-70](https://universalsoftware.atlassian.net/browse/AL-70) | Alliance Laundry Expansion | 2024-04-15 |
| Value Added  | [VAL-319](https://universalsoftware.atlassian.net/browse/VAL-319) | MGO Password Reset - April 7th | 2024-04-07 |
| Value Added  | [VAL-14](https://universalsoftware.atlassian.net/browse/VAL-14) | Open Order and Order History Divergence | 2024-04-01 |
| GM - Factory Zero - General Tickets  | [GMFZGT-91](https://universalsoftware.atlassian.net/browse/GMFZGT-91) | Implement Inventory Merge On Scanner | 2024-03-29 |
| Westport - WheelTire - General Tickets  | [WSWTGT-6](https://universalsoftware.atlassian.net/browse/WSWTGT-6) | WnT Ticket Importer Not Working | 2024-03-26 |
| WMS - Android App - General Tickets  | [ANGT-5](https://universalsoftware.atlassian.net/browse/ANGT-5) | Zebra TC8300 Demo Units | 2024-01-23 |
| WMS 3.0 Research  | [YJ3-24](https://universalsoftware.atlassian.net/browse/YJ3-24) | Research ABP.IO - Shayne | Not set |
| WMS 3.0 Research  | [YJ3-19](https://universalsoftware.atlassian.net/browse/YJ3-19) | Review ASP.NET Zero - Shayne | Not set |
| WMS 3.0 Research  | [YJ3-18](https://universalsoftware.atlassian.net/browse/YJ3-18) | Part Lookup Radzen Rework | Not set |
| WMS 3.0 Research  | [YJ3-6](https://universalsoftware.atlassian.net/browse/YJ3-6) | Review ABP.IO | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-54](https://universalsoftware.atlassian.net/browse/WSWTGT-54) | High runner wheels not decrementing from inventory | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-46](https://universalsoftware.atlassian.net/browse/WSWTGT-46) | Check the performance of PinPoint_InflationStatus_WheelTire_Westport | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-45](https://universalsoftware.atlassian.net/browse/WSWTGT-45) | Fix Pinpoint_LoadingComplete_WheelTire_Westport | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-43](https://universalsoftware.atlassian.net/browse/WSWTGT-43) | PinPoint WorkOrders Error M359048802 | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-40](https://universalsoftware.atlassian.net/browse/WSWTGT-40) | Wheels Shorted by WMS - 1/10/24 | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-39](https://universalsoftware.atlassian.net/browse/WSWTGT-39) | Job M742051656 Not On Build List | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-38](https://universalsoftware.atlassian.net/browse/WSWTGT-38) | Job M517002867 Showing Short | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-37](https://universalsoftware.atlassian.net/browse/WSWTGT-37) | Work Order Generation - Model Prefix Lookup | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-35](https://universalsoftware.atlassian.net/browse/WSWTGT-35) | Inventory Feed Status - PinPoint 0 Issue | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-33](https://universalsoftware.atlassian.net/browse/WSWTGT-33) | Inventory Feed Status - PinPoint 0 Issue - Pt 2 | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-30](https://universalsoftware.atlassian.net/browse/WSWTGT-30) | Sequence Feed Importing Issue - Line Set Archive Not Working | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-29](https://universalsoftware.atlassian.net/browse/WSWTGT-29) | Finals BOLs Showing Wrong Models | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-27](https://universalsoftware.atlassian.net/browse/WSWTGT-27) | Wheel Tire Sprint Ticket Upload Viewer | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-24](https://universalsoftware.atlassian.net/browse/WSWTGT-24) | Inventory Feed Status - Job PinPoint Status | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-8](https://universalsoftware.atlassian.net/browse/WSWTGT-8) | Westport - Mertztown - Wheel Tire - BR-101 Sprint Importer Outage | Not set |
| Westport - WheelTire - General Tickets  | [WSWTGT-1](https://universalsoftware.atlassian.net/browse/WSWTGT-1) | Wheel Tire - Reconfigure SMTP | Not set |
| Westport - Seats - General Tickets  | [WSSG-29](https://universalsoftware.atlassian.net/browse/WSSG-29) | Sequence Feed Importing Issue - Line Set Archive Not Working | Not set |
| Westport - Seats - General Tickets  | [WSSG-27](https://universalsoftware.atlassian.net/browse/WSSG-27) | Change Lineset to Model/Chassis Seats BOL | Not set |
| Westport - Seats - General Tickets  | [WSSG-26](https://universalsoftware.atlassian.net/browse/WSSG-26) | Inventory Feed Status - Seats | Not set |
| Westport - Seats - General Tickets  | [WSSG-20](https://universalsoftware.atlassian.net/browse/WSSG-20) | Seats Sequencing Dashboard Data Issue 11/21 | Not set |
| Westport - Seats - General Tickets  | [WSSG-2](https://universalsoftware.atlassian.net/browse/WSSG-2) | The V1 pages left behind in the scan app need to be removed | Not set |
| Westport - Seats - General Tickets  | [WSSG-1](https://universalsoftware.atlassian.net/browse/WSSG-1) | Seats - Reconfigure SMTP | Not set |
| Westport - Painted Parts - General Tickets   | [WSPPGT-37](https://universalsoftware.atlassian.net/browse/WSPPGT-37) | Outbound BOL report to indicate the ProperSequenceNumber on the BOL report | Not set |
| Westport - Painted Parts - General Tickets   | [WSPPGT-27](https://universalsoftware.atlassian.net/browse/WSPPGT-27) | Sequence Feed Importing Issue - Line Set Archive Not Working | Not set |
| Westport - Painted Parts - General Tickets   | [WSPPGT-24](https://universalsoftware.atlassian.net/browse/WSPPGT-24) | Painted Parts Feed Status Updates 10/30 | Not set |
| Westport - Painted Parts - General Tickets   | [WSPPGT-18](https://universalsoftware.atlassian.net/browse/WSPPGT-18) | Painted Parts - Paint File Processor Not Importing? | Not set |
| Westport - Painted Parts - General Tickets   | [WSPPGT-2](https://universalsoftware.atlassian.net/browse/WSPPGT-2) |  WP - Painted Parts - Inventory Status Report | Not set |
| Westport - Painted Parts - General Tickets   | [WSPPGT-1](https://universalsoftware.atlassian.net/browse/WSPPGT-1) | Painted Parts - Reconfigure SMTP | Not set |
| Westport - Legacy Business - General Tickets  | [WSLB-7](https://universalsoftware.atlassian.net/browse/WSLB-7) | Axle BOL User Addition - <EMAIL> | Not set |
| Westport - Legacy Business - General Tickets  | [WSLB-4](https://universalsoftware.atlassian.net/browse/WSLB-4) | SQL Server Email Configuration | Not set |
| Westport - Legacy Business - General Tickets  | [WSLB-1](https://universalsoftware.atlassian.net/browse/WSLB-1) | Fix SMTP Configurations for Legacy Westport | Not set |
| WMS Android Future Features | [WAFF-56](https://universalsoftware.atlassian.net/browse/WAFF-56) | PreferenceLoader Fallback Values | Not set |
| WMS Android Future Features | [WAFF-51](https://universalsoftware.atlassian.net/browse/WAFF-51) | Scanner - Reset File | Not set |
| WMS Android Future Features | [WAFF-50](https://universalsoftware.atlassian.net/browse/WAFF-50) | ErrorMessage Method Use | Not set |
| WMS Android Future Features | [WAFF-49](https://universalsoftware.atlassian.net/browse/WAFF-49) | Cleanup Preference Loader GetValue Methods | Not set |
| WMS Android Future Features | [WAFF-48](https://universalsoftware.atlassian.net/browse/WAFF-48) | SetBusy Method Use | Not set |
| WMS Android Future Features | [WAFF-41](https://universalsoftware.atlassian.net/browse/WAFF-41) | Add Mvvm toolkit nuget package to nuget package manager | Not set |
| WMS Android Future Features | [WAFF-40](https://universalsoftware.atlassian.net/browse/WAFF-40) | Update WMS_App.Portable project to .NET Standard 2.1 | Not set |
| WMS Android Future Features | [WAFF-39](https://universalsoftware.atlassian.net/browse/WAFF-39) | Issue with Timeout feature | Not set |
| WMS Android Future Features | [WAFF-30](https://universalsoftware.atlassian.net/browse/WAFF-30) | Exception Handling Passthrough Message | Not set |
| WMS Android Future Features | [WAFF-27](https://universalsoftware.atlassian.net/browse/WAFF-27) | Migrate Login/MainPage Configurations | Not set |
| WMS Android Future Features | [WAFF-25](https://universalsoftware.atlassian.net/browse/WAFF-25) | ReloadManagers Update | Not set |
| WMS Android Future Features | [WAFF-23](https://universalsoftware.atlassian.net/browse/WAFF-23) | Migrate camera feature from time-punch HR1-Mobile App | Not set |
| WMS Android Future Features | [WAFF-22](https://universalsoftware.atlassian.net/browse/WAFF-22) | Check out Jenkins | Not set |
| WMS Android Future Features | [WAFF-20](https://universalsoftware.atlassian.net/browse/WAFF-20) | Icon Padding | Not set |
| WMS Android Future Features | [WAFF-18](https://universalsoftware.atlassian.net/browse/WAFF-18) | Migrate PageTask stuff over to Shared class | Not set |
| WMS Android Future Features | [WAFF-13](https://universalsoftware.atlassian.net/browse/WAFF-13) | Honeywell Settings - EAN-13 / UPC-A Check Digit Preference | Not set |
| WMS Android Future Features | [WAFF-12](https://universalsoftware.atlassian.net/browse/WAFF-12) | SignalR Heart Beat - Application Status Check | Not set |
| WMS Android Future Features | [WAFF-7](https://universalsoftware.atlassian.net/browse/WAFF-7) | Build 130 | Not set |
| WMS Android Future Features | [WAFF-5](https://universalsoftware.atlassian.net/browse/WAFF-5) | Running build for the next version of android application.  | Not set |
| WMS Android Future Features | [WAFF-2](https://universalsoftware.atlassian.net/browse/WAFF-2) | Android Build 127 | Not set |
| WMS Android Future Features | [WAFF-1](https://universalsoftware.atlassian.net/browse/WAFF-1) | Android Build - V 126 | Not set |
| Value Added  | [VAL-441](https://universalsoftware.atlassian.net/browse/VAL-441) | Westport - Mertztown - SSIS Package Migration Issues | Not set |
| Value Added  | [VAL-438](https://universalsoftware.atlassian.net/browse/VAL-438) | New Hire Ticket Process Issue | Not set |
| Value Added  | [VAL-437](https://universalsoftware.atlassian.net/browse/VAL-437) | Android Legacy Application - TC8000_Kiosk_App - Scanner Issue - CNH Racine Trinity Project | Not set |
| Value Added  | [VAL-434](https://universalsoftware.atlassian.net/browse/VAL-434) | Fix git removing tmp folder in wms website | Not set |
| Value Added  | [VAL-423](https://universalsoftware.atlassian.net/browse/VAL-423) | HuntsvilleWMS | Not set |
| Value Added  | [VAL-422](https://universalsoftware.atlassian.net/browse/VAL-422) | HuntsvilleWMS UAT | Not set |
| Value Added  | [VAL-421](https://universalsoftware.atlassian.net/browse/VAL-421) | Huntsville Dashboard | Not set |
| Value Added  | [VAL-419](https://universalsoftware.atlassian.net/browse/VAL-419) | Huntsville Dashboard UAT | Not set |
| Value Added  | [VAL-418](https://universalsoftware.atlassian.net/browse/VAL-418) | HuntsvilleScan | Not set |
| Value Added  | [VAL-417](https://universalsoftware.atlassian.net/browse/VAL-417) | HuntsvilleScan UAT | Not set |
| Value Added  | [VAL-412](https://universalsoftware.atlassian.net/browse/VAL-412) | Sub Zero Desktop - Repository Adjustment | Not set |
| Value Added  | [VAL-383](https://universalsoftware.atlassian.net/browse/VAL-383) | Westport Mertztown Wheeltire UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-375](https://universalsoftware.atlassian.net/browse/VAL-375) | Test Ticket | Not set |
| Value Added  | [VAL-362](https://universalsoftware.atlassian.net/browse/VAL-362) | Server Applications (Web/Compiled) | Not set |
| Value Added  | [VAL-318](https://universalsoftware.atlassian.net/browse/VAL-318) | Whirlpool - Desktop UAT | Not set |
| Value Added  | [VAL-317](https://universalsoftware.atlassian.net/browse/VAL-317) | GitSync | Not set |
| Value Added  | [VAL-316](https://universalsoftware.atlassian.net/browse/VAL-316) | Whirlpool - Business Service | Not set |
| Value Added  | [VAL-315](https://universalsoftware.atlassian.net/browse/VAL-315) | Whirlpool - Desktop | Not set |
| Value Added  | [VAL-313](https://universalsoftware.atlassian.net/browse/VAL-313) | Whirlpool - Business Service UAT | Not set |
| Value Added  | [VAL-298](https://universalsoftware.atlassian.net/browse/VAL-298) | SQL Server Reporting Services | Not set |
| Value Added  | [VAL-297](https://universalsoftware.atlassian.net/browse/VAL-297) | Wheatley Laptop Setup | Not set |
| Value Added  | [VAL-293](https://universalsoftware.atlassian.net/browse/VAL-293) | Whirlpool - Android Service | Not set |
| Value Added  | [VAL-292](https://universalsoftware.atlassian.net/browse/VAL-292) | Whirlpool - Android Service UAT | Not set |
| Value Added  | [VAL-261](https://universalsoftware.atlassian.net/browse/VAL-261) | Label Question - Test Barcode Scanning PDF | Not set |
| Value Added  | [VAL-222](https://universalsoftware.atlassian.net/browse/VAL-222) | Robbie 10.4.6.243 IIS Instances | Not set |
| Value Added  | [VAL-209](https://universalsoftware.atlassian.net/browse/VAL-209) | Repurpose the training wms with the latest model from factory zero and sub zero | Not set |
| Value Added  | [VAL-195](https://universalsoftware.atlassian.net/browse/VAL-195) | Factory Zero Desktop - Repository Adjustment | Not set |
| Value Added  | [VAL-188](https://universalsoftware.atlassian.net/browse/VAL-188) | New canon printers for wms use | Not set |
| Value Added  | [VAL-148](https://universalsoftware.atlassian.net/browse/VAL-148) | Westport Mertztown Wheeltire Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-146](https://universalsoftware.atlassian.net/browse/VAL-146) | Westport Mertztown Seats Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-145](https://universalsoftware.atlassian.net/browse/VAL-145) | Westport Mertztown Seats UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-143](https://universalsoftware.atlassian.net/browse/VAL-143) | Westport Mertztown Painted Parts Production - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-141](https://universalsoftware.atlassian.net/browse/VAL-141) | Westport Mertztown Painted Parts UAT - Migration to Bit Bucket | Not set |
| Value Added  | [VAL-91](https://universalsoftware.atlassian.net/browse/VAL-91) | User Management Connection Info Update | Not set |
| Value Added  | [VAL-72](https://universalsoftware.atlassian.net/browse/VAL-72) | Flint Continental - Order History | Not set |
| Value Added  | [VAL-69](https://universalsoftware.atlassian.net/browse/VAL-69) | Flint Continental - Open Orders | Not set |
| Value Added  | [VAL-65](https://universalsoftware.atlassian.net/browse/VAL-65) | Service Framework | Not set |
| Value Added  | [VAL-38](https://universalsoftware.atlassian.net/browse/VAL-38) | Server Information Sheet Update | Not set |
| Value Added  | [VAL-25](https://universalsoftware.atlassian.net/browse/VAL-25) | Fix SMTP Configurations for Valadd Server | Not set |
| Value Added  | [VAL-23](https://universalsoftware.atlassian.net/browse/VAL-23) | WMS_AndroidBusinessService - Gitea to BitBucket Migration | Not set |
| Value Added  | [VAL-22](https://universalsoftware.atlassian.net/browse/VAL-22) | WMS_Dashboard- Gitea to BitBucket Migration | Not set |
| Value Added  | [VAL-20](https://universalsoftware.atlassian.net/browse/VAL-20) | WMS_IOSScanApp - Gitea to BitBucket Migration | Not set |
| Value Added  | [VAL-19](https://universalsoftware.atlassian.net/browse/VAL-19) | Value Added WMS Teams Portion of Ivanti Swap | Not set |
| Value Added  | [VAL-17](https://universalsoftware.atlassian.net/browse/VAL-17) | WMS_Website - Gitea to BitBucket Migration | Not set |
| Value Added  | [VAL-12](https://universalsoftware.atlassian.net/browse/VAL-12) | User Removal from Reports | Not set |
| Value Added  | [VAL-11](https://universalsoftware.atlassian.net/browse/VAL-11) |  Upgrade PurgeByLocation Mechanics To Improve Logging Capabilities | Not set |
| Value Added  | [VAL-6](https://universalsoftware.atlassian.net/browse/VAL-6) | JSON Routing Webservice | Not set |
| Value Added  | [VAL-4](https://universalsoftware.atlassian.net/browse/VAL-4) | GoCD and GitSync | Not set |
| Value Added  | [VAL-1](https://universalsoftware.atlassian.net/browse/VAL-1) | WMS_ScannerWebservice - Gitea to BitBucket Migration | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-65](https://universalsoftware.atlassian.net/browse/TAMA-65) | EDI File Mover | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-61](https://universalsoftware.atlassian.net/browse/TAMA-61) | Outbound File Generator | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-56](https://universalsoftware.atlassian.net/browse/TAMA-56) | Android Business Service | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-55](https://universalsoftware.atlassian.net/browse/TAMA-55) | Android Business Service UAT | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-51](https://universalsoftware.atlassian.net/browse/TAMA-51) | Desktop | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-49](https://universalsoftware.atlassian.net/browse/TAMA-49) | Desktop UAT | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-45](https://universalsoftware.atlassian.net/browse/TAMA-45) | Android Application Service | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-44](https://universalsoftware.atlassian.net/browse/TAMA-44) | Android Application Service UAT | Not set |
| ValAdd Time and Attendance Mobile | [TAMA-12](https://universalsoftware.atlassian.net/browse/TAMA-12) | Fix SMTP Configurations for HR TnA | Not set |
| Stork Rubber - General Tickets  | [STOR-1](https://universalsoftware.atlassian.net/browse/STOR-1) | Fix SMTP Configurations for Stork | Not set |
| Shakopee XDock - General Tickets  | [SHAK-13](https://universalsoftware.atlassian.net/browse/SHAK-13) | Destination Plant Routing | Not set |
| Shakopee XDock - General Tickets  | [SHAK-11](https://universalsoftware.atlassian.net/browse/SHAK-11) | Shakopee CSV Report Generator | Not set |
| Shakopee XDock - General Tickets  | [SHAK-10](https://universalsoftware.atlassian.net/browse/SHAK-10) | Process Hot Board | Not set |
| Shakopee XDock - General Tickets  | [SHAK-9](https://universalsoftware.atlassian.net/browse/SHAK-9) | ProcessSBS | Not set |
| Shakopee XDock - General Tickets  | [SHAK-8](https://universalsoftware.atlassian.net/browse/SHAK-8) | Process Penske File | Not set |
| Shakopee XDock - General Tickets  | [SHAK-7](https://universalsoftware.atlassian.net/browse/SHAK-7) | Process945 | Not set |
| Shakopee XDock - General Tickets  | [SHAK-6](https://universalsoftware.atlassian.net/browse/SHAK-6) | IMAP Downloader | Not set |
| Shakopee XDock - General Tickets  | [SHAK-1](https://universalsoftware.atlassian.net/browse/SHAK-1) | Fix SMTP Configurations for Shakopee | Not set |
| SubZero  | [SBZ-71](https://universalsoftware.atlassian.net/browse/SBZ-71) | Migrate GFZGT-250 differences back to Sub Zero | Not set |
| SubZero  | [SBZ-68](https://universalsoftware.atlassian.net/browse/SBZ-68) | Migrate GFZGT-240 to Sub Zero | Not set |
| SubZero  | [SBZ-56](https://universalsoftware.atlassian.net/browse/SBZ-56) | Migrate GFZGT-243 to Sub Zero | Not set |
| SubZero  | [SBZ-52](https://universalsoftware.atlassian.net/browse/SBZ-52) | Migrate the changes that were started off 9/23 | Not set |
| SubZero  | [SBZ-50](https://universalsoftware.atlassian.net/browse/SBZ-50) | Setup GitSync and UAT environments | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-111](https://universalsoftware.atlassian.net/browse/RLNGTN-111) | Question concerning unload totals | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-109](https://universalsoftware.atlassian.net/browse/RLNGTN-109) | GM - Arlington - Productivity By Shift By User - Telerik Reporting Bug | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-105](https://universalsoftware.atlassian.net/browse/RLNGTN-105) | Arlington PPS Importer Config Missing | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-104](https://universalsoftware.atlassian.net/browse/RLNGTN-104) | Productivity by shift report not accurate  | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-97](https://universalsoftware.atlassian.net/browse/RLNGTN-97) | GM - Arlington - Cycle Not Printing 6-24 | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-95](https://universalsoftware.atlassian.net/browse/RLNGTN-95) | GM - Arlington - Laser Printer Management Page | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-94](https://universalsoftware.atlassian.net/browse/RLNGTN-94) | GM - Arlington - Label Printer Management Page | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-85](https://universalsoftware.atlassian.net/browse/RLNGTN-85) | DB Mail Configuration | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-80](https://universalsoftware.atlassian.net/browse/RLNGTN-80) | Arlington - MGO - Reporting Outage - 5/20/24 - 7:21 AM | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-71](https://universalsoftware.atlassian.net/browse/RLNGTN-71) | NoStock Scanning | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-69](https://universalsoftware.atlassian.net/browse/RLNGTN-69) | Desktop - ScanUser | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-68](https://universalsoftware.atlassian.net/browse/RLNGTN-68) | PartFindNavistar | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-67](https://universalsoftware.atlassian.net/browse/RLNGTN-67) | Android_GetOpenReleasesByFilterValue | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-66](https://universalsoftware.atlassian.net/browse/RLNGTN-66) | Android_AddInventoryDetailsASN | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-65](https://universalsoftware.atlassian.net/browse/RLNGTN-65) | UpdateInventory_Remove | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-61](https://universalsoftware.atlassian.net/browse/RLNGTN-61) | Android_AddInventoryDetails | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-60](https://universalsoftware.atlassian.net/browse/RLNGTN-60) | Android_GetOpenReleaseDetailsToPick | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-59](https://universalsoftware.atlassian.net/browse/RLNGTN-59) | Added InventoryType to Inventory table | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-58](https://universalsoftware.atlassian.net/browse/RLNGTN-58) | Android_UpdateReleaseMasterOutboundReturnPrinter | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-38](https://universalsoftware.atlassian.net/browse/RLNGTN-38) | Post Deployment - Obsolete Column Alteration | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-5](https://universalsoftware.atlassian.net/browse/RLNGTN-5) | Arlington Database Cleanout | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-3](https://universalsoftware.atlassian.net/browse/RLNGTN-3) | Arlington - Upgrade 2024/25 | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-1](https://universalsoftware.atlassian.net/browse/RLNGTN-1) | Arlington Fix SMTP Configurations | Not set |
| Polaris - Huntsville - General Tickets  | [POL-1](https://universalsoftware.atlassian.net/browse/POL-1) | Fix SMTP Configurations for Huntsville | Not set |
| Polaris - WH17 - General Tickets  | [PL-16](https://universalsoftware.atlassian.net/browse/PL-16) | EDI File Mover - Report CSV Generator | Not set |
| Polaris - WH17 - General Tickets  | [PL-15](https://universalsoftware.atlassian.net/browse/PL-15) | EDI File Mover | Not set |
| Polaris - WH17 - General Tickets  | [PL-14](https://universalsoftware.atlassian.net/browse/PL-14) | Desktop Site UAT | Not set |
| Polaris - WH17 - General Tickets  | [PL-13](https://universalsoftware.atlassian.net/browse/PL-13) | WH17 - Adding email address to Auto Generated Report | Not set |
| Polaris - WH17 - General Tickets  | [PL-12](https://universalsoftware.atlassian.net/browse/PL-12) | Report CSV Generator | Not set |
| Polaris - WH17 - General Tickets  | [PL-11](https://universalsoftware.atlassian.net/browse/PL-11) | Process Huntsville | Not set |
| Polaris - WH17 - General Tickets  | [PL-9](https://universalsoftware.atlassian.net/browse/PL-9) | Process Hot Board | Not set |
| Polaris - WH17 - General Tickets  | [PL-8](https://universalsoftware.atlassian.net/browse/PL-8) | Process Bounce Report | Not set |
| Polaris - WH17 - General Tickets  | [PL-7](https://universalsoftware.atlassian.net/browse/PL-7) | Daily Inventory Received Alert | Not set |
| Polaris - WH17 - General Tickets  | [PL-6](https://universalsoftware.atlassian.net/browse/PL-6) | IMAP Downloader | Not set |
| Polaris - WH17 - General Tickets  | [PL-5](https://universalsoftware.atlassian.net/browse/PL-5) | Desktop Site | Not set |
| Polaris - WH17 - General Tickets  | [PL-4](https://universalsoftware.atlassian.net/browse/PL-4) | WH37 and WH14 shortage report | Not set |
| Polaris - WH17 - General Tickets  | [PL-1](https://universalsoftware.atlassian.net/browse/PL-1) | Fix SMTP Configurations for WH17 | Not set |
| Navistar San Antonio | [NVSSA-67](https://universalsoftware.atlassian.net/browse/NVSSA-67) | Navistar - Outbound Open/Close title incorrect | Not set |
| Navistar San Antonio | [NVSSA-65](https://universalsoftware.atlassian.net/browse/NVSSA-65) | Navistar - Outbound - Missing Title | Not set |
| Navistar San Antonio | [NVSSA-63](https://universalsoftware.atlassian.net/browse/NVSSA-63) | Job #305061 - Not printing wheel labels | Not set |
| Navistar San Antonio | [NVSSA-56](https://universalsoftware.atlassian.net/browse/NVSSA-56) | Navistar - Staging to Trailer - Missing Title | Not set |
| Navistar San Antonio | [NVSSA-55](https://universalsoftware.atlassian.net/browse/NVSSA-55) | Navistar - Wheel Tire Audit - Missing Title | Not set |
| Navistar San Antonio | [NVSSA-54](https://universalsoftware.atlassian.net/browse/NVSSA-54) | Navistar - Pick To Line - Missing Title | Not set |
| Navistar San Antonio | [NVSSA-53](https://universalsoftware.atlassian.net/browse/NVSSA-53) | Navistar - Tire Pressure Audit - Missing Title | Not set |
| Navistar San Antonio | [NVSSA-50](https://universalsoftware.atlassian.net/browse/NVSSA-50) | Outbound Doors Bug | Not set |
| Navistar San Antonio | [NVSSA-48](https://universalsoftware.atlassian.net/browse/NVSSA-48) | Sequence CLOC Upload - Initialized Buckets Missing New InventorySubTypeID | Not set |
| Navistar San Antonio | [NVSSA-31](https://universalsoftware.atlassian.net/browse/NVSSA-31) | Printing Issue - 12/19 | Not set |
| Navistar San Antonio | [NVSSA-29](https://universalsoftware.atlassian.net/browse/NVSSA-29) | Non Serialized Issue Valves and Caps | Not set |
| Navistar San Antonio | [NVSSA-17](https://universalsoftware.atlassian.net/browse/NVSSA-17) | Navistar - Box Move | Not set |
| Navistar San Antonio | [NVSSA-13](https://universalsoftware.atlassian.net/browse/NVSSA-13) | New Label Requirements | Not set |
| Navistar San Antonio | [NVSSA-1](https://universalsoftware.atlassian.net/browse/NVSSA-1) | Fix SMTP Configurations for Navistar | Not set |
| Lear Stanley Gault | [LSG-18](https://universalsoftware.atlassian.net/browse/LSG-18) | Change the status of 15218 to indicate it is complete/deleted | Not set |
| Lear Stanley Gault | [LSG-17](https://universalsoftware.atlassian.net/browse/LSG-17) | WMS Lear Louisville - 862 Release | Not set |
| Lear Stanley Gault | [LSG-15](https://universalsoftware.atlassian.net/browse/LSG-15) | Order 15243 - Not Closing | Not set |
| Lear Stanley Gault | [LSG-2](https://universalsoftware.atlassian.net/browse/LSG-2) | Fix SMTP Configurations for Lear | Not set |
| GM SLP WMS | [GMSLP-1](https://universalsoftware.atlassian.net/browse/GMSLP-1) | Fix SMTP Configurations for SLP | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-275](https://universalsoftware.atlassian.net/browse/GMFZGT-275) | Part Lookup Editor Not Working | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-264](https://universalsoftware.atlassian.net/browse/GMFZGT-264) | InboundTrailerDetails - Backfill InventoryDetailSerialID column data | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-257](https://universalsoftware.atlassian.net/browse/GMFZGT-257) | Site.Master and Default - Order Links Not Working | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-250](https://universalsoftware.atlassian.net/browse/GMFZGT-250) | Factory Zero - Function - GetOrderData vs GetPartLevelDetailByReleaseDetailID | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-247](https://universalsoftware.atlassian.net/browse/GMFZGT-247) | Serial Not Showing - Shipped Containers | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-246](https://universalsoftware.atlassian.net/browse/GMFZGT-246) | Order Calculation Issues | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-244](https://universalsoftware.atlassian.net/browse/GMFZGT-244) | Trailer Status - Inbound Error | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-238](https://universalsoftware.atlassian.net/browse/GMFZGT-238) | Part 85798926 - Deleted Still In Inventory - 08/09/24 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-237](https://universalsoftware.atlassian.net/browse/GMFZGT-237) | Small lot picking problem | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-226](https://universalsoftware.atlassian.net/browse/GMFZGT-226) | ReleaseMaster 102102 Divide By Zero | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-223](https://universalsoftware.atlassian.net/browse/GMFZGT-223) | Clear Cross Dock New Model Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-220](https://universalsoftware.atlassian.net/browse/GMFZGT-220) | 7/26 - Shipped Orders Route Issue | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-217](https://universalsoftware.atlassian.net/browse/GMFZGT-217) | Inventory - Part Lookup - Part Receipts Bug - Export Excel | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-214](https://universalsoftware.atlassian.net/browse/GMFZGT-214) | Outbound BOL - Not Printing Automatically | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-212](https://universalsoftware.atlassian.net/browse/GMFZGT-212) | Pickface Mins to Stock Issue | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-211](https://universalsoftware.atlassian.net/browse/GMFZGT-211) | Outbound Trailer History | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-204](https://universalsoftware.atlassian.net/browse/GMFZGT-204) | Factory Zero - WMS - InventoryPickface.aspx Page Missing | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-201](https://universalsoftware.atlassian.net/browse/GMFZGT-201) | WMS Scanner - Reprinting Labels | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-195](https://universalsoftware.atlassian.net/browse/GMFZGT-195) | Scanner Code Review | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-194](https://universalsoftware.atlassian.net/browse/GMFZGT-194) | Desktop Code Review | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-187](https://universalsoftware.atlassian.net/browse/GMFZGT-187) | Migrate SUB-4 To FactoryZero from SubZero | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-186](https://universalsoftware.atlassian.net/browse/GMFZGT-186) | Table Updater For Inventory | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-181](https://universalsoftware.atlassian.net/browse/GMFZGT-181) | Validate Assigned Locations without Inventory | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-180](https://universalsoftware.atlassian.net/browse/GMFZGT-180) | Printer Management Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-179](https://universalsoftware.atlassian.net/browse/GMFZGT-179) | Script #2 - August 5th | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-178](https://universalsoftware.atlassian.net/browse/GMFZGT-178) | Parts Receipts Tab | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-177](https://universalsoftware.atlassian.net/browse/GMFZGT-177) | Material / Plant Management personnel do not have access to delete individual serial numbers. | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-175](https://universalsoftware.atlassian.net/browse/GMFZGT-175) | SSRS Report Library Version | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-174](https://universalsoftware.atlassian.net/browse/GMFZGT-174) | PPS Delivery Sheets | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-173](https://universalsoftware.atlassian.net/browse/GMFZGT-173) | Script #1 - July 9th | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-172](https://universalsoftware.atlassian.net/browse/GMFZGT-172) | MGO Status Updater - Android_UpdateInboundTrailerClosedByID Discrepancy | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-170](https://universalsoftware.atlassian.net/browse/GMFZGT-170) | Android Device Dashboard - Missing From Menu | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-165](https://universalsoftware.atlassian.net/browse/GMFZGT-165) | Change Permissions for generic Inventory In and Inventory Out | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-164](https://universalsoftware.atlassian.net/browse/GMFZGT-164) | Setup separate email for RESS back-up MOPS picks | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-160](https://universalsoftware.atlassian.net/browse/GMFZGT-160) | New WMS Users 6-13 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-158](https://universalsoftware.atlassian.net/browse/GMFZGT-158) | Clean out production data from SQL Database  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-152](https://universalsoftware.atlassian.net/browse/GMFZGT-152) | Repack For Body Shop | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-147](https://universalsoftware.atlassian.net/browse/GMFZGT-147) | New Users WMS - 6-3 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-146](https://universalsoftware.atlassian.net/browse/GMFZGT-146) | SQL - Initial Migration Effort | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-143](https://universalsoftware.atlassian.net/browse/GMFZGT-143) | Setup MGO Status Updater Receipts | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-142](https://universalsoftware.atlassian.net/browse/GMFZGT-142) | kbanaszewski - password reset - 6/4 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-141](https://universalsoftware.atlassian.net/browse/GMFZGT-141) | Part Lookup / TableUpdater - Repack Qty  | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-140](https://universalsoftware.atlassian.net/browse/GMFZGT-140) | Password Reset Jerome Sutter 6-12 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-139](https://universalsoftware.atlassian.net/browse/GMFZGT-139) | New WMS Users 6-12 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-129](https://universalsoftware.atlassian.net/browse/GMFZGT-129) | Clean up EDI File Mover Leftovers - 6-17 - Security Patch Issue | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-128](https://universalsoftware.atlassian.net/browse/GMFZGT-128) | PPS Manifest Adjustment - 07-08 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-126](https://universalsoftware.atlassian.net/browse/GMFZGT-126) | Inventory Report Schedule - Add 5PM | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-125](https://universalsoftware.atlassian.net/browse/GMFZGT-125) | NCP Ticket SSRS Report - Web Config | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-124](https://universalsoftware.atlassian.net/browse/GMFZGT-124) | Add Priority and Route to the page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-120](https://universalsoftware.atlassian.net/browse/GMFZGT-120) | InventoryDetailsOut - TransactionType BackFill - ClearXDock | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-118](https://universalsoftware.atlassian.net/browse/GMFZGT-118) | InboundTrailerDetails Add Location | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-104](https://universalsoftware.atlassian.net/browse/GMFZGT-104) | Inbound #2 Dashboard - Inbound Trailers | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-103](https://universalsoftware.atlassian.net/browse/GMFZGT-103) | Inbound #1 Dashboard | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-102](https://universalsoftware.atlassian.net/browse/GMFZGT-102) | CMA Remove Endcap B Grid | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-101](https://universalsoftware.atlassian.net/browse/GMFZGT-101) | First Deployment - CMA and Bulk | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-96](https://universalsoftware.atlassian.net/browse/GMFZGT-96) | Please Grant "View Only" access to the Factory Zero WMS System | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-87](https://universalsoftware.atlassian.net/browse/GMFZGT-87) | Pcs picked/ordered report | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-86](https://universalsoftware.atlassian.net/browse/GMFZGT-86) | Duplicate Serial scanned to orders report | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-85](https://universalsoftware.atlassian.net/browse/GMFZGT-85) | BOL for Shuttles | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-76](https://universalsoftware.atlassian.net/browse/GMFZGT-76) | Inventory - Add ELOC | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-75](https://universalsoftware.atlassian.net/browse/GMFZGT-75) | No Stock Report Updates | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-68](https://universalsoftware.atlassian.net/browse/GMFZGT-68) | Order Cycle Times and Due Date Times | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-66](https://universalsoftware.atlassian.net/browse/GMFZGT-66) | PPS - Importer Times Adjustment | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-65](https://universalsoftware.atlassian.net/browse/GMFZGT-65) | Dashboard Adjustments | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-64](https://universalsoftware.atlassian.net/browse/GMFZGT-64) | Setup the File Archive Utility on archive for PPS | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-62](https://universalsoftware.atlassian.net/browse/GMFZGT-62) | Add DeviceDashboard.aspx Link to Site.master Menu - FZ Prime and RESS | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-60](https://universalsoftware.atlassian.net/browse/GMFZGT-60) | Inventory - No Stocks Report - Release Master Status | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-57](https://universalsoftware.atlassian.net/browse/GMFZGT-57) | Duplicate Serials - Page Tweaks | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-56](https://universalsoftware.atlassian.net/browse/GMFZGT-56) | Pieces Picked / Ordered Report - Title Update On Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-55](https://universalsoftware.atlassian.net/browse/GMFZGT-55) | Inventory ASN - Update Trailer Location Logging | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-52](https://universalsoftware.atlassian.net/browse/GMFZGT-52) | Factory Zero Prime - July 9th Intermittent Outages | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-48](https://universalsoftware.atlassian.net/browse/GMFZGT-48) | WMS Shipped Containers Qty Data | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-46](https://universalsoftware.atlassian.net/browse/GMFZGT-46) | PDF Reports Folder | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-45](https://universalsoftware.atlassian.net/browse/GMFZGT-45) | Desktop - InventoryLocations - Delete Ambiguity | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-42](https://universalsoftware.atlassian.net/browse/GMFZGT-42) | Print PPS Report Quantity Web Service 2 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-33](https://universalsoftware.atlassian.net/browse/GMFZGT-33) | InventoryASN - Log When Trailer Location Is Updated | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-31](https://universalsoftware.atlassian.net/browse/GMFZGT-31) | GM - Factory Zero - Test Improvement | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-25](https://universalsoftware.atlassian.net/browse/GMFZGT-25) | Part Lookup - Non Net Qty | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-22](https://universalsoftware.atlassian.net/browse/GMFZGT-22) | Missing Desktop Pages | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-16](https://universalsoftware.atlassian.net/browse/GMFZGT-16) | Inventory Details FIFO Reporting - pt 2 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-10](https://universalsoftware.atlassian.net/browse/GMFZGT-10) | Outbound Trailer History Data Correction | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-6](https://universalsoftware.atlassian.net/browse/GMFZGT-6) | Factory Zero - Launch Support | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-3](https://universalsoftware.atlassian.net/browse/GMFZGT-3) | Get Website into WMS Git Branch | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-2](https://universalsoftware.atlassian.net/browse/GMFZGT-2) | Android Factory Zero Setup | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-1](https://universalsoftware.atlassian.net/browse/GMFZGT-1) | GM - Factory Zero - Test Epic Summary | Not set |
| GM Flint Torrey Rd | [GMFNT-51](https://universalsoftware.atlassian.net/browse/GMFNT-51) | Report Server Configuration | Not set |
| GM Flint Torrey Rd | [GMFNT-48](https://universalsoftware.atlassian.net/browse/GMFNT-48) | Process Menlo XML | Not set |
| GM Flint Torrey Rd | [GMFNT-41](https://universalsoftware.atlassian.net/browse/GMFNT-41) | Generate XML Reconciliation | Not set |
| GM Flint Torrey Rd | [GMFNT-40](https://universalsoftware.atlassian.net/browse/GMFNT-40) | Flint - Torrey Road - Outage 5/7/24 - 6:39 PM | Not set |
| GM Flint Torrey Rd | [GMFNT-39](https://universalsoftware.atlassian.net/browse/GMFNT-39) | Dealer Import File | Not set |
| GM Flint Torrey Rd | [GMFNT-38](https://universalsoftware.atlassian.net/browse/GMFNT-38) | Generate XML RouteClose | Not set |
| GM Flint Torrey Rd | [GMFNT-37](https://universalsoftware.atlassian.net/browse/GMFNT-37) | IMAP Downloader | Not set |
| GM Flint Torrey Rd | [GMFNT-28](https://universalsoftware.atlassian.net/browse/GMFNT-28) | Generate XML Arrival | Not set |
| GM Flint Torrey Rd | [GMFNT-26](https://universalsoftware.atlassian.net/browse/GMFNT-26) | Generate XML EDI File Mover | Not set |
| GM Flint Torrey Rd | [GMFNT-25](https://universalsoftware.atlassian.net/browse/GMFNT-25) | Generate Expedite Out | Not set |
| GM Flint Torrey Rd | [GMFNT-24](https://universalsoftware.atlassian.net/browse/GMFNT-24) | Generate XML Possession | Not set |
| GM Flint Torrey Rd | [GMFNT-23](https://universalsoftware.atlassian.net/browse/GMFNT-23) | Generate XML Inbound | Not set |
| GM Flint Torrey Rd | [GMFNT-22](https://universalsoftware.atlassian.net/browse/GMFNT-22) | Generate XML LoadClose | Not set |
| GM Flint Torrey Rd | [GMFNT-17](https://universalsoftware.atlassian.net/browse/GMFNT-17) | Torrey Road - Door Code Scanning Issue - 3/11/24 | Not set |
| GM Flint Torrey Rd | [GMFNT-16](https://universalsoftware.atlassian.net/browse/GMFNT-16) | Process Missed Scans | Not set |
| GM Flint Torrey Rd | [GMFNT-14](https://universalsoftware.atlassian.net/browse/GMFNT-14) | AndroidWebService_IntegrationUAT | Not set |
| GM Flint Torrey Rd | [GMFNT-13](https://universalsoftware.atlassian.net/browse/GMFNT-13) | FlintTorreyWMS | Not set |
| GM Flint Torrey Rd | [GMFNT-12](https://universalsoftware.atlassian.net/browse/GMFNT-12) | AndroidWebService | Not set |
| GM Flint Torrey Rd | [GMFNT-11](https://universalsoftware.atlassian.net/browse/GMFNT-11) | AndroidWebService_Integration | Not set |
| GM Flint Torrey Rd | [GMFNT-10](https://universalsoftware.atlassian.net/browse/GMFNT-10) | FlintTorreyWMSUAT | Not set |
| GM Flint Torrey Rd | [GMFNT-9](https://universalsoftware.atlassian.net/browse/GMFNT-9) | EDI File Mover - Generate Expedite Out | Not set |
| GM Flint Torrey Rd | [GMFNT-1](https://universalsoftware.atlassian.net/browse/GMFNT-1) | Fix SMTP Configurations for Torrey | Not set |
| GM ODC Launch  | [GMDCL-30](https://universalsoftware.atlassian.net/browse/GMDCL-30) | GM - ODC - User Creation 10/6 | Not set |
| GM ODC Launch  | [GMDCL-19](https://universalsoftware.atlassian.net/browse/GMDCL-19) | Fix PLNTNAME table  | Not set |
| GM ODC Launch  | [GMDCL-17](https://universalsoftware.atlassian.net/browse/GMDCL-17) | User Creation - 9/29 - Dorian Lopez Request | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-54](https://universalsoftware.atlassian.net/browse/GFCGT-54) | Unable to Reprint PPS order | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-39](https://universalsoftware.atlassian.net/browse/GFCGT-39) | Dashboard Updates | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-27](https://universalsoftware.atlassian.net/browse/GFCGT-27) | PPS Importer | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-26](https://universalsoftware.atlassian.net/browse/GFCGT-26) | Android Download Processor - Turn off | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-25](https://universalsoftware.atlassian.net/browse/GFCGT-25) | Original UAT Environment Refresh | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-24](https://universalsoftware.atlassian.net/browse/GFCGT-24) | GM Flint Continental - Android V2 Upgrade - Outstanding Issues | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-5](https://universalsoftware.atlassian.net/browse/GFCGT-5) | SSRS Pickface Min Violation Report Update | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-4](https://universalsoftware.atlassian.net/browse/GFCGT-4) | Flint Continental Fix SMTP Configurations | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-2](https://universalsoftware.atlassian.net/browse/GFCGT-2) | GM - Flint Continental - Android Upgrade Factory Zero | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-1](https://universalsoftware.atlassian.net/browse/GFCGT-1) | Flint Continental V2 Migration Cleanup | Not set |
| FCA XDock  | [FX-3](https://universalsoftware.atlassian.net/browse/FX-3) | GM ODC - Docks Listing | Not set |
| Ford Steering Stanley Gault | [FSSG-45](https://universalsoftware.atlassian.net/browse/FSSG-45) | Label Printing Issue - 12/20 | Not set |
| Ford Steering Stanley Gault | [FSSG-44](https://universalsoftware.atlassian.net/browse/FSSG-44) | UAT Desktop Web Config Target Framework | Not set |
| Ford Steering Stanley Gault | [FSSG-43](https://universalsoftware.atlassian.net/browse/FSSG-43) | BOL Report Access Issue | Not set |
| Ford Steering Stanley Gault | [FSSG-41](https://universalsoftware.atlassian.net/browse/FSSG-41) | Webconfig Framework Issue | Not set |
| Ford Steering Stanley Gault | [FSSG-39](https://universalsoftware.atlassian.net/browse/FSSG-39) | CUM YTD Quantity Issue | Not set |
| Ford Steering Stanley Gault | [FSSG-37](https://universalsoftware.atlassian.net/browse/FSSG-37) | Inventory Upload Bug | Not set |
| Ford Steering Stanley Gault | [FSSG-31](https://universalsoftware.atlassian.net/browse/FSSG-31) | Update Maintenance Plans | Not set |
| Ford Steering Stanley Gault | [FSSG-30](https://universalsoftware.atlassian.net/browse/FSSG-30) | BOL Report Tweak | Not set |
| Ford Steering Stanley Gault | [FSSG-29](https://universalsoftware.atlassian.net/browse/FSSG-29) | SQL Job - Truncate_UpdatedopenOrderRelease | Not set |
| Ford Steering Stanley Gault | [FSSG-27](https://universalsoftware.atlassian.net/browse/FSSG-27) | Import Label Printer | Not set |
| Ford Steering Stanley Gault | [FSSG-20](https://universalsoftware.atlassian.net/browse/FSSG-20) | SQL Job - Reset Part Cumulative YTD | Not set |
| Ford Steering Stanley Gault | [FSSG-19](https://universalsoftware.atlassian.net/browse/FSSG-19) | Migrate Reporting Solution | Not set |
| Ford Steering Stanley Gault | [FSSG-2](https://universalsoftware.atlassian.net/browse/FSSG-2) | New Louisville - Lear WMS Instance | Not set |
| Ford Steering Stanley Gault | [FSSG-1](https://universalsoftware.atlassian.net/browse/FSSG-1) | Fix SMTP Configurations for Ford Steering | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-5](https://universalsoftware.atlassian.net/browse/FNGFW-5) | DBMail Configuration | Not set |
| Flex N Gate - Fort Wayne | [FNGFW-1](https://universalsoftware.atlassian.net/browse/FNGFW-1) | Fix SMTP Configurations for Flex N Gate | Not set |
| FCA XDock App Rewrite  | [FCXDR-129](https://universalsoftware.atlassian.net/browse/FCXDR-129) | Fix SMTP Configurations for Cross Dock Applications | Not set |
| FCA XDock App Rewrite  | [FCXDR-83](https://universalsoftware.atlassian.net/browse/FCXDR-83) | Clark Street - Outbound ASCs Missing 3:30 PM - 4:30 PM | Not set |
| FCA XDock App Rewrite  | [FCXDR-47](https://universalsoftware.atlassian.net/browse/FCXDR-47) | IIS - Cross Dock Landing Site Homepage Adjustment | Not set |
| DTNA CMS | [DTCMS-1](https://universalsoftware.atlassian.net/browse/DTCMS-1) | DTNA Fix SMTP Configurations | Not set |
| CNH Racine | [CNHR-46](https://universalsoftware.atlassian.net/browse/CNHR-46) | CNH - Scan Application - Outage 2/24/24 | Not set |
| CNH Racine | [CNHR-2](https://universalsoftware.atlassian.net/browse/CNHR-2) | Fix SMTP Configurations for Racine | Not set |
| CNH Goodfield | [CNHG-7](https://universalsoftware.atlassian.net/browse/CNHG-7) | Goodfield - Wms is lagging again | Not set |
| CNH Goodfield | [CNHG-1](https://universalsoftware.atlassian.net/browse/CNHG-1) | Fix SMTP Configurations for Goodfield | Not set |
| Clark Street - General Tickets  | [CLAR-6](https://universalsoftware.atlassian.net/browse/CLAR-6) | Clark Street - WMS Outage 3/20/24 - 8:00PM  | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-30](https://universalsoftware.atlassian.net/browse/BOEIN-30) | Need Account to mirror David Vargas | Not set |
| Boeing - XDock - General Tickets  | [BOEIN-1](https://universalsoftware.atlassian.net/browse/BOEIN-1) | Boeing XDock Fix SMTP Configurations | Not set |
| Boeing Portland - General Tickets  | [BNG-1](https://universalsoftware.atlassian.net/browse/BNG-1) | Portland Fix SMTP Configurations | Not set |
| Boeing Mesa - General Tickets  | [BN-104](https://universalsoftware.atlassian.net/browse/BN-104) | Rights Assigned and Toggle Order Priority | Not set |
| Boeing Mesa - General Tickets  | [BN-95](https://universalsoftware.atlassian.net/browse/BN-95) | Boeing Mesa - Create Pick Request Issue - GOLDMSA Receipt Identifier Issue | Not set |
| Boeing Mesa - General Tickets  | [BN-90](https://universalsoftware.atlassian.net/browse/BN-90) | Boeing Mesa - Outage 01/30/24 - 1:00am | Not set |
| Boeing Mesa - General Tickets  | [BN-85](https://universalsoftware.atlassian.net/browse/BN-85) | EDI File Mover - Database Server | Not set |
| Boeing Mesa - General Tickets  | [BN-84](https://universalsoftware.atlassian.net/browse/BN-84) | Remove ManualAsnMaster 68544 | Not set |
| Boeing Mesa - General Tickets  | [BN-82](https://universalsoftware.atlassian.net/browse/BN-82) | Remove ManualAsnMaster 193223 | Not set |
| Boeing Mesa - General Tickets  | [BN-80](https://universalsoftware.atlassian.net/browse/BN-80) | Remove "Inventory Out" function | Not set |
| Boeing Mesa - General Tickets  | [BN-29](https://universalsoftware.atlassian.net/browse/BN-29) | Boeing Mesa - Temporarily Disabled Receipt Confirmations | Not set |
| Boeing Mesa - General Tickets  | [BN-1](https://universalsoftware.atlassian.net/browse/BN-1) | Fix SMTP Configurations for Mesa | Not set |
| BMW Spartanburg | [BMWS-457](https://universalsoftware.atlassian.net/browse/BMWS-457) | Trailer Status - Closing Issue | Not set |
| BMW Spartanburg | [BMWS-406](https://universalsoftware.atlassian.net/browse/BMWS-406) | BMW - hide the merge trailer button | Not set |
| BMW Spartanburg | [BMWS-391](https://universalsoftware.atlassian.net/browse/BMWS-391) | Reorder Trailer Not Identifiable | Not set |
| BMW Spartanburg | [BMWS-376](https://universalsoftware.atlassian.net/browse/BMWS-376) | BMW - Joined Trailers Merge Process | Not set |
| BMW Spartanburg | [BMWS-374](https://universalsoftware.atlassian.net/browse/BMWS-374) | BMW - Pre Sequence ASN Post Process - Order Commodity Records | Not set |
| BMW Spartanburg | [BMWS-373](https://universalsoftware.atlassian.net/browse/BMWS-373) | BMW - Pre Sequenced Receipts Not Producing SupplyGroups | Not set |
| BMW Spartanburg | [BMWS-365](https://universalsoftware.atlassian.net/browse/BMWS-365) | Outbound Racks - Missing Details on Racks | Not set |
| BMW Spartanburg | [BMWS-362](https://universalsoftware.atlassian.net/browse/BMWS-362) | Outbound Racks Management - Bulk JIS UAT Testing | Not set |
| BMW Spartanburg | [BMWS-341](https://universalsoftware.atlassian.net/browse/BMWS-341) | Issue with movement showing different names | Not set |
| BMW Spartanburg | [BMWS-340](https://universalsoftware.atlassian.net/browse/BMWS-340) | Sequence Consumption Issue - Duplicated Rack Data Bulk | Not set |
| BMW Spartanburg | [BMWS-318](https://universalsoftware.atlassian.net/browse/BMWS-318) | BMW Supplier Label Reprinting | Not set |
| BMW Spartanburg | [BMWS-297](https://universalsoftware.atlassian.net/browse/BMWS-297) | Outbound Departure Process - Not Departing Commodities | Not set |
| BMW Spartanburg | [BMWS-294](https://universalsoftware.atlassian.net/browse/BMWS-294) | BMW - Bulk JIS Updates - Outbound Rack Master Updates | Not set |
| BMW Spartanburg | [BMWS-279](https://universalsoftware.atlassian.net/browse/BMWS-279) | Visibility of Rack Status is lacking on the outbound process | Not set |
| BMW Spartanburg | [BMWS-265](https://universalsoftware.atlassian.net/browse/BMWS-265) | Seq Web - Bulk Consumption Feed Accuracy Issue | Not set |
| BMW Spartanburg | [BMWS-260](https://universalsoftware.atlassian.net/browse/BMWS-260) | Trailer Status - Inbound Trailers | Not set |
| BMW Spartanburg | [BMWS-258](https://universalsoftware.atlassian.net/browse/BMWS-258) | Trailer history outbound sort trailer id descending | Not set |
| BMW Spartanburg | [BMWS-250](https://universalsoftware.atlassian.net/browse/BMWS-250) | Performance Tuning Ticket | Not set |
| BMW Spartanburg | [BMWS-219](https://universalsoftware.atlassian.net/browse/BMWS-219) | PreSequence Order Commodity Post Process | Not set |
| BMW Spartanburg | [BMWS-218](https://universalsoftware.atlassian.net/browse/BMWS-218) | InventoryDetailsOut adjustment | Not set |
| BMW Spartanburg | [BMWS-211](https://universalsoftware.atlassian.net/browse/BMWS-211) | Add a new field to SequenceOrders table to mark an order as Reorder True/False. | Not set |
| BMW Spartanburg | [BMWS-210](https://universalsoftware.atlassian.net/browse/BMWS-210) | BMW - Inventory ID resolution issue Order Commodities | Not set |
| BMW Spartanburg | [BMWS-192](https://universalsoftware.atlassian.net/browse/BMWS-192) | Orders missing default step and status | Not set |
| BMW Spartanburg | [BMWS-190](https://universalsoftware.atlassian.net/browse/BMWS-190) | Box Move Merge Label Move Error | Not set |
| BMW Spartanburg | [BMWS-183](https://universalsoftware.atlassian.net/browse/BMWS-183) | Inbound Trailer Presequence Finalization Error | Not set |
| BMW Spartanburg | [BMWS-170](https://universalsoftware.atlassian.net/browse/BMWS-170) | ASN Receiving PreSequenced and Box Moving Step Fix | Not set |
| BMW Spartanburg | [BMWS-168](https://universalsoftware.atlassian.net/browse/BMWS-168) | Box Move Staging Process | Not set |
| BMW Spartanburg | [BMWS-161](https://universalsoftware.atlassian.net/browse/BMWS-161) | Order Table Issues | Not set |
| BMW Spartanburg | [BMWS-159](https://universalsoftware.atlassian.net/browse/BMWS-159) | Create 1" x 3" Labels to support BMW | Not set |
| BMW Spartanburg | [BMWS-154](https://universalsoftware.atlassian.net/browse/BMWS-154) | Orders and OrderCommodity Mangement Page | Not set |
| BMW Spartanburg | [BMWS-140](https://universalsoftware.atlassian.net/browse/BMWS-140) | Desktop Site Master URLS Not Deactivating | Not set |
| BMW Spartanburg | [BMWS-138](https://universalsoftware.atlassian.net/browse/BMWS-138) | Bulk ASN Data - Supplier Assignment | Not set |
| BMW Spartanburg | [BMWS-112](https://universalsoftware.atlassian.net/browse/BMWS-112) | Validate Part Lookup | Not set |
| BMW Spartanburg | [BMWS-87](https://universalsoftware.atlassian.net/browse/BMWS-87) | ASN JIS 50 Putaway | Not set |
| BMW Spartanburg | [BMWS-77](https://universalsoftware.atlassian.net/browse/BMWS-77) | ASN JIS 50 Receiving | Not set |
| BMW Spartanburg | [BMWS-74](https://universalsoftware.atlassian.net/browse/BMWS-74) | Remove support for Linker table | Not set |
| BMW Spartanburg | [BMWS-68](https://universalsoftware.atlassian.net/browse/BMWS-68) | Sequence Call Off - SequenceEventLog Support | Not set |
| BMW Spartanburg | [BMWS-67](https://universalsoftware.atlassian.net/browse/BMWS-67) | Sequence Information - SequenceEventLog Support | Not set |
| BMW Spartanburg | [BMWS-63](https://universalsoftware.atlassian.net/browse/BMWS-63) | Remove OrderNumber column from ImportedFileOrderNumberLinker table | Not set |
| BMW Spartanburg | [BMWS-62](https://universalsoftware.atlassian.net/browse/BMWS-62) | Reorder - OrderID Support | Not set |
| BMW Spartanburg | [BMWS-60](https://universalsoftware.atlassian.net/browse/BMWS-60) | Technical Order - OrderID Support | Not set |
| BMW Spartanburg | [BMWS-50](https://universalsoftware.atlassian.net/browse/BMWS-50) | Build Sequence Tracking Report | Not set |
| BMW Spartanburg | [BMWS-49](https://universalsoftware.atlassian.net/browse/BMWS-49) | Create SequenceTrackingLog | Not set |
| BMW Spartanburg | [BMWS-47](https://universalsoftware.atlassian.net/browse/BMWS-47) | Duplicate Inventory records | Not set |
| BMW Spartanburg | [BMWS-44](https://universalsoftware.atlassian.net/browse/BMWS-44) | Saving the label to the printer | Not set |
| BMW Spartanburg | [BMWS-40](https://universalsoftware.atlassian.net/browse/BMWS-40) | Supplier Data from Inventory Upload | Not set |
| BMW Spartanburg | [BMWS-39](https://universalsoftware.atlassian.net/browse/BMWS-39) | Supplier Data from ASN | Not set |
| BMW Spartanburg | [BMWS-38](https://universalsoftware.atlassian.net/browse/BMWS-38) | Trailer Receipt Report Discrepancies | Not set |
| BMW Spartanburg | [BMWS-35](https://universalsoftware.atlassian.net/browse/BMWS-35) | Clear Inventory - Retest Imports | Not set |
| BMW Spartanburg | [BMWS-27](https://universalsoftware.atlassian.net/browse/BMWS-27) | Validate ASN Data Against Physical Load on Trailer | Not set |
| BMW Spartanburg | [BMWS-18](https://universalsoftware.atlassian.net/browse/BMWS-18) | BMW - Inbound Trailer ASN Management | Not set |
| BMW Spartanburg | [BMWS-15](https://universalsoftware.atlassian.net/browse/BMWS-15) | Mass Import Parts and new fields | Not set |
| BMW Spartanburg | [BMWS-14](https://universalsoftware.atlassian.net/browse/BMWS-14) | Ability to implement new part number | Not set |
| Alliance Laundry - General Tickets  | [AL-265](https://universalsoftware.atlassian.net/browse/AL-265) | Alliance User Identity Issues | Not set |
| Alliance Laundry - General Tickets  | [AL-248](https://universalsoftware.atlassian.net/browse/AL-248) | Host New Services | Not set |
| Alliance Laundry - General Tickets  | [AL-246](https://universalsoftware.atlassian.net/browse/AL-246) | Original UAT Environment Refresh | Not set |
| Alliance Laundry - General Tickets  | [AL-239](https://universalsoftware.atlassian.net/browse/AL-239) | Possible Upgrade WarehouseTransfer | Not set |
| Alliance Laundry - General Tickets  | [AL-234](https://universalsoftware.atlassian.net/browse/AL-234) | Migrate the Users | Not set |
| Alliance Laundry - General Tickets  | [AL-74](https://universalsoftware.atlassian.net/browse/AL-74) | Alliance Laundry - Order Number Problem | Not set |
| Alliance Laundry - General Tickets  | [AL-59](https://universalsoftware.atlassian.net/browse/AL-59) | Migrate the service to valaddsql | Not set |
| Alliance Laundry - General Tickets  | [AL-39](https://universalsoftware.atlassian.net/browse/AL-39) | Fix SMTP Configurations for Alliance | Not set |
| Alliance Laundry - General Tickets  | [AL-5](https://universalsoftware.atlassian.net/browse/AL-5) | Alliance Laundry - System Upgrade - FZ/SZ/FC Model | Not set |

### Trent Meyering

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | Highway | [HIG-26](https://universalsoftware.atlassian.net/browse/HIG-26) | update to ULAchEmailService | 2025-07-11 | Packaged | 4 days remaining |
| ✅ | Highway | [HIG-25](https://universalsoftware.atlassian.net/browse/HIG-25) | Carriers use EIN or SSN when setting up their company | 2025-07-14 | Packaged | 7 days remaining |
| 📋 | YMS Project | [YP-47](https://universalsoftware.atlassian.net/browse/YP-47) | Yard Check Module - Menu | Not set | Code Complete | No due date |
| 📋 | PortPro Integrations  | [POR-17](https://universalsoftware.atlassian.net/browse/POR-17) | Location & Job Profile Filters for Workday Demographic API | Not set | Code Complete | No due date |
| 📋 | Highway | [HIG-33](https://universalsoftware.atlassian.net/browse/HIG-33) | Factoring Company - to be updated in ACH queue | Not set | Test | No due date |
| 📋 | Highway | [HIG-22](https://universalsoftware.atlassian.net/browse/HIG-22) | Implement POST Sync Carrier upload data | Not set | Packaged | No due date |
| 📋 | Highway | [HIG-21](https://universalsoftware.atlassian.net/browse/HIG-21) | Implement POST Bulk Upload Carrier Data | Not set | Packaged | No due date |
| 📋 | Highway | [HIG-20](https://universalsoftware.atlassian.net/browse/HIG-20) | Implement Full Update GET request Endpoint: carrier data (once a week) | Not set | Packaged | No due date |
| 📋 | Highway | [HIG-19](https://universalsoftware.atlassian.net/browse/HIG-19) | Implement POST Process to mark Carrier Alerts as processed | Not set | Packaged | No due date |
| 📋 | Highway | [HIG-18](https://universalsoftware.atlassian.net/browse/HIG-18) | Implement Carrier Alerts GET Endpoint: Carrier Data | Not set | Packaged | No due date |
| 📋 | Highway | [HIG-17](https://universalsoftware.atlassian.net/browse/HIG-17) | Implement POST Process to mark onboarding packets complete | Not set | Packaged | No due date |
| 📋 | Highway | [HIG-16](https://universalsoftware.atlassian.net/browse/HIG-16) | Implement Onboarding Packet GET Endpoint: Carrier Data | Not set | Packaged | No due date |
| 📋 | AppDev | [AP-65](https://universalsoftware.atlassian.net/browse/AP-65) | ULCarrierImporter service and ULCarrierSyncService | Not set | Code Complete | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| WES | [WES-2](https://universalsoftware.atlassian.net/browse/WES-2) | Identity domain | 2025-02-07 |
| Highway | [HIG-3](https://universalsoftware.atlassian.net/browse/HIG-3) | Map Highway Carrier API Data | 2025-01-29 |
| Highway | [HIG-2](https://universalsoftware.atlassian.net/browse/HIG-2) | Research FMSCA Data points from the RMIS Xml File | 2025-01-28 |
| WES | [WES-97](https://universalsoftware.atlassian.net/browse/WES-97) | Initial Entity Framework Migration Script | Not set |
| WES | [WES-71](https://universalsoftware.atlassian.net/browse/WES-71) | PowerUnits - Proposal | Not set |
| WES | [WES-62](https://universalsoftware.atlassian.net/browse/WES-62) | ContainerStatus - Proposal | Not set |
| WES | [WES-49](https://universalsoftware.atlassian.net/browse/WES-49) | Initiating Navigation for YMS | Not set |
| WES | [WES-48](https://universalsoftware.atlassian.net/browse/WES-48) | Research ticket for docker to run microservices  | Not set |
| PortPro Integrations  | [POR-6](https://universalsoftware.atlassian.net/browse/POR-6) | for researching columns to support carriers | Not set |
| Highway | [HIG-12](https://universalsoftware.atlassian.net/browse/HIG-12) | Research Carrier Insurance Information Data points from the RMIS XML File | Not set |
| Highway | [HIG-11](https://universalsoftware.atlassian.net/browse/HIG-11) | Map Highway Carrier Insurance Information API Data | Not set |
| Highway | [HIG-9](https://universalsoftware.atlassian.net/browse/HIG-9) | Research Carrier Contact data point from the RMIS XML file | Not set |
| Highway | [HIG-8](https://universalsoftware.atlassian.net/browse/HIG-8) | Map Highway Carrier API Data | Not set |
| Highway | [HIG-6](https://universalsoftware.atlassian.net/browse/HIG-6) | Research Vendor data points from the RMIS XML file | Not set |
| Highway | [HIG-5](https://universalsoftware.atlassian.net/browse/HIG-5) | Map Highway API Data | Not set |
| AppDev | [AP-8](https://universalsoftware.atlassian.net/browse/AP-8) | Research ticket for ABP.io | Not set |

### Tyler Meholic

#### In Progress Items

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |
|--------|---------|---------|------|----------|--------|----------------|
| ✅ | BMW Spartanburg | [BMWS-435](https://universalsoftware.atlassian.net/browse/BMWS-435) | Picking To Sequence Failing To Move Racks | 2025-07-18 | Test | 11 days remaining |
| 📋 | SubZero  | [SBZ-112](https://universalsoftware.atlassian.net/browse/SBZ-112) | Dashboard Displays for TVs | Not set | Review | No due date |
| 📋 | SubZero  | [SBZ-109](https://universalsoftware.atlassian.net/browse/SBZ-109) | PrintLabel page failing to load | Not set | Backlog | No due date |
| 📋 | BMW Spartanburg | [BMWS-456](https://universalsoftware.atlassian.net/browse/BMWS-456) | Scanner - Unable To Skip Racks When Staging | Not set | Test | No due date |
| 📋 | BMW Spartanburg | [BMWS-404](https://universalsoftware.atlassian.net/browse/BMWS-404) | BoxMove By Overpack Not Displaying Error Message | Not set | Test | No due date |
| 📋 | BMW Spartanburg | [BMWS-400](https://universalsoftware.atlassian.net/browse/BMWS-400) | InventoryRepackSerialized Not Deleting Depleted Repacked Material | Not set | Test | No due date |
| 📋 | BMW Spartanburg | [BMWS-399](https://universalsoftware.atlassian.net/browse/BMWS-399) | InventoryRepackSerialized Printing With Wrong Prefix | Not set | Test | No due date |
| 📋 | BMW Spartanburg | [BMWS-398](https://universalsoftware.atlassian.net/browse/BMWS-398) | Android_RepackSetup And Android_RepackVerify Transaction Mismatch | Not set | In Progress | No due date |
| 📋 | BMW Spartanburg | [BMWS-384](https://universalsoftware.atlassian.net/browse/BMWS-384) | BMW - SQL - Generic Update Calls - Transactions | Not set | Test | No due date |

#### Completed Items

| Project | Jira ID | Name | Due Date |
|---------|---------|------|----------|
| SubZero  | [SBZ-113](https://universalsoftware.atlassian.net/browse/SBZ-113) | Create Repo for SubZero | 2025-06-18 |
| BMW Spartanburg | [BMWS-220](https://universalsoftware.atlassian.net/browse/BMWS-220) | Record LotNumber (CallOffNumber) when departing outbound trailer | 2025-04-25 |
| SubZero  | [SBZ-34](https://universalsoftware.atlassian.net/browse/SBZ-34) | Add Manual Order - Copy/Paste Version | 2024-11-22 |
| Ford Steering Stanley Gault | [FSSG-24](https://universalsoftware.atlassian.net/browse/FSSG-24) | Printing Data Alignment Issue  | 2024-01-15 |
| Value Added  | [VAL-414](https://universalsoftware.atlassian.net/browse/VAL-414) | Sub Zero Business - Repository Adjustment | Not set |
| Value Added  | [VAL-399](https://universalsoftware.atlassian.net/browse/VAL-399) | Factory Zero Business - Repository Adjustment | Not set |
| Value Added  | [VAL-280](https://universalsoftware.atlassian.net/browse/VAL-280) | Update Purge Mechanics in FCA Huber WMS | Not set |
| Shakopee XDock - General Tickets  | [SHAK-3](https://universalsoftware.atlassian.net/browse/SHAK-3) | WMS Column Field Sizing | Not set |
| SubZero  | [SBZ-110](https://universalsoftware.atlassian.net/browse/SBZ-110) | PrintLabel scanner page - Requiring serial # to print label instead of generating missing serial# | Not set |
| SubZero  | [SBZ-108](https://universalsoftware.atlassian.net/browse/SBZ-108) | CreateOrder scanner page - handling priority when an order already exists for given route | Not set |
| SubZero  | [SBZ-106](https://universalsoftware.atlassian.net/browse/SBZ-106) | SubZero CreateOrder page - order created without a user attached | Not set |
| SubZero  | [SBZ-103](https://universalsoftware.atlassian.net/browse/SBZ-103) | Desktop - UserScanReport - Name Not Pulling From IdentityTrackedEvent | Not set |
| SubZero  | [SBZ-90](https://universalsoftware.atlassian.net/browse/SBZ-90) | Scanner - InventoryIn - Missing Packsize and PackCount | Not set |
| SubZero  | [SBZ-89](https://universalsoftware.atlassian.net/browse/SBZ-89) | Desktop - Open Orders Widget Incorrect | Not set |
| SubZero  | [SBZ-88](https://universalsoftware.atlassian.net/browse/SBZ-88) | Desktop - OrderHistory - Handle Null NextDueDate | Not set |
| SubZero  | [SBZ-87](https://universalsoftware.atlassian.net/browse/SBZ-87) | FIFO - Bug - MFG Date and Creation Date Problem - 9/30 | Not set |
| SubZero  | [SBZ-85](https://universalsoftware.atlassian.net/browse/SBZ-85) | Scanner - BoxMove - Can't BoxMove By Overpack | Not set |
| SubZero  | [SBZ-84](https://universalsoftware.atlassian.net/browse/SBZ-84) | Scanner - Create Order - UserName is blank | Not set |
| SubZero  | [SBZ-83](https://universalsoftware.atlassian.net/browse/SBZ-83) | Scanner - Repack - Not Checking VQQ | Not set |
| SubZero  | [SBZ-82](https://universalsoftware.atlassian.net/browse/SBZ-82) | Scanner - Inventory Repack  - Getting Duplicate Detail Records | Not set |
| SubZero  | [SBZ-81](https://universalsoftware.atlassian.net/browse/SBZ-81) | Desktop - EditRelease_CopyPaste Not Matching Pasted Order | Not set |
| SubZero  | [SBZ-80](https://universalsoftware.atlassian.net/browse/SBZ-80) | Create Order - Route Types Blank Bug | Not set |
| SubZero  | [SBZ-72](https://universalsoftware.atlassian.net/browse/SBZ-72) | Migrate GFZGT-235 to Sub Zero | Not set |
| SubZero  | [SBZ-70](https://universalsoftware.atlassian.net/browse/SBZ-70) | Desktop - Label Printer Label Types | Not set |
| SubZero  | [SBZ-69](https://universalsoftware.atlassian.net/browse/SBZ-69) | Create WMS User Account For Mike Taberski | Not set |
| SubZero  | [SBZ-67](https://universalsoftware.atlassian.net/browse/SBZ-67) | Sub Zero Order Creation by Route Adjustment | Not set |
| SubZero  | [SBZ-66](https://universalsoftware.atlassian.net/browse/SBZ-66) | Migrate GFZGT-228 to Sub Zero | Not set |
| SubZero  | [SBZ-65](https://universalsoftware.atlassian.net/browse/SBZ-65) | Migrate GFZGT-226 to Sub Zero | Not set |
| SubZero  | [SBZ-64](https://universalsoftware.atlassian.net/browse/SBZ-64) | Migrate GFZGT-227 to Sub Zero | Not set |
| SubZero  | [SBZ-62](https://universalsoftware.atlassian.net/browse/SBZ-62) | Migrate GFZGT-225 to Sub Zero | Not set |
| SubZero  | [SBZ-60](https://universalsoftware.atlassian.net/browse/SBZ-60) | Migrate GFZGT-231 to Sub Zero | Not set |
| SubZero  | [SBZ-57](https://universalsoftware.atlassian.net/browse/SBZ-57) | Migrate GFZGT-234 to Sub Zero | Not set |
| SubZero  | [SBZ-53](https://universalsoftware.atlassian.net/browse/SBZ-53) | Need the facility address for labels and local paperwork | Not set |
| SubZero  | [SBZ-49](https://universalsoftware.atlassian.net/browse/SBZ-49) | Trailer History - Move Selected Items to Trailer Feature Removal | Not set |
| SubZero  | [SBZ-48](https://universalsoftware.atlassian.net/browse/SBZ-48) | TrailerStatus and AddManualAsnToTrailer  | Not set |
| SubZero  | [SBZ-37](https://universalsoftware.atlassian.net/browse/SBZ-37) | Print Label Page - Serial Passthrough support | Not set |
| SubZero  | [SBZ-36](https://universalsoftware.atlassian.net/browse/SBZ-36) | Manual Order Creation - Route Based | Not set |
| SubZero  | [SBZ-32](https://universalsoftware.atlassian.net/browse/SBZ-32) | Print Label Page - Require Selection of MFG Date | Not set |
| SubZero  | [SBZ-30](https://universalsoftware.atlassian.net/browse/SBZ-30) | Desktop - NCP - Verbiage Change | Not set |
| SubZero  | [SBZ-28](https://universalsoftware.atlassian.net/browse/SBZ-28) | Report - NCP - Verbiage Change | Not set |
| SubZero  | [SBZ-26](https://universalsoftware.atlassian.net/browse/SBZ-26) | Desktop - InventoryOnHand - Only Displays When Net Qty Above 0 | Not set |
| SubZero  | [SBZ-25](https://universalsoftware.atlassian.net/browse/SBZ-25) | Scanner - Outbound - No Stock Auto Closed Order Not Closing Properly | Not set |
| SubZero  | [SBZ-24](https://universalsoftware.atlassian.net/browse/SBZ-24) | Desktop - Trailer History Part And Serial Source Change Away From ASN | Not set |
| SubZero  | [SBZ-18](https://universalsoftware.atlassian.net/browse/SBZ-18) | Scanner - InventoryIn - Does Not Support PackSize and PackCount | Not set |
| SubZero  | [SBZ-17](https://universalsoftware.atlassian.net/browse/SBZ-17) | Clean Up Sorting Of Pick Details | Not set |
| SubZero  | [SBZ-16](https://universalsoftware.atlassian.net/browse/SBZ-16) | Scanner - InventoryOut - InventoryOut Record Has Incorrect Quantity When Repacking | Not set |
| SubZero  | [SBZ-14](https://universalsoftware.atlassian.net/browse/SBZ-14) | Scanner - Outbound - Notes When Creating Order Are Not Displaying On Scanner | Not set |
| SubZero  | [SBZ-13](https://universalsoftware.atlassian.net/browse/SBZ-13) | Update the Bulk and Small lot Android Update - Shared Functions | Not set |
| SubZero  | [SBZ-9](https://universalsoftware.atlassian.net/browse/SBZ-9) | Print Label Page - Improvement Rearrange Quantity Calculations | Not set |
| SubZero  | [SBZ-8](https://universalsoftware.atlassian.net/browse/SBZ-8) | Inventory In - Blind Receipt Process Improvment | Not set |
| SubZero  | [SBZ-7](https://universalsoftware.atlassian.net/browse/SBZ-7) | Desktop - EditRelease_CopyPaste - Strips Dashes | Not set |
| SubZero  | [SBZ-6](https://universalsoftware.atlassian.net/browse/SBZ-6) | Desktop - OpenOrders - Displays Closed Orders That Are Incomplete and Do Not Have Order Details | Not set |
| SubZero  | [SBZ-5](https://universalsoftware.atlassian.net/browse/SBZ-5) | Desktop - AddRelease - Required Priority Handling | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-110](https://universalsoftware.atlassian.net/browse/RLNGTN-110) | Desktop - LabelPrint | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-100](https://universalsoftware.atlassian.net/browse/RLNGTN-100) | HR Kiosk Data Importer | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-92](https://universalsoftware.atlassian.net/browse/RLNGTN-92) | Telerik Subscription Service | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-91](https://universalsoftware.atlassian.net/browse/RLNGTN-91) | Arl PPS Importer | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-90](https://universalsoftware.atlassian.net/browse/RLNGTN-90) | GitSync | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-89](https://universalsoftware.atlassian.net/browse/RLNGTN-89) | Android Application Service | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-88](https://universalsoftware.atlassian.net/browse/RLNGTN-88) | PPS Email Importer | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-87](https://universalsoftware.atlassian.net/browse/RLNGTN-87) | Android Business Service | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-86](https://universalsoftware.atlassian.net/browse/RLNGTN-86) | Process 856 | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-84](https://universalsoftware.atlassian.net/browse/RLNGTN-84) | Arl MGO Updater | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-83](https://universalsoftware.atlassian.net/browse/RLNGTN-83) | Reporting Services Configuration Manager | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-82](https://universalsoftware.atlassian.net/browse/RLNGTN-82) | Webservice | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-81](https://universalsoftware.atlassian.net/browse/RLNGTN-81) | Desktop | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-79](https://universalsoftware.atlassian.net/browse/RLNGTN-79) | PPS Importer | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-78](https://universalsoftware.atlassian.net/browse/RLNGTN-78) | Part Lookup Fifo Not Accurate | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-74](https://universalsoftware.atlassian.net/browse/RLNGTN-74) | Paystub display | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-73](https://universalsoftware.atlassian.net/browse/RLNGTN-73) | UserID | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-72](https://universalsoftware.atlassian.net/browse/RLNGTN-72) | Android_UpdateInventoryDetailsLocationOverpack | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-70](https://universalsoftware.atlassian.net/browse/RLNGTN-70) | IPageInvokeInterface | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-64](https://universalsoftware.atlassian.net/browse/RLNGTN-64) | InventoryValidation_IsLocationValid | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-63](https://universalsoftware.atlassian.net/browse/RLNGTN-63) | Android_UpdateReleaseMasterOutboundReturnPrinter | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-62](https://universalsoftware.atlassian.net/browse/RLNGTN-62) | Android_AddInventoryDetailsASN | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-57](https://universalsoftware.atlassian.net/browse/RLNGTN-57) | Android_UpdateInboundTrailerClosedByID | Not set |
| GM - Arlington - General Tickets  | [RLNGTN-56](https://universalsoftware.atlassian.net/browse/RLNGTN-56) | Cummins_UpdateInventoryQty | Not set |
| Navistar San Antonio | [NVSSA-61](https://universalsoftware.atlassian.net/browse/NVSSA-61) | Android - Job Move Issue Still | Not set |
| Navistar San Antonio | [NVSSA-47](https://universalsoftware.atlassian.net/browse/NVSSA-47) | InventoryAdjust - Not Able To Adjust All Types | Not set |
| Navistar San Antonio | [NVSSA-24](https://universalsoftware.atlassian.net/browse/NVSSA-24) | Desktop - Inbound - Add ASN | Not set |
| Navistar San Antonio | [NVSSA-22](https://universalsoftware.atlassian.net/browse/NVSSA-22) | Desktop - Inbound - Trailer History | Not set |
| Navistar San Antonio | [NVSSA-21](https://universalsoftware.atlassian.net/browse/NVSSA-21) | Desktop - Inbound - Trailer Status | Not set |
| Navistar San Antonio | [NVSSA-15](https://universalsoftware.atlassian.net/browse/NVSSA-15) | Scanner - Inventory ASN | Not set |
| Navistar San Antonio | [NVSSA-8](https://universalsoftware.atlassian.net/browse/NVSSA-8) | Inventory Adjust - Part Listing Generic Results | Not set |
| Navistar San Antonio | [NVSSA-7](https://universalsoftware.atlassian.net/browse/NVSSA-7) | Navistar - Desktop - Scan User Permissions - Missing Permissions | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-274](https://universalsoftware.atlassian.net/browse/GMFZGT-274) | GM PPS Importer - Route Retrieval | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-267](https://universalsoftware.atlassian.net/browse/GMFZGT-267) | IdentityTrackedEvent ID Clean Up | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-266](https://universalsoftware.atlassian.net/browse/GMFZGT-266) | Outbound Trailer History - Time Out Fix | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-265](https://universalsoftware.atlassian.net/browse/GMFZGT-265) | Trailer History - ASNSupplierName | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-263](https://universalsoftware.atlassian.net/browse/GMFZGT-263) | NCP Quality Defect Types - Not Adding | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-262](https://universalsoftware.atlassian.net/browse/GMFZGT-262) | Desktop - EditRelease Typo | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-259](https://universalsoftware.atlassian.net/browse/GMFZGT-259) | Part Receipts - Pickface Adjust Issue - 7-11 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-258](https://universalsoftware.atlassian.net/browse/GMFZGT-258) | Pickface Move Displaying Wrong Location Error Incorrectly | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-256](https://universalsoftware.atlassian.net/browse/GMFZGT-256) | Open Orders Page - Complete Button Not Working | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-255](https://universalsoftware.atlassian.net/browse/GMFZGT-255) | NCP Ticket Creation Does not work | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-254](https://universalsoftware.atlassian.net/browse/GMFZGT-254) | Inventory Location Pages - Logging for UserID is incorrect | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-253](https://universalsoftware.atlassian.net/browse/GMFZGT-253) | FIFO - Bug - MFG Date and Creation Date Problem - 9/30 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-252](https://universalsoftware.atlassian.net/browse/GMFZGT-252) | Pickface Mins Issue - 7-11 | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-251](https://universalsoftware.atlassian.net/browse/GMFZGT-251) | Investigate Duplicate ASN Received Records | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-249](https://universalsoftware.atlassian.net/browse/GMFZGT-249) | GetFIFO - FIFO Column Use | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-243](https://universalsoftware.atlassian.net/browse/GMFZGT-243) | Order Priority Not Filling In | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-239](https://universalsoftware.atlassian.net/browse/GMFZGT-239) | TrailerHistory.aspx Page - Selecting Trailer Does Not Show Related ASNs | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-236](https://universalsoftware.atlassian.net/browse/GMFZGT-236) | Part Receipt Details Bug | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-227](https://universalsoftware.atlassian.net/browse/GMFZGT-227) | Asn Location Management Page - Location Field Length | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-225](https://universalsoftware.atlassian.net/browse/GMFZGT-225) | Release Master - Missing Creation Date for Manually Created Orders | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-224](https://universalsoftware.atlassian.net/browse/GMFZGT-224) | Outbound - Scan Pickface Location as Pickface Serial Number | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-221](https://universalsoftware.atlassian.net/browse/GMFZGT-221) | Part Receipts Tab (Part Lookup) and Inbound Trailer History not displaying correct info | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-219](https://universalsoftware.atlassian.net/browse/GMFZGT-219) | Inventory Adjust - Bug Creating Pickfaces | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-218](https://universalsoftware.atlassian.net/browse/GMFZGT-218) | Desktop - Trailer Status - Delete Scan Feature Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-216](https://universalsoftware.atlassian.net/browse/GMFZGT-216) | InventoryAdjust - CLocs Does Not Check Part Number | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-215](https://universalsoftware.atlassian.net/browse/GMFZGT-215) | Desktop - Trailer Status - Clear Scans Feature Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-213](https://universalsoftware.atlassian.net/browse/GMFZGT-213) | Shipped Containers Tab Data | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-209](https://universalsoftware.atlassian.net/browse/GMFZGT-209) | Location Audit Model Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-207](https://universalsoftware.atlassian.net/browse/GMFZGT-207) | Missing Info on Outbound Trailer History | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-205](https://universalsoftware.atlassian.net/browse/GMFZGT-205) | NonNet Location Fix - Update/Delete/Insert Recalculation Issue | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-202](https://universalsoftware.atlassian.net/browse/GMFZGT-202) | Trailer Receipt Report Error | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-200](https://universalsoftware.atlassian.net/browse/GMFZGT-200) | Part Lookup - Part Receipts Bug - Row Selection | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-199](https://universalsoftware.atlassian.net/browse/GMFZGT-199) | Trailer Receipt Report Issue | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-197](https://universalsoftware.atlassian.net/browse/GMFZGT-197) | Fix Validating Part Number by Aliases Return Type | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-196](https://universalsoftware.atlassian.net/browse/GMFZGT-196) | Turn off Validating Part Number by Aliases | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-193](https://universalsoftware.atlassian.net/browse/GMFZGT-193) | Importer Setup UAT | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-192](https://universalsoftware.atlassian.net/browse/GMFZGT-192) | UI Updates Open Orders/ Order History | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-185](https://universalsoftware.atlassian.net/browse/GMFZGT-185) | MGO Password Updated - Web Config | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-176](https://universalsoftware.atlassian.net/browse/GMFZGT-176) | Label Printer Management Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-169](https://universalsoftware.atlassian.net/browse/GMFZGT-169) | Research - OpenOrders and OrderHistory Close Trailer Button | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-166](https://universalsoftware.atlassian.net/browse/GMFZGT-166) | Minomi Repack | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-163](https://universalsoftware.atlassian.net/browse/GMFZGT-163) | Update Scanning to Handle No Quantity Field | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-161](https://universalsoftware.atlassian.net/browse/GMFZGT-161) | Print Label Page - Serial Passthrough support | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-154](https://universalsoftware.atlassian.net/browse/GMFZGT-154) | Test Outbound Shipping Feature | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-151](https://universalsoftware.atlassian.net/browse/GMFZGT-151) | Factory Zero - PPS File Importer | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-150](https://universalsoftware.atlassian.net/browse/GMFZGT-150) | Migrate CreateOrder from SubZero to FactoryZero | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-149](https://universalsoftware.atlassian.net/browse/GMFZGT-149) | ASN Receiving Issue - Non Serialized Inventory | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-145](https://universalsoftware.atlassian.net/browse/GMFZGT-145) | Websites - Initial Migration Effort | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-138](https://universalsoftware.atlassian.net/browse/GMFZGT-138) | CLoc Move Scanner Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-137](https://universalsoftware.atlassian.net/browse/GMFZGT-137) | Pickface Move Scanner Page | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-134](https://universalsoftware.atlassian.net/browse/GMFZGT-134) | Part Receipts | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-133](https://universalsoftware.atlassian.net/browse/GMFZGT-133) | SSRS Report Viewer Version | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-132](https://universalsoftware.atlassian.net/browse/GMFZGT-132) | BoxMove - CLoc and Pickface Logging | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-127](https://universalsoftware.atlassian.net/browse/GMFZGT-127) | Part Master - New Columns | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-121](https://universalsoftware.atlassian.net/browse/GMFZGT-121) | InventoryDetailsOut - Not Recording the ContainerQtyAsReceived | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-119](https://universalsoftware.atlassian.net/browse/GMFZGT-119) | InventoryDetailsOut - TransactionType backfill | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-117](https://universalsoftware.atlassian.net/browse/GMFZGT-117) | Migrate use of Trailers to InboundTrailers | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-116](https://universalsoftware.atlassian.net/browse/GMFZGT-116) | InboundTrailerDetails - Add ManualAsnMasterID | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-115](https://universalsoftware.atlassian.net/browse/GMFZGT-115) | InventoryDetailsOut - Use TransactionType | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-114](https://universalsoftware.atlassian.net/browse/GMFZGT-114) | InventoryDetailsOut - Add ManualAsnMasterID / ManualAsnDetailID | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-113](https://universalsoftware.atlassian.net/browse/GMFZGT-113) | PickCount Calculations | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-110](https://universalsoftware.atlassian.net/browse/GMFZGT-110) | Migrate 257 to Sub Zero | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-95](https://universalsoftware.atlassian.net/browse/GMFZGT-95) | No Pick Report | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-94](https://universalsoftware.atlassian.net/browse/GMFZGT-94) | BoxMove Logging Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-93](https://universalsoftware.atlassian.net/browse/GMFZGT-93) | Inventory Repack Serialized Rack | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-92](https://universalsoftware.atlassian.net/browse/GMFZGT-92) | CLOC Move Adjustment - Partial Quantities | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-88](https://universalsoftware.atlassian.net/browse/GMFZGT-88) | Roll No Stocks to a new order when original pick is closed | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-79](https://universalsoftware.atlassian.net/browse/GMFZGT-79) | Hot Shot Report | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-74](https://universalsoftware.atlassian.net/browse/GMFZGT-74) | Pick Face Min to Stock Sort | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-73](https://universalsoftware.atlassian.net/browse/GMFZGT-73) | Scanner Label Printing Improvement - Error Reporting | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-72](https://universalsoftware.atlassian.net/browse/GMFZGT-72) | InventoryAdjust - Sequence Giving Wrong Errors | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-71](https://universalsoftware.atlassian.net/browse/GMFZGT-71) | Scan Users List Not Adding Username | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-70](https://universalsoftware.atlassian.net/browse/GMFZGT-70) | Suppliers Table - AsnSupplierName - SupplierID | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-67](https://universalsoftware.atlassian.net/browse/GMFZGT-67) | TRL - Trailer Prefix Testing | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-53](https://universalsoftware.atlassian.net/browse/GMFZGT-53) | No Stock Process Refactor | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-47](https://universalsoftware.atlassian.net/browse/GMFZGT-47) | Part Lookup - Standard Pack / Inventory Qty - Decimals Format as Integer | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-43](https://universalsoftware.atlassian.net/browse/GMFZGT-43) | ASN Qty in part receipts | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-41](https://universalsoftware.atlassian.net/browse/GMFZGT-41) | WMS MIN & MAX Reports | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-38](https://universalsoftware.atlassian.net/browse/GMFZGT-38) | Part Receipt scans | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-34](https://universalsoftware.atlassian.net/browse/GMFZGT-34) | Improve process by introducing new ID columns - InventoryDetailsOut/InboundTrailerDetails | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-32](https://universalsoftware.atlassian.net/browse/GMFZGT-32) | Min Violations Mailer Improvement | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-29](https://universalsoftware.atlassian.net/browse/GMFZGT-29) | Desktop - AddRelease Page NextDueDate Overwrite | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-26](https://universalsoftware.atlassian.net/browse/GMFZGT-26) | Outbound Trailer History Not Displaying Parts Attached to Trailer | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-23](https://universalsoftware.atlassian.net/browse/GMFZGT-23) | Desktop - Trailer Status - Serials On Trailer Source Update | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-21](https://universalsoftware.atlassian.net/browse/GMFZGT-21) | Pick to Trailer Card View - RawBarcode Trailer Number | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-19](https://universalsoftware.atlassian.net/browse/GMFZGT-19) | MinLog and MaxLog Violation Emails | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-18](https://universalsoftware.atlassian.net/browse/GMFZGT-18) | Repack Serialized Supervisor Authorization | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-17](https://universalsoftware.atlassian.net/browse/GMFZGT-17) | Repack Racks Label Print | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-13](https://universalsoftware.atlassian.net/browse/GMFZGT-13) | Correct Shipped Containers Tab SQL and UI To Match | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-11](https://universalsoftware.atlassian.net/browse/GMFZGT-11) | GenerareHourlyMinLog Sequencing | Not set |
| GM - Factory Zero - General Tickets  | [GMFZGT-9](https://universalsoftware.atlassian.net/browse/GMFZGT-9) | CLocMove Quantity To Fill Displays Wrong Number | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-64](https://universalsoftware.atlassian.net/browse/GFCGT-64) | Desktop - Print Label Page Modifications | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-63](https://universalsoftware.atlassian.net/browse/GFCGT-63) | Inbound Trailers UserName Issue | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-18](https://universalsoftware.atlassian.net/browse/GFCGT-18) | Flint Continental - Pickface Move Issue - 02/06/25 | Not set |
| GM - Flint Continental - General Tickets  | [GFCGT-11](https://universalsoftware.atlassian.net/browse/GFCGT-11) | Scanner - Outbound - Complete Order When No Remaining Available Picks | Not set |
| Ford Steering Stanley Gault | [FSSG-38](https://universalsoftware.atlassian.net/browse/FSSG-38) | Deleting Printer from PrinterManagement.aspx | Not set |
| Ford Steering Stanley Gault | [FSSG-32](https://universalsoftware.atlassian.net/browse/FSSG-32) | Implement the Captcha Form | Not set |
| Ford Steering Stanley Gault | [FSSG-4](https://universalsoftware.atlassian.net/browse/FSSG-4) | Orders - Manual Add Verification | Not set |
| Ford Steering Stanley Gault | [FSSG-3](https://universalsoftware.atlassian.net/browse/FSSG-3) | Outbound BOL with Shipment | Not set |
| BMW Spartanburg | [BMWS-375](https://universalsoftware.atlassian.net/browse/BMWS-375) | Scanner - CLocMove - Not Deleting Serials When Moved Into CLoc Bucket | Not set |
| BMW Spartanburg | [BMWS-350](https://universalsoftware.atlassian.net/browse/BMWS-350) | Add an error on return using box move page | Not set |
| BMW Spartanburg | [BMWS-316](https://universalsoftware.atlassian.net/browse/BMWS-316) | Implement scans for non-serialized inventory  | Not set |
| BMW Spartanburg | [BMWS-308](https://universalsoftware.atlassian.net/browse/BMWS-308) | Issue with presequenced page | Not set |
| BMW Spartanburg | [BMWS-302](https://universalsoftware.atlassian.net/browse/BMWS-302) | Staging to Trailer - scan racks in order presented in OBTrailerDetails table | Not set |
| BMW Spartanburg | [BMWS-301](https://universalsoftware.atlassian.net/browse/BMWS-301) | Staging to Trailer page - get all trailer details regardless of status | Not set |
| BMW Spartanburg | [BMWS-300](https://universalsoftware.atlassian.net/browse/BMWS-300) | Staging to Trailer page - change to tile view | Not set |
| BMW Spartanburg | [BMWS-299](https://universalsoftware.atlassian.net/browse/BMWS-299) | Staging to Trailer page - Notify user and close trailer once all racks have been scanned | Not set |
| BMW Spartanburg | [BMWS-292](https://universalsoftware.atlassian.net/browse/BMWS-292) | New scanner page to load the trailer with racks in correct order | Not set |
| BMW Spartanburg | [BMWS-263](https://universalsoftware.atlassian.net/browse/BMWS-263) | PickToSequence scanner page - improve error handling | Not set |
| BMW Spartanburg | [BMWS-256](https://universalsoftware.atlassian.net/browse/BMWS-256) | update the PrintLabel page on the desktop to include the first property of the label | Not set |
| BMW Spartanburg | [BMWS-249](https://universalsoftware.atlassian.net/browse/BMWS-249) | Ability to reprint B10 Micro Labels from the OutboundRack page on the desktop | Not set |
| BMW Spartanburg | [BMWS-248](https://universalsoftware.atlassian.net/browse/BMWS-248) | InventoryAsnPreSeq page not logging the label printers | Not set |
| BMW Spartanburg | [BMWS-246](https://universalsoftware.atlassian.net/browse/BMWS-246) | BoxMove scanner page - remove requirement for Quantity scan | Not set |
| BMW Spartanburg | [BMWS-236](https://universalsoftware.atlassian.net/browse/BMWS-236) | PickToSequence scanner page - display error message returned from SQL | Not set |
| BMW Spartanburg | [BMWS-225](https://universalsoftware.atlassian.net/browse/BMWS-225) | Add the inbound rack number to the merge label | Not set |
| BMW Spartanburg | [BMWS-224](https://universalsoftware.atlassian.net/browse/BMWS-224) | Add to the Pre Sequence Inbound ASN page | Not set |
| BMW Spartanburg | [BMWS-199](https://universalsoftware.atlassian.net/browse/BMWS-199) | PickToSequence (Replenishment) page - update the RackStatusIDs as they have been updated | Not set |
| BMW Spartanburg | [BMWS-198](https://universalsoftware.atlassian.net/browse/BMWS-198) | InventoryAsnPreSeq crash when scanning more racks than expected | Not set |
| BMW Spartanburg | [BMWS-197](https://universalsoftware.atlassian.net/browse/BMWS-197) | Update PickToSequence (Replenishment) page to display the InboundPartFamily | Not set |
| BMW Spartanburg | [BMWS-178](https://universalsoftware.atlassian.net/browse/BMWS-178) | Replenishment page - OutboundPartFamilies | Not set |
| BMW Spartanburg | [BMWS-167](https://universalsoftware.atlassian.net/browse/BMWS-167) | InventoryASN Pre-Sequence page - drop scanning for Load ID  | Not set |
| BMW Spartanburg | [BMWS-165](https://universalsoftware.atlassian.net/browse/BMWS-165) | Inventory ASN stored procedures | Not set |
| BMW Spartanburg | [BMWS-152](https://universalsoftware.atlassian.net/browse/BMWS-152) | Replenishment page | Not set |
| BMW Spartanburg | [BMWS-128](https://universalsoftware.atlassian.net/browse/BMWS-128) | Inbound ASN Location Management - Add nullable Label Printer | Not set |
| BMW Spartanburg | [BMWS-111](https://universalsoftware.atlassian.net/browse/BMWS-111) | Validate Box Moves + Merge Label | Not set |
| BMW Spartanburg | [BMWS-108](https://universalsoftware.atlassian.net/browse/BMWS-108) | Print Overpack Label | Not set |
| BMW Spartanburg | [BMWS-107](https://universalsoftware.atlassian.net/browse/BMWS-107) | Pre-sequenced Material Scanner (back-end) | Not set |
| BMW Spartanburg | [BMWS-64](https://universalsoftware.atlassian.net/browse/BMWS-64) | Label Getting Cut Off | Not set |
| BMW Spartanburg | [BMWS-48](https://universalsoftware.atlassian.net/browse/BMWS-48) | Inventory ASN Page - Not recording Packsize properly | Not set |
| BMW Spartanburg | [BMWS-43](https://universalsoftware.atlassian.net/browse/BMWS-43) | Scanner Location Info Label | Not set |
| BMW Spartanburg | [BMWS-42](https://universalsoftware.atlassian.net/browse/BMWS-42) | Scanner Print Label | Not set |
| BMW Spartanburg | [BMWS-41](https://universalsoftware.atlassian.net/browse/BMWS-41) | Desktop Print Label | Not set |
| BMW Spartanburg | [BMWS-37](https://universalsoftware.atlassian.net/browse/BMWS-37) | Support the new label | Not set |
| Alliance Laundry - General Tickets  | [AL-241](https://universalsoftware.atlassian.net/browse/AL-241) | Migrate PartLookup_Alliance | Not set |
| Alliance Laundry - General Tickets  | [AL-226](https://universalsoftware.atlassian.net/browse/AL-226) | Migrate Release_CopyPaste_Alliance | Not set |
| Alliance Laundry - General Tickets  | [AL-207](https://universalsoftware.atlassian.net/browse/AL-207) | Desktop - Environment Link Missing | Not set |

## Project on Hold (BLOCKED)

| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|--------|---------|---------|------|----------|--------|----------------|--------------|----------|
| ⚠️ | Container Management System | [CMS-3](https://universalsoftware.atlassian.net/browse/CMS-3) | CMS UI Update - Add Loading Panels | Not set | Project on Hold (BLOCKED) | No due date | 7 days | Adam Caldwell |

