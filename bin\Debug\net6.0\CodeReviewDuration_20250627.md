# Code Review Duration Report

## Tickets Currently in Review

| Status | Ticket | Summary | Assignee | Days in Review | Entered Review |
|--------|--------|---------|----------|----------------|----------------|
| 🚨 | [NVSSA-5](https://universalsoftware.atlassian.net/browse/NVSSA-5) | Navistar - Printer Managment Page | Shayne Vallad | 142 | 2025-02-05 |
| 🚨 | [SBZ-10](https://universalsoftware.atlassian.net/browse/SBZ-10) | ASN Location Management Page - What If Scenario - No Printer | <PERSON> | 99 | 2025-03-19 |
| 🚨 | [WES-106](https://universalsoftware.atlassian.net/browse/WES-106) | Migrating the outbound status (ready to depart popup) from YMS to WES - frontend | <PERSON> | 81 | 2025-04-07 |
| 🚨 | [BOEIN-37](https://universalsoftware.atlassian.net/browse/BOEIN-37) | Migrate Label Printer page to desktop app | <PERSON> | 81 | 2025-04-07 |
| 🚨 | [GMFZGT-285](https://universalsoftware.atlassian.net/browse/GMFZGT-285) | Add Feature to View Completed but Not Dispatched Orders | Rakhi Garg | 70 | 2025-04-17 |
| 🚨 | [WSSG-43](https://universalsoftware.atlassian.net/browse/WSSG-43) | Cycle Count Function Seats WMS | Igor Ivanovski | 45 | 2025-05-12 |
| 🚨 | [WES-107](https://universalsoftware.atlassian.net/browse/WES-107) | Migrating the outbound status report (generate report button) from YMS to WES | Trent Meyering | 24 | 2025-06-02 |
| 🚨 | [WES-61](https://universalsoftware.atlassian.net/browse/WES-61) | Migrating the outbound status report from YMS to WES - backend | Trent Meyering | 24 | 2025-06-02 |
| 🚨 | [GFCGT-77](https://universalsoftware.atlassian.net/browse/GFCGT-77) | Dashboard Time Range | Alex DeLuca | 24 | 2025-06-02 |
| 🚨 | [FLEET-41](https://universalsoftware.atlassian.net/browse/FLEET-41) | Asset detail - Equipment tab | R Sanders | 22 | 2025-06-04 |
| 🚨 | [YP-76](https://universalsoftware.atlassian.net/browse/YP-76) | QA Unit Testing of YP-32: Sorting By Categories - Carrier Report | George Moshi | 20 | 2025-06-06 |
| 🚨 | [WES-167](https://universalsoftware.atlassian.net/browse/WES-167) | Verbiage Changes | Trent Meyering | 20 | 2025-06-06 |
| 🚨 | [YP-119](https://universalsoftware.atlassian.net/browse/YP-119) | Add SCAC column to carrier | Ludmil Gueorguiev | 16 | 2025-06-11 |
| 🚨 | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | Igor Ivanovski | 15 | 2025-06-12 |
| 🚨 | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | Rakhi Garg | 13 | 2025-06-13 |
| 🚨 | [WES-152](https://universalsoftware.atlassian.net/browse/WES-152) | Migrating Docks (empty - action menu - add evidence) from YMS to WES | Trent Meyering | 13 | 2025-06-13 |
| 🚨 | [WES-151](https://universalsoftware.atlassian.net/browse/WES-151) | Migrating Docks (outbound - action menu - add evidence) from YMS to WES | Trent Meyering | 13 | 2025-06-13 |
| 🚨 | [WES-150](https://universalsoftware.atlassian.net/browse/WES-150) | Migrating Docks (inbound - action menu - add evidence) from YMS to WES | Trent Meyering | 13 | 2025-06-13 |
| 🚨 | [WES-140](https://universalsoftware.atlassian.net/browse/WES-140) | Migrating Container Page (action menu - add evidence) - At Docks - from YMS to WES | Trent Meyering | 13 | 2025-06-13 |
| 🚨 | [WES-131](https://universalsoftware.atlassian.net/browse/WES-131) | Migrating Container Page (action menu - see history - attachments) - At Docks - from YMS to WES | Trent Meyering | 13 | 2025-06-13 |
| 🚨 | [WES-124](https://universalsoftware.atlassian.net/browse/WES-124) | Migrating Container Page (action menu - see history - attachment) - In Yard - from YMS to WES | Trent Meyering | 13 | 2025-06-13 |
| 🚨 | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | Alex Gonzalez | 11 | 2025-06-16 |
| 🚨 | [YP-123](https://universalsoftware.atlassian.net/browse/YP-123) | add scac to the display, sort and search by Scac in Carrier Master | Ludmil Gueorguiev | 10 | 2025-06-17 |
| 🚨 | [YP-122](https://universalsoftware.atlassian.net/browse/YP-122) | Carrier Master update needed for this epic - edit - adding SCAC | Ludmil Gueorguiev | 10 | 2025-06-16 |
| 🚨 | [WSSG-49](https://universalsoftware.atlassian.net/browse/WSSG-49) | Change the BOL for Seats | Andres Marcelo Garza Cantu | 10 | 2025-06-16 |
| 🚨 | [WES-24](https://universalsoftware.atlassian.net/browse/WES-24) | Migrating the outbound status from YMS to WES | Trent Meyering | 10 | 2025-06-16 |
| 🚨 | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | Amy DeRousha | 10 | 2025-06-16 |
| 🚨 | [YP-126](https://universalsoftware.atlassian.net/browse/YP-126) | Menu to add zone master | Ludmil Gueorguiev | 9 | 2025-06-18 |
| 🚨 | [YP-92](https://universalsoftware.atlassian.net/browse/YP-92) | Research how to accommodate the switcher queue in the database and generate the data structure | Ludmil Gueorguiev | 9 | 2025-06-17 |
| 🚨 | [YP-129](https://universalsoftware.atlassian.net/browse/YP-129) | Zone Master - adding to database | Ludmil Gueorguiev | 8 | 2025-06-19 |
| 🚨 | [DTCMS-92](https://universalsoftware.atlassian.net/browse/DTCMS-92) | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | Cameron Rye | 7 | 2025-06-20 |
| 🚨 | [BMWS-395](https://universalsoftware.atlassian.net/browse/BMWS-395) | Resolve Permission Issues for Android 14 in Xamarin App | Matthew Berryhill | 7 | 2025-06-19 |
| ⚠️ | [YP-136](https://universalsoftware.atlassian.net/browse/YP-136) | Zone Master - delete zones | Ludmil Gueorguiev | 4 | 2025-06-23 |
| ⚠️ | [YP-132](https://universalsoftware.atlassian.net/browse/YP-132) |  Zone Mater - view zones and sort | Ludmil Gueorguiev | 4 | 2025-06-23 |
| ⚠️ | [YP-127](https://universalsoftware.atlassian.net/browse/YP-127) | Zone Master - add zone | Ludmil Gueorguiev | 4 | 2025-06-23 |
| ⚠️ | [FLEET-100](https://universalsoftware.atlassian.net/browse/FLEET-100) | Asset detail - Side-panel Features (Reinstate) | R Sanders | 4 | 2025-06-23 |
| ⚠️ | [POR-17](https://universalsoftware.atlassian.net/browse/POR-17) | Location & Job Profile Filters for Workday Demographic API | Christian Marino Matsoukis | 3 | 2025-06-23 |
| ✅ | [BMWS-306](https://universalsoftware.atlassian.net/browse/BMWS-306) | Refactor SFG Generator Code for SFG Forecasting Page Update | Isaiah Childs | 2 | 2025-06-24 |
| ✅ | [AP-71](https://universalsoftware.atlassian.net/browse/AP-71) | DMS Document handling - Threading & Stability | Ben Blazy | 1 | 2025-06-25 |
| ✅ | [YP-138](https://universalsoftware.atlassian.net/browse/YP-138) | Zone Master - Yard Location search by zone | Ludmil Gueorguiev | 0 | 2025-06-27 |
| ✅ | [YP-134](https://universalsoftware.atlassian.net/browse/YP-134) | Zone Master - edit yard locations | Ludmil Gueorguiev | 0 | 2025-06-26 |
| ✅ | [YP-47](https://universalsoftware.atlassian.net/browse/YP-47) | Yard Check Module - Menu | Trent Meyering | 0 | 2025-06-26 |
| ✅ | [SBZ-112](https://universalsoftware.atlassian.net/browse/SBZ-112) | Dashboard Displays for TVs | Tyler Meholic | 0 | 2025-06-26 |
| ✅ | [FLEET-101](https://universalsoftware.atlassian.net/browse/FLEET-101) | Asset detail - Side-panel Features (Change Contractor) | R Sanders | 0 | 2025-06-26 |
| ✅ | [BMWS-413](https://universalsoftware.atlassian.net/browse/BMWS-413) | Configure Dashboard URLs for All Supported Commodities | Christian Marino Matsoukis | 0 | 2025-06-26 |
| ✅ | [AL-269](https://universalsoftware.atlassian.net/browse/AL-269) | Pick Mins to Stock | Alex DeLuca | 0 | 2025-06-26 |

## Summary Statistics

- **Total tickets in review:** 46
- **Average days in review:** 19.5
- **Longest in review:** 142 days
- **Tickets over 3 days:** 36
- **Tickets over 7 days:** 30

