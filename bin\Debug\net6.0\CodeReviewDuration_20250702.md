# Code Review Duration Report

## Tickets Currently in Review

| Status | Ticket | Summary | Assignee | Days in Review | Entered Review |
|--------|--------|---------|----------|----------------|----------------|
| 🚨 | [WES-106](https://universalsoftware.atlassian.net/browse/WES-106) | Migrating the outbound status (ready to depart popup) from YMS to WES - frontend | <PERSON> | 86 | 2025-04-07 |
| 🚨 | [GMFZGT-285](https://universalsoftware.atlassian.net/browse/GMFZGT-285) | Add Feature to View Completed but Not Dispatched Orders | Rakhi Garg | 75 | 2025-04-17 |
| 🚨 | [WSSG-43](https://universalsoftware.atlassian.net/browse/WSSG-43) | Cycle Count Function Seats WMS | <PERSON> | 50 | 2025-05-12 |
| 🚨 | [YP-119](https://universalsoftware.atlassian.net/browse/YP-119) | Add SCAC column to carrier | Ludmil Gueorguiev | 20 | 2025-06-11 |
| 🚨 | [WSSG-45](https://universalsoftware.atlassian.net/browse/WSSG-45) | Seats WMS receiving and storing duplicates | Igor Ivanovski | 20 | 2025-06-12 |
| 🚨 | [WSWTGT-58](https://universalsoftware.atlassian.net/browse/WSWTGT-58) | Change the BOL for WheelTire | Rakhi Garg | 18 | 2025-06-13 |
| 🚨 | [WSPPGT-40](https://universalsoftware.atlassian.net/browse/WSPPGT-40) | Change the BOL for Painted Parts  | Alex Gonzalez | 16 | 2025-06-16 |
| 🚨 | [WSSG-49](https://universalsoftware.atlassian.net/browse/WSSG-49) | Change the BOL for Seats | Andres Marcelo Garza Cantu | 15 | 2025-06-16 |
| 🚨 | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | Amy DeRousha | 15 | 2025-06-16 |
| 🚨 | [BMWS-395](https://universalsoftware.atlassian.net/browse/BMWS-395) | Resolve Permission Issues for Android 14 in Xamarin App | Matthew Berryhill | 12 | 2025-06-19 |
| ⚠️ | [SBZ-112](https://universalsoftware.atlassian.net/browse/SBZ-112) | Dashboard Displays for TVs | Tyler Meholic | 5 | 2025-06-26 |
| ⚠️ | [CTP-125](https://universalsoftware.atlassian.net/browse/CTP-125) | Database modifications for note Attachments | Ben Blazy | 4 | 2025-06-27 |
| ⚠️ | [CTP-121](https://universalsoftware.atlassian.net/browse/CTP-121) | Database modifications for Control Tower Notes | Ben Blazy | 4 | 2025-06-27 |
| ✅ | [BMWS-432](https://universalsoftware.atlassian.net/browse/BMWS-432) | Allow Multiple OffHours/Holiday Pauses on Same Day | Isaiah Childs | 1 | 2025-07-01 |
| ✅ | [BMWS-431](https://universalsoftware.atlassian.net/browse/BMWS-431) | Unpause Deleted Off-Hours/Holidays Configs to Prevent Stuck States | Isaiah Childs | 1 | 2025-07-01 |
| ✅ | [BMWS-430](https://universalsoftware.atlassian.net/browse/BMWS-430) | Update Error Message on Failed Label Prints in DCO Service | Isaiah Childs | 1 | 2025-06-30 |
| ✅ | [BMWS-405](https://universalsoftware.atlassian.net/browse/BMWS-405) | BMW - Packout Old Data Cleanup | Shayne Vallad | 1 | 2025-07-01 |
| ✅ | [RLNGTN-125](https://universalsoftware.atlassian.net/browse/RLNGTN-125) | Error Logging, set up error logging so that runtime errors can be monitored and investigated as reported.  | Alex DeLuca | 0 | 2025-07-01 |
| ✅ | [HIG-33](https://universalsoftware.atlassian.net/browse/HIG-33) | Factoring Company - to be updated in ACH queue | Trent Meyering | 0 | 2025-07-01 |
| ✅ | [BMWS-428](https://universalsoftware.atlassian.net/browse/BMWS-428) | Remove Duplicated Serials - 06/30 | Matthew Berryhill | 0 | 2025-07-01 |

## Summary Statistics

- **Total tickets in review:** 20
- **Average days in review:** 17.2
- **Longest in review:** 86 days
- **Tickets over 3 days:** 13
- **Tickets over 7 days:** 10

