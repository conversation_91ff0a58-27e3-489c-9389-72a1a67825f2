# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Overall Summary

| Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|---------|---------|------|----------|----------------|------------|--------|
| BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 67 days remaining | 0.0% | In Progress |
| BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 67 days remaining | 26.7% | In Progress |
| BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 67 days remaining | 47.1% | In Progress |
| BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 67 days remaining | 0.0% | In Progress |
| BMW Spartanburg | [BMWS-379](https://universalsoftware.atlassian.net/browse/BMWS-379) | Controls API | 2025-08-31 | 67 days remaining | N/A | Refining |
| BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 67 days remaining | 44.4% | In Progress |

## Coming Soon (Next 7 Days)

| Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|---------|---------|------|----------|----------------|----------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-25 | Due today | Cameron Rye |
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | 2 days remaining | Justin Kerketta |
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | 2 days remaining | Amy DeRousha |
| BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | 5 days remaining | Antonio Silva |

## Late Items

| Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|---------|---------|------|----------|--------------|----------|
| BMW Spartanburg | [BMWS-328](https://universalsoftware.atlassian.net/browse/BMWS-328) | Create BMW Order Fulfillment By Part SSRS Report  | 2025-06-20 | 5 days overdue | Alex Gonzalez |
| BMW Spartanburg | [BMWS-408](https://universalsoftware.atlassian.net/browse/BMWS-408) | Add Missing Columns InventoryDetailsPurge | 2025-06-24 | 1 days overdue | Justin Mosley |

## Blocked Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|---------|---------|------|----------|--------|----------------|--------------|----------|
| BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 190 days remaining | 14 days | Shayne Vallad |
| BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | Not set | Blocked/Deferred | No due date | 1 days | Cameron Rye |

## Developer Summary

### Alex Gonzalez

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-328](https://universalsoftware.atlassian.net/browse/BMWS-328) | Create BMW Order Fulfillment By Part SSRS Report  | 2025-06-20 | Review | 5 days overdue |

### Amy DeRousha

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | Review | 2 days remaining |

### Antonio Silva

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | Ready | 5 days remaining |

### Cameron Rye

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-25 | Review | Due today |

### Justin Kerketta

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | In Progress | 2 days remaining |

### Justin Mosley

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-408](https://universalsoftware.atlassian.net/browse/BMWS-408) | Add Missing Columns InventoryDetailsPurge | 2025-06-24 | Test | 1 days overdue |

### Shayne Vallad

#### In Progress Items

_No items in progress._

