using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Linq;

class Program
{
    private static IConfiguration _configuration;
    private static HttpClient _httpClient;
    private static string _jiraBaseUrl;
    private static string _jiraUser;
    private static string _jiraToken;
    private static StringBuilder _md;

    static async Task Main(string[] args)
    {
        InitializeConfiguration();
        InitializeHttpClient();
        _md = new StringBuilder();

        var timestamp = DateTime.Now.ToString("yyyyMMdd");
        _md.AppendLine("# Daily Report \n");
        await GenerateReport();

        var outputPath = Path.Combine(Directory.GetCurrentDirectory(), $"DailyReport_{timestamp}.md");
        File.WriteAllText(outputPath, _md.ToString());
        Console.WriteLine($"Markdown report generated: {outputPath}");

        // Generate project manager reports in parallel
        var projectManagerTasks = new[]
        {
            GenerateProjectManagerReport("Emily Gamble", new[] { "MobileApp", "Carta Porte", "Control Tower", "Fleet Tracker", "POR", "YP", "HIG", "AP", "CTP" }),
            GenerateProjectManagerReport("Kyle Mundie", new[] { "BMW Spartanburg", "WMS Development" },new[] { "" }, true)
        };

        await Task.WhenAll(projectManagerTasks);

        // Generate "Who's Doing What" report at the end (includes capacity information)
        await GenerateWhosDoingWhatReport();

        // Generate Code Review Duration report
        await GenerateCodeReviewDurationReport();

        Console.WriteLine("All reports generated successfully!");
    }

    private static void InitializeConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        _configuration = builder.Build();
        var jiraSettings = _configuration.GetSection("JiraSettings");
        _jiraBaseUrl = jiraSettings["ServerUrl"]?.TrimEnd('/');
        _jiraUser = jiraSettings["Username"];
        _jiraToken = jiraSettings["ApiToken"];
    }

    private static void InitializeHttpClient()
    {
        _httpClient = new HttpClient();
        var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_jiraUser}:{_jiraToken}"));
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    }

    private static async Task GenerateReport()
    {
        _md.Clear();
        var timestamp = DateTime.Now.ToString("yyyyMMdd");
        _md.AppendLine("# Daily Report\n");

        // 1. Highlights Section
        _md.AppendLine("## Highlights\n");
        _md.AppendLine("_Add any important highlights, meetings, or steps that are not obvious in other sections._\n");

        // 2. Waiting on Customer (BLOCKED)
        await GenerateWaitingOnCustomerReport();

        // 3. Late Items
        await GenerateLateItemsReport();

        // 4. Coming Soon
        await GenerateComingSoonReport();

        // 5. Blocked Items
        await GenerateBlockedItemsReport();

        // 6. Project on Hold (BLOCKED)
        await GenerateProjectOnHoldReport();

        // 7. Overall Summary
        await GenerateOverallSummary();

        // 8. Recently Completed
        await GenerateRecentlyCompletedReport();

        var outputPath = Path.Combine(Directory.GetCurrentDirectory(), $"DailyReport_{timestamp}.md");
        await File.WriteAllTextAsync(outputPath, _md.ToString());
        Console.WriteLine($"Markdown report generated: {outputPath}");
    }



    private static async Task GenerateWhosDoingWhatReport()
    {
        var md = new StringBuilder();
        md.AppendLine("# Who's Doing What Report\n");

        try
        {
            // Define excluded employees list for validation
            var excludedEmployees = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Chahak Mittal", "Connor Wood", "Chris Melton", "Noah Surprenant",
                "Riley Perez", "Robert Trupiano", "Ryan Soulard", "Leon Wilke",
                "Omar Salih", "Roman Grigoriev", "Seth Wickham", "Will Lennon",
                "Donald Young", "Michael Foster", "wayne b", "Isaiah Childs",
                "Christian Marino Matsoukis", "David Pierce", "Aaron Anderson",
                 "Jeff Priskorn", "R Sanders", "Paul Allard",
                "George Moshi"
            };

            // Get all active issues - using same pattern as other working reports
            var activeJql = "status not in (\"Removed\", \"Cancelled\", \"Canceled\", \"Done\", \"Closed\", \"Resolved\") AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC) AND assignee is not EMPTY AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
            Console.WriteLine($"Active Issues JQL: {activeJql}");
            var activeIssues = await GetIssuesFromJql(activeJql);

            // Group by assignee and determine their current activity
            var userActivities = new Dictionary<string, string>();

            foreach (var issue in activeIssues.Where(i => i.Fields.Assignee != null))
            {
                var userName = issue.Fields.Assignee.DisplayName;

                // Skip excluded employees
                if (excludedEmployees.Contains(userName))
                    continue;

                var status = issue.Fields.Status.Name;
                var issueType = issue.Fields.IssueType?.Name ?? "Unknown";

                // Only include active statuses (not Done, Closed, etc.)
                if (new[] { "Done", "Closed", "Resolved", "Removed", "Cancelled", "Canceled" }.Contains(status))
                    continue;

                string activity = DetermineActivity(status, issueType);

                // If user already has an activity, prioritize based on status hierarchy
                if (userActivities.ContainsKey(userName))
                {
                    var currentActivity = userActivities[userName];
                    if (ShouldUpdateActivity(currentActivity, activity))
                    {
                        userActivities[userName] = activity;
                    }
                }
                else
                {
                    userActivities[userName] = activity;
                }
            }

            // Also check for testers - using consistent project exclusion list
            var testingJql = "status = \"Test\" AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC) AND Tester is not EMPTY AND Tester not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
            var testingIssues = await GetIssuesFromJql(testingJql);

            foreach (var issue in testingIssues.Where(i => i.Fields.Tester != null))
            {
                var testerName = issue.Fields.Tester.DisplayName;

                // Skip excluded employees
                if (excludedEmployees.Contains(testerName))
                    continue;

                userActivities[testerName] = "Testing";
            }

            // Get ALL users who have ever been assigned to tickets (including completed ones) - remove status filter to get everyone
            var allUsersJql = "assignee is not EMPTY AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC) AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
            Console.WriteLine($"All Users JQL: {allUsersJql}");
            var allUsersIssues = await GetIssuesFromJql(allUsersJql);
            var allUsers = allUsersIssues
                .Where(i => i.Fields.Assignee != null)
                .Select(i => i.Fields.Assignee.DisplayName)
                .Where(name => !excludedEmployees.Contains(name)) // Additional validation filter
                .Distinct()
                .ToList();

            // Create a comprehensive list with everyone's status
            var allUserStatuses = new Dictionary<string, string>();

            // First, mark everyone as available
            foreach (var user in allUsers)
            {
                allUserStatuses[user] = "Available";
            }

            // Then update with actual activities for those who have active work
            // BUT treat "Code Review" as available capacity since they can take on new work
            foreach (var userActivity in userActivities)
            {
                if (userActivity.Value == "Code Review")
                {
                    allUserStatuses[userActivity.Key] = "Available";
                }
                else
                {
                    allUserStatuses[userActivity.Key] = userActivity.Value;
                }
            }

            md.AppendLine("## Everyone's Current Status\n");

            if (allUserStatuses.Count == 0)
            {
                md.AppendLine("_No users found._\n");
            }
            else
            {
                md.AppendLine("| Person | Current Activity |");
                md.AppendLine("|--------|------------------|");

                // Sort by status (Available first) then by name
                var sortedUsers = allUserStatuses
                    .OrderBy(u => u.Value == "Available" ? 0 : 1)  // Available first
                    .ThenBy(u => u.Value)  // Then by activity type
                    .ThenBy(u => u.Key);   // Then by name

                foreach (var user in sortedUsers)
                {
                    md.AppendLine($"| {user.Key} | {user.Value} |");
                }
                md.AppendLine();

                // Summary counts
                var availableCount = allUserStatuses.Count(u => u.Value == "Available");
                var busyCount = allUserStatuses.Count - availableCount;

                md.AppendLine($"**Summary:** {allUserStatuses.Count} total developers - {busyCount} working, {availableCount} available\n");

                // Separate section for available users
                var availableUsers = allUserStatuses.Where(u => u.Value == "Available").Select(u => u.Key).ToList();
                if (availableUsers.Count > 0)
                {
                    md.AppendLine("## Available Developers\n");
                    md.AppendLine("| Person |");
                    md.AppendLine("|--------|");

                    foreach (var user in availableUsers.OrderBy(u => u))
                    {
                        md.AppendLine($"| {user} |");
                    }
                    md.AppendLine();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating Who's Doing What report: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            md.AppendLine("_Error generating Who's Doing What report. Please check the logs._\n");
            md.AppendLine($"_Error details: {ex.Message}_\n");
        }

        var timestamp = DateTime.Now.ToString("yyyyMMdd");
        var outputPath = Path.Combine(Directory.GetCurrentDirectory(), $"WhosDoingWhat_{timestamp}.md");
        await File.WriteAllTextAsync(outputPath, md.ToString());
        Console.WriteLine($"Who's Doing What report generated: {outputPath}");
    }

    private static async Task GenerateCodeReviewDurationReport()
    {
        var md = new StringBuilder();
        md.AppendLine("# Code Review Duration Report\n");

        try
        {
            // Get all tickets currently in Review status
            var reviewJql = "status = \"Review\" AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
            Console.WriteLine($"Review Issues JQL: {reviewJql}");
            var reviewIssues = await GetIssuesFromJql(reviewJql);

            if (reviewIssues.Count == 0)
            {
                md.AppendLine("_No tickets currently in Review status._\n");
            }
            else
            {
                var reviewData = new List<(string Key, string Summary, string Assignee, DateTime? EnteredReview, int DaysInReview)>();

                foreach (var issue in reviewIssues)
                {
                    var assigneeName = issue.Fields.Assignee?.DisplayName ?? "Unassigned";
                    var enteredReviewDate = GetDateEnteredReview(issue);
                    var daysInReview = enteredReviewDate.HasValue ?
                        (int)(DateTime.Now - enteredReviewDate.Value).TotalDays : 0;

                    reviewData.Add((
                        issue.Key,
                        issue.Fields.Summary,
                        assigneeName,
                        enteredReviewDate,
                        daysInReview
                    ));
                }

                // Sort by days in review (longest first)
                reviewData = reviewData.OrderByDescending(r => r.DaysInReview).ToList();

                md.AppendLine("## Tickets Currently in Review\n");
                md.AppendLine("| Status | Ticket | Summary | Assignee | Days in Review | Entered Review |");
                md.AppendLine("|--------|--------|---------|----------|----------------|----------------|");

                foreach (var item in reviewData)
                {
                    var enteredReviewStr = item.EnteredReview?.ToString("yyyy-MM-dd") ?? "Unknown";
                    var summaryEscaped = EscapeMd(item.Summary);

                    // Add warning icons based on days in review
                    string statusIcon;
                    if (item.DaysInReview >= 7)
                        statusIcon = "🚨"; // Critical - 7+ days
                    else if (item.DaysInReview >= 3)
                        statusIcon = "⚠️"; // Warning - 3+ days
                    else
                        statusIcon = "✅"; // OK - under 3 days

                    var ticketLink = $"[{item.Key}]({_jiraBaseUrl}/browse/{item.Key})";
                    md.AppendLine($"| {statusIcon} | {ticketLink} | {summaryEscaped} | {item.Assignee} | {item.DaysInReview} | {enteredReviewStr} |");
                }

                md.AppendLine();

                // Summary statistics
                var avgDays = reviewData.Average(r => r.DaysInReview);
                var maxDays = reviewData.Max(r => r.DaysInReview);
                var ticketsOver3Days = reviewData.Count(r => r.DaysInReview > 3);
                var ticketsOver7Days = reviewData.Count(r => r.DaysInReview > 7);

                md.AppendLine("## Summary Statistics\n");
                md.AppendLine($"- **Total tickets in review:** {reviewData.Count}");
                md.AppendLine($"- **Average days in review:** {avgDays:F1}");
                md.AppendLine($"- **Longest in review:** {maxDays} days");
                md.AppendLine($"- **Tickets over 3 days:** {ticketsOver3Days}");
                md.AppendLine($"- **Tickets over 7 days:** {ticketsOver7Days}");
                md.AppendLine();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating Code Review Duration report: {ex.Message}");
            md.AppendLine("_Error generating Code Review Duration report. Please check the logs._\n");
        }

        var timestamp = DateTime.Now.ToString("yyyyMMdd");
        var outputPath = Path.Combine(Directory.GetCurrentDirectory(), $"CodeReviewDuration_{timestamp}.md");
        await File.WriteAllTextAsync(outputPath, md.ToString());
        Console.WriteLine($"Code Review Duration report generated: {outputPath}");
    }

    private static async Task GenerateWaitingOnCustomerReport()
    {
        _md.AppendLine("## Waiting on Customer (BLOCKED)\n");
        var jql = "status = \"Waiting on Customer (BLOCKED)\" AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        var issues = await GetIssuesFromJql(jql);
        if (issues.Count == 0)
        {
            _md.AppendLine("_No items waiting on customer._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |");
        _md.AppendLine("|--------|---------|---------|------|----------|--------|----------------|--------------|----------|");
        foreach (var issue in issues.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
        {
            var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
            var daysBlocked = CalculateDaysBlocked(issue);
            var statusIcon = GetStatusIcon(issue, "blocked");

            _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} | {daysBlocked} days | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
        }
        _md.AppendLine();
    }

    private static async Task GenerateProjectOnHoldReport()
    {
        _md.AppendLine("## Project on Hold (BLOCKED)\n");
        var jql = "status = \"Project on Hold (BLOCKED)\" AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        var issues = await GetIssuesFromJql(jql);
        if (issues.Count == 0)
        {
            _md.AppendLine("_No projects on hold._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |");
        _md.AppendLine("|--------|---------|---------|------|----------|--------|----------------|--------------|----------|");
        foreach (var issue in issues.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
        {
            var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
            var daysBlocked = CalculateDaysBlocked(issue);
            var statusIcon = GetStatusIcon(issue, "blocked");

            _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} | {daysBlocked} days | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
        }
        _md.AppendLine();
    }

    private static async Task GenerateRecentlyCompletedReport()
    {
        _md.AppendLine("## Recently Completed (Last 5 Items)\n");
        var jql = "status in (Done, Closed, Resolved) AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC) ORDER BY resolved DESC";
        var issues = await GetIssuesFromJql(jql);

        // Take only the most recent 5 completed items
        var recentCompleted = issues.Take(5).ToList();

        if (recentCompleted.Count == 0)
        {
            _md.AppendLine("_No recently completed items._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Completed Date | Assignee |");
        _md.AppendLine("|--------|---------|---------|------|----------------|----------|");
        foreach (var issue in recentCompleted)
        {
            var completedDate = issue.Fields.ResolutionDate?.ToString("yyyy-MM-dd") ?? "Unknown";
            var statusIcon = "✅"; // Completed items get green checkmark

            _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {completedDate} | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
        }
        _md.AppendLine();
    }

    private static DateTime? GetDateEnteredReview(JiraIssue issue)
    {
        try
        {
            // Look through the changelog to find when the issue entered Review status
            if (issue.Changelog?.Histories != null)
            {
                foreach (var history in issue.Changelog.Histories.OrderByDescending(h => h.Created))
                {
                    var statusChange = history.Items?.FirstOrDefault(item =>
                        item.Field?.ToLower() == "status" &&
                        item.ToString?.ToLower() == "review");

                    if (statusChange != null && history.Created.HasValue)
                    {
                        return history.Created.Value;
                    }
                }
            }

            // Fallback: if no changelog data, assume it entered review recently
            return DateTime.Now.AddDays(-1);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting review date for {issue.Key}: {ex.Message}");
            return null;
        }
    }

    private static string DetermineActivity(string status, string issueType)
    {
        var statusLower = status.ToLower();
        var issueTypeLower = issueType?.ToLower() ?? "";

        // Check for common status patterns
        if (statusLower.Contains("progress"))
            return "Developing";
        if (statusLower.Contains("review"))
            return "Code Review";
        if (statusLower.Contains("test"))
            return "Testing";
        if (statusLower.Contains("refinement") || statusLower.Contains("refining"))
        {
            if (issueTypeLower.Contains("epic"))
                return "Writing Requirements";
            else
                return "Refining Requirements";
        }
        if (statusLower.Contains("to do") || statusLower.Contains("todo") || statusLower.Contains("open"))
            return "Planning";
        if (statusLower.Contains("blocked") || statusLower.Contains("deferred"))
            return "Blocked";

        // Default case
        return $"Working on {status}";
    }

    private static bool ShouldUpdateActivity(string currentActivity, string newActivity)
    {
        // Priority order: Developing > Code Review > Testing > Writing Requirements > Planning
        var priorities = new Dictionary<string, int>
        {
            { "Developing", 5 },
            { "Code Review", 4 },
            { "Testing", 3 },
            { "Writing Requirements", 2 },
            { "Refining Requirements", 2 },
            { "Planning", 1 }
        };

        var currentPriority = priorities.GetValueOrDefault(currentActivity, 0);
        var newPriority = priorities.GetValueOrDefault(newActivity, 0);

        return newPriority > currentPriority;
    }

    private static async Task<List<JiraIssue>> GetIssuesFromJql(string jql)
    {
        var maxResults = 100;

        // First request to get total count
        var firstUrl = $"{_jiraBaseUrl}/rest/api/2/search?jql={Uri.EscapeDataString(jql)}&startAt=0&maxResults={maxResults}&expand=changelog";
        var firstResponse = await _httpClient.GetAsync(firstUrl);
        firstResponse.EnsureSuccessStatusCode();
        var firstJson = await firstResponse.Content.ReadAsStringAsync();
        var firstResult = JsonSerializer.Deserialize<JiraSearchResult>(firstJson, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        if (firstResult?.Issues == null || firstResult.Total == 0)
        {
            return new List<JiraIssue>();
        }

        var totalIssues = firstResult.Total;
        Console.WriteLine($"Total issues to retrieve: {totalIssues}");

        // If we got everything in the first request, return it
        if (totalIssues <= maxResults)
        {
            return firstResult.Issues;
        }

        // Create parallel tasks for remaining batches
        var tasks = new List<Task<List<JiraIssue>>>();

        // Add the first batch result as a completed task
        tasks.Add(Task.FromResult(firstResult.Issues));

        // Create tasks for remaining batches
        for (int startAt = maxResults; startAt < totalIssues; startAt += maxResults)
        {
            var batchStartAt = startAt; // Capture for closure
            tasks.Add(FetchBatch(jql, batchStartAt, maxResults));
        }

        Console.WriteLine($"Fetching {tasks.Count} batches in parallel...");

        // Wait for all batches to complete
        var batchResults = await Task.WhenAll(tasks);

        // Use HashSet to consolidate and avoid duplicates
        var issueSet = new HashSet<JiraIssue>(new JiraIssueComparer());
        foreach (var batch in batchResults)
        {
            foreach (var issue in batch)
            {
                issueSet.Add(issue);
            }
        }

        Console.WriteLine($"Total unique issues retrieved: {issueSet.Count}");
        return issueSet.ToList();
    }

    private static async Task<List<JiraIssue>> FetchBatch(string jql, int startAt, int maxResults)
    {
        try
        {
            var url = $"{_jiraBaseUrl}/rest/api/2/search?jql={Uri.EscapeDataString(jql)}&startAt={startAt}&maxResults={maxResults}&expand=changelog";
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JiraSearchResult>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            Console.WriteLine($"Batch {startAt}-{startAt + maxResults - 1}: Retrieved {result?.Issues?.Count ?? 0} issues");
            return result?.Issues ?? new List<JiraIssue>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error fetching batch {startAt}: {ex.Message}");
            return new List<JiraIssue>();
        }
    }

    private static async Task GenerateBlockedItemsReport()
    {
        _md.AppendLine("## Blocked Items\n");
        var jql = "status = \"Blocked/Deferred\" AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        var issues = await GetIssuesFromJql(jql);
        if (issues.Count == 0)
        {
            _md.AppendLine("_No blocked items._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |");
        _md.AppendLine("|--------|---------|---------|------|----------|--------|----------------|--------------|----------|");
        foreach (var issue in issues.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
        {
            var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
            var daysBlocked = CalculateDaysBlocked(issue);
            var statusIcon = GetStatusIcon(issue, "blocked");

            _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} | {daysBlocked} days | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
        }
        _md.AppendLine();
    }

    private static async Task GenerateLateItemsReport()
    {
        _md.AppendLine("## Late Items\n");
        var jql = "duedate < now() AND status not in (Done, Removed, Closed, \"Blocked/Deferred\", \"Waiting on Customer (BLOCKED)\", \"Project on Hold (BLOCKED)\") AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        var issues = await GetIssuesFromJql(jql);
        if (issues.Count == 0)
        {
            _md.AppendLine("_No late items._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Overdue | Assignee |");
        _md.AppendLine("|--------|---------|---------|------|----------|--------------|----------|");
        foreach (var issue in issues.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
        {
            var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
            var statusIcon = GetStatusIcon(issue, "late");

            _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
        }
        _md.AppendLine();
    }

    private static async Task GenerateComingSoonReport()
    {
        _md.AppendLine("## Coming Soon (Next 7 Days)\n");
        var nextWeek = DateTime.UtcNow.AddDays(7).ToString("yyyy-MM-dd");
        var jql = $"duedate >= now() AND duedate <= {nextWeek} AND status not in (Done, Closed, \"Blocked/Deferred\", \"Waiting on Customer (BLOCKED)\", \"Project on Hold (BLOCKED)\") AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        var issues = await GetIssuesFromJql(jql);
        if (issues.Count == 0)
        {
            _md.AppendLine("_No items due in the next week._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Remaining | Assignee |");
        _md.AppendLine("|--------|---------|---------|------|----------|----------------|----------|");
        foreach (var issue in issues.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
        {
            var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
            var statusIcon = GetStatusIcon(issue, "comingsoon");

            _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
        }
        _md.AppendLine();
    }

    private static async Task GenerateOverallSummary()
    {
        _md.AppendLine("## Overall Summary\n");
        var jql = "issuetype = Epic AND status not in (\"Removed\", \"Done\", \"Closed\", \"Resolved\") AND project not in (ATLAS, \"Transportation Systems\",\"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        Console.WriteLine($"Overall Summary JQL: {jql}");
        var epics = await GetIssuesFromJql(jql);
        
        // Additional filtering to ensure no Done/Closed/Resolved items
        epics = epics.Where(e => !new[] { "Done", "Closed", "Resolved" }.Contains(e.Fields.Status.Name)).ToList();
        
        if (epics.Count == 0)
        {
            _md.AppendLine("_No epics found._\n");
            return;
        }

        _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |");
        _md.AppendLine("|--------|---------|---------|------|----------|----------------|------------|--------|");
        foreach (var epic in epics.OrderBy(e => e.Fields.DueDate ?? DateTime.MaxValue))
        {
            var children = await GetIssuesFromJql($"\"Epic Link\" = {epic.Key}");
            var completion = children.Count > 0
                ? $"{(double)children.Count(i => i.Fields.Status.Name == "Done") / children.Count * 100:F1}%"
                : "N/A";
            var daysRemaining = CalculateDaysRemaining(epic.Fields.DueDate);

            var completionPercent = children.Count > 0
                ? (double)children.Count(i => i.Fields.Status.Name == "Done") / children.Count * 100
                : 0;
            var statusIcon = GetEpicStatusIcon(epic, completionPercent);

            _md.AppendLine($"| {statusIcon} | {epic.Fields.Project.Name} | [{epic.Key}]({_jiraBaseUrl}/browse/{epic.Key}) | {EscapeMd(epic.Fields.Summary)} | {epic.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} | {completion} | {epic.Fields.Status.Name} |");
        }
        _md.AppendLine();
    }

    private static async Task GenerateDeveloperSummary()
    {
        _md.AppendLine("## Developer Summary\n");
        // Gather all issues for the developer summary
        var jql = "status not in (\"Removed\") AND project not in (ATLAS, \"ATLAS Rollout\", \"ATLAS Terminal Launch\", \"Business Integration\", IntermodalCombinedQlikDashboard, \"International Paper WMS\", \"GHG/ESG Qlik App\", BI-SSRS_Work, \"Transportation Systems\", NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        var allIssues = await GetIssuesFromJql(jql);

        // Define excluded employees list for filtering
        var excludedEmployees = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Chahak Mittal", "Connor Wood", "Chris Melton", "Noah Surprenant",
            "Riley Perez", "Robert Trupiano", "Ryan Soulard", "Leon Wilke",
            "Omar Salih", "Roman Grigoriev", "Seth Wickham", "Will Lennon",
            "Donald Young", "Michael Foster", "wayne b", "Isaiah Childs",
            "Christian Marino Matsoukis", "David Pierce", "Aaron Anderson",
            "Igor Ivanovski", "Jeff Priskorn", "R Sanders", "Paul Allard",
            "George Moshi"
        };

        var developerIssues = allIssues
            .Where(i => i.Fields.Assignee != null && !excludedEmployees.Contains(i.Fields.Assignee.DisplayName))
            .GroupBy(i => i.Fields.Assignee.DisplayName);
        if (!developerIssues.Any())
        {
            _md.AppendLine("_No developer assignments found._\n");
        }
        else
        {
            foreach (var developerGroup in developerIssues.OrderBy(g => g.Key))
            {
                _md.AppendLine($"### {developerGroup.Key}\n");
                
                // In Progress Items
                var inProgressItems = developerGroup.Where(i => i.Fields.Status.Name != "Done" && i.Fields.Status.Name != "Blocked/Deferred" && i.Fields.Status.Name != "To Do").ToList();
                _md.AppendLine("#### In Progress Items\n");
                if (inProgressItems.Any())
                {
                    _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining |");
                    _md.AppendLine("|--------|---------|---------|------|----------|--------|----------------|");
                    foreach (var issue in inProgressItems.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
                    {
                        var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                        var statusIcon = GetStatusIcon(issue, "progress");

                        _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} |");
                    }
                }
                else
                {
                    _md.AppendLine("_No items in progress._");
                }
                _md.AppendLine();
                
                // To Do Items (Assigned but not started)
                var toDoItems = developerGroup.Where(i => i.Fields.Status.Name == "To Do").ToList();
                if (toDoItems.Any())
                {
                    _md.AppendLine("#### To Do Items (Assigned but not started)\n");
                    _md.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Remaining |");
                    _md.AppendLine("|--------|---------|---------|------|----------|----------------|");
                    foreach (var issue in toDoItems.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
                    {
                        var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                        var statusIcon = GetStatusIcon(issue, "comingsoon"); // Use coming soon logic for To Do items

                        _md.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} |");
                    }
                    _md.AppendLine();
                }
                
                // Completed Items
                var completedItems = developerGroup.Where(i => i.Fields.Status.Name == "Done").ToList();
                if (completedItems.Any())
                {
                    _md.AppendLine("#### Completed Items\n");
                    _md.AppendLine("| Project | Jira ID | Name | Due Date |");
                    _md.AppendLine("|---------|---------|------|----------|");
                    foreach (var issue in completedItems.OrderByDescending(i => i.Fields.DueDate ?? DateTime.MinValue))
                    {
                        _md.AppendLine($"| {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} |");
                    }
                    _md.AppendLine();
                }
            }
        }
    }



    private static async Task<string> GetHierarchyMarkdown(JiraIssue issue)
    {
        // Recursively build parent chain
        if (issue.Fields.Parent != null)
        {
            var parent = await GetIssue(issue.Fields.Parent.Key);
            var parentHierarchy = await GetHierarchyMarkdown(parent);
            return $"- **Parent:** {parent.Key} - {EscapeMd(parent.Fields.Summary)}\n" + parentHierarchy;
        }
        return string.Empty;
    }

    private static async Task<JiraIssue> GetIssue(string key)
    {
        var url = $"{_jiraBaseUrl}/rest/api/2/issue/{key}?expand=changelog";
        var response = await _httpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<JiraIssue>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
    }

    private static string EscapeMd(string input)
    {
        return input?.Replace("|", "\\|") ?? string.Empty;
    }

    private static string GetStatusIcon(JiraIssue issue, string context = "default")
    {
        switch (context.ToLower())
        {
            case "late":
                if (issue.Fields.DueDate.HasValue)
                {
                    var daysOverdue = (DateTime.Now - issue.Fields.DueDate.Value).TotalDays;
                    if (daysOverdue >= 14) return "🚨"; // Critical - 2+ weeks overdue
                    if (daysOverdue >= 7) return "⚠️";  // Warning - 1+ week overdue
                    if (daysOverdue >= 1) return "🟡";  // Caution - overdue
                    return "🔴"; // Just overdue
                }
                return "❓"; // No due date

            case "comingsoon":
                if (issue.Fields.DueDate.HasValue)
                {
                    var daysUntilDue = (issue.Fields.DueDate.Value - DateTime.Now).TotalDays;
                    if (daysUntilDue <= 1) return "🚨"; // Due today/tomorrow
                    if (daysUntilDue <= 2) return "⚠️"; // Due very soon
                    if (daysUntilDue <= 4) return "🟡"; // Due this week
                    return "✅"; // Due next week
                }
                return "❓"; // No due date

            case "blocked":
                var daysBlocked = CalculateDaysBlocked(issue);
                if (daysBlocked >= 14) return "🚨"; // Critical - blocked 2+ weeks
                if (daysBlocked >= 7) return "⚠️";  // Warning - blocked 1+ week
                if (daysBlocked >= 3) return "🟡";  // Caution - blocked several days
                return "🔴"; // Recently blocked

            case "progress":
                if (issue.Fields.DueDate.HasValue)
                {
                    var daysUntilDue = (issue.Fields.DueDate.Value - DateTime.Now).TotalDays;
                    if (daysUntilDue < 0) return "🚨"; // Overdue
                    if (daysUntilDue <= 1) return "⚠️"; // Due soon
                    if (daysUntilDue <= 3) return "🟡"; // Due this week
                    return "✅"; // On track
                }
                return "📋"; // No due date

            default:
                return "📋"; // Default icon
        }
    }

    private static string GetEpicStatusIcon(JiraIssue epic, double completionPercent)
    {
        if (epic.Fields.DueDate.HasValue)
        {
            var daysUntilDue = (epic.Fields.DueDate.Value - DateTime.Now).TotalDays;
            if (daysUntilDue < 0 && completionPercent < 100) return "🚨"; // Overdue and not complete
            if (daysUntilDue <= 7 && completionPercent < 80) return "⚠️"; // Due soon and behind
        }

        if (completionPercent >= 100) return "✅"; // Complete
        if (completionPercent >= 75) return "🟡";  // Good progress
        return "📋"; // In progress
    }

    private static string CalculateDaysRemaining(DateTime? dueDate)
    {
        if (!dueDate.HasValue)
            return "No due date";
        
        var today = DateTime.Today;
        var daysDiff = (dueDate.Value.Date - today).Days;
        
        if (daysDiff < 0)
            return $"{Math.Abs(daysDiff)} days overdue";
        else if (daysDiff == 0)
            return "Due today";
        else
            return $"{daysDiff} days remaining";
    }

    private static int CalculateDaysBlocked(JiraIssue issue)
    {
        if (issue.Changelog?.Histories == null)
            return 0;

        // Find the most recent status change to "Blocked/Deferred"
        var blockedHistory = issue.Changelog.Histories
            .Where(h => h.Items?.Any(item => 
                item.Field == "status" && 
                item.ToString == "Blocked/Deferred") == true)
            .OrderByDescending(h => h.Created)
            .FirstOrDefault();

        if (blockedHistory?.Created == null)
            return 0;

        var today = DateTime.Today;
        var daysBlocked = (today - blockedHistory.Created.Value.Date).Days;
        return Math.Max(0, daysBlocked);
    }

    private static async Task GenerateProjectManagerReport(string projectManagerName, string[] includedProjects, string[] excludedProjects = null, bool isCategory = false)
    {
        var reportBuilder = new StringBuilder();
        var timestamp = DateTime.Now.ToString("yyyyMMdd");
        reportBuilder.AppendLine($"# Daily Report\n");

        // 1. Highlights Section
        reportBuilder.AppendLine("## Highlights\n");
        reportBuilder.AppendLine("_Add any important highlights, meetings, or steps that are not obvious in other sections._\n");

        // 2. Waiting on Customer (BLOCKED)
        var waitingOnCustomerJql = "status = \"Waiting on Customer (BLOCKED)\"";
        if (isCategory)
            waitingOnCustomerJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            waitingOnCustomerJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                waitingOnCustomerJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        waitingOnCustomerJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        waitingOnCustomerJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        var waitingOnCustomerIssues = await GetIssuesFromJql(waitingOnCustomerJql);

        reportBuilder.AppendLine("## Waiting on Customer (BLOCKED)\n");
        var waitingOnCustomerFiltered = waitingOnCustomerIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).ToList();
        if (waitingOnCustomerFiltered.Count == 0)
        {
            reportBuilder.AppendLine("_No items waiting on customer._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------|--------|----------------|--------------|----------|");
            foreach (var issue in waitingOnCustomerFiltered.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
            {
                var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                var daysBlocked = CalculateDaysBlocked(issue);
                var statusIcon = GetStatusIcon(issue, "blocked");

                reportBuilder.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} | {daysBlocked} days | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
            }
            reportBuilder.AppendLine();
        }

        // Get all issues for all sections
        var allIssues = new List<JiraIssue>();
        allIssues.AddRange(waitingOnCustomerIssues);

        // Blocked Items
        var blockedJql = "status = \"Blocked/Deferred\"";
        if (isCategory)
            blockedJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            blockedJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                blockedJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        blockedJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        blockedJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        var blockedIssues = await GetIssuesFromJql(blockedJql);
        allIssues.AddRange(blockedIssues);

        // Late Items
        var lateJql = "duedate < now() AND status not in (\"Done\", \"Closed\", \"Blocked/Deferred\", \"Waiting on Customer (BLOCKED)\", \"Project on Hold (BLOCKED)\", \"Removed\")";
        if (isCategory)
            lateJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            lateJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                lateJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        lateJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        lateJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        var lateIssues = await GetIssuesFromJql(lateJql);
        allIssues.AddRange(lateIssues);

        // Coming Soon
        var nextWeek = DateTime.Now.AddDays(7).ToString("yyyy-MM-dd");
        var comingSoonJql = $"duedate >= now() AND duedate <= \"{nextWeek}\" AND status not in (\"Done\", \"Closed\", \"Blocked/Deferred\", \"Waiting on Customer (BLOCKED)\", \"Project on Hold (BLOCKED)\", \"Removed\")";
        if (isCategory)
            comingSoonJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            comingSoonJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                comingSoonJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        comingSoonJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        comingSoonJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        var comingSoonIssues = await GetIssuesFromJql(comingSoonJql);
        allIssues.AddRange(comingSoonIssues);

        // Filter out excluded projects from the issues list
        if (excludedProjects != null && excludedProjects.Length > 0)
        {
            allIssues = allIssues.Where(i => !excludedProjects.Contains(i.Fields.Project.Name)).ToList();
        }
        // Additional filter to ensure NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC are excluded
        allIssues = allIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).ToList();

        // 3. Blocked Items
        reportBuilder.AppendLine("## Blocked Items\n");
        var blockedFiltered = blockedIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).ToList();
        if (blockedFiltered.Count == 0)
        {
            reportBuilder.AppendLine("_No blocked items._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------|--------|----------------|--------------|----------|");
            foreach (var issue in blockedFiltered.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
            {
                var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                var daysBlocked = CalculateDaysBlocked(issue);
                var statusIcon = GetStatusIcon(issue, "blocked");

                reportBuilder.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} | {daysBlocked} days | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
            }
            reportBuilder.AppendLine();
        }

        // 4. Late Items
        reportBuilder.AppendLine("## Late Items\n");
        var lateFiltered = lateIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).ToList();
        if (lateFiltered.Count == 0)
        {
            reportBuilder.AppendLine("_No late items._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Overdue | Assignee |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------|--------------|----------|");
            foreach (var issue in lateFiltered.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
            {
                var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                var statusIcon = GetStatusIcon(issue, "late");

                reportBuilder.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
            }
            reportBuilder.AppendLine();
        }

        // 5. Coming Soon
        reportBuilder.AppendLine("## Coming Soon (Next 7 Days)\n");
        var comingSoonFiltered = comingSoonIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).ToList();
        if (comingSoonFiltered.Count == 0)
        {
            reportBuilder.AppendLine("_No items due in the next week._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Remaining | Assignee |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------|----------------|----------|");
            foreach (var issue in comingSoonFiltered.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
            {
                var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                var statusIcon = GetStatusIcon(issue, "comingsoon");

                reportBuilder.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
            }
            reportBuilder.AppendLine();
        }

        // 6. Project on Hold (BLOCKED)
        var projectOnHoldJql = "status = \"Project on Hold (BLOCKED)\"";
        if (isCategory)
            projectOnHoldJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            projectOnHoldJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                projectOnHoldJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        projectOnHoldJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        projectOnHoldJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        var projectOnHoldIssues = await GetIssuesFromJql(projectOnHoldJql);

        reportBuilder.AppendLine("## Project on Hold (BLOCKED)\n");
        var projectOnHoldFiltered = projectOnHoldIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).ToList();
        if (projectOnHoldFiltered.Count == 0)
        {
            reportBuilder.AppendLine("_No projects on hold._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------|--------|----------------|--------------|----------|");
            foreach (var issue in projectOnHoldFiltered.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
            {
                var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                var daysBlocked = CalculateDaysBlocked(issue);
                var statusIcon = GetStatusIcon(issue, "blocked");

                reportBuilder.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} | {daysBlocked} days | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
            }
            reportBuilder.AppendLine();
        }

        // 7. Overall Summary
        reportBuilder.AppendLine("## Overall Summary\n");
        var summaryJql = "issuetype = Epic AND status not in (\"Removed\", \"Done\", \"Closed\", \"Resolved\")";
        if (isCategory)
            summaryJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            summaryJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                summaryJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        summaryJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        summaryJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        var epics = await GetIssuesFromJql(summaryJql);
        epics = epics.Where(e => !new[] { "Done", "Closed", "Resolved" }.Contains(e.Fields.Status.Name)).ToList();
        if (epics.Count == 0)
        {
            reportBuilder.AppendLine("_No epics found._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------|----------------|------------|--------|");
            foreach (var epic in epics.OrderBy(e => e.Fields.DueDate ?? DateTime.MaxValue))
            {
                var children = await GetIssuesFromJql($"\"Epic Link\" = {epic.Key}");
                var completion = children.Count > 0
                    ? $"{(double)children.Count(i => i.Fields.Status.Name == "Done") / children.Count * 100:F1}%"
                    : "N/A";
                var daysRemaining = CalculateDaysRemaining(epic.Fields.DueDate);

                var completionPercent = children.Count > 0
                    ? (double)children.Count(i => i.Fields.Status.Name == "Done") / children.Count * 100
                    : 0;
                var statusIcon = GetEpicStatusIcon(epic, completionPercent);

                reportBuilder.AppendLine($"| {statusIcon} | {epic.Fields.Project.Name} | [{epic.Key}]({_jiraBaseUrl}/browse/{epic.Key}) | {EscapeMd(epic.Fields.Summary)} | {epic.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} | {completion} | {epic.Fields.Status.Name} |");
            }
            reportBuilder.AppendLine();
        }

        // 8. Developer Summary
        reportBuilder.AppendLine("## Developer Summary\n");

        // Define excluded employees list for filtering
        var excludedEmployees = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Chahak Mittal", "Connor Wood", "Chris Melton", "Noah Surprenant",
            "Riley Perez", "Robert Trupiano", "Ryan Soulard", "Leon Wilke",
            "Omar Salih", "Roman Grigoriev", "Seth Wickham", "Will Lennon",
            "Donald Young", "Michael Foster", "wayne b", "Isaiah Childs",
            "Christian Marino Matsoukis", "David Pierce", "Aaron Anderson",
            "Igor Ivanovski", "Jeff Priskorn", "R Sanders", "Paul Allard",
            "George Moshi"
        };

        var developerIssues = allIssues
            .Where(i => i.Fields.Assignee != null && !excludedEmployees.Contains(i.Fields.Assignee.DisplayName))
            .GroupBy(i => i.Fields.Assignee.DisplayName);
        if (!developerIssues.Any())
        {
            reportBuilder.AppendLine("_No developer assignments found._\n");
        }
        else
        {
            foreach (var developerGroup in developerIssues.OrderBy(g => g.Key))
            {
                reportBuilder.AppendLine($"### {developerGroup.Key}\n");
                
                // In Progress Items
                var inProgressItems = developerGroup.Where(i => i.Fields.Status.Name != "Done" && i.Fields.Status.Name != "Blocked/Deferred" && i.Fields.Status.Name != "To Do").ToList();
                reportBuilder.AppendLine("#### In Progress Items\n");
                if (inProgressItems.Any())
                {
                    reportBuilder.AppendLine("| Project | Jira ID | Name | Due Date | Status | Days Remaining |");
                    reportBuilder.AppendLine("|---------|---------|------|----------|--------|----------------|");
                    foreach (var issue in inProgressItems.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
                    {
                        var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                        reportBuilder.AppendLine($"| {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {issue.Fields.Status.Name} | {daysRemaining} |");
                    }
                }
                else
                {
                    reportBuilder.AppendLine("_No items in progress._");
                }
                reportBuilder.AppendLine();
                
                // To Do Items (Assigned but not started)
                var toDoItems = developerGroup.Where(i => i.Fields.Status.Name == "To Do").ToList();
                if (toDoItems.Any())
                {
                    reportBuilder.AppendLine("#### To Do Items (Assigned but not started)\n");
                    reportBuilder.AppendLine("| Project | Jira ID | Name | Due Date | Days Remaining |");
                    reportBuilder.AppendLine("|---------|---------|------|----------|----------------|");
                    foreach (var issue in toDoItems.OrderBy(i => i.Fields.DueDate ?? DateTime.MaxValue))
                    {
                        var daysRemaining = CalculateDaysRemaining(issue.Fields.DueDate);
                        reportBuilder.AppendLine($"| {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} | {daysRemaining} |");
                    }
                    reportBuilder.AppendLine();
                }
                
                // Completed Items
                var completedItems = developerGroup.Where(i => i.Fields.Status.Name == "Done").ToList();
                if (completedItems.Any())
                {
                    reportBuilder.AppendLine("#### Completed Items\n");
                    reportBuilder.AppendLine("| Project | Jira ID | Name | Due Date |");
                    reportBuilder.AppendLine("|---------|---------|------|----------|");
                    foreach (var issue in completedItems.OrderByDescending(i => i.Fields.DueDate ?? DateTime.MinValue))
                    {
                        reportBuilder.AppendLine($"| {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {issue.Fields.DueDate?.ToString("yyyy-MM-dd") ?? "Not set"} |");
                    }
                    reportBuilder.AppendLine();
                }
            }
        }

        // 9. Recently Completed (Last 5 Items)
        var recentlyCompletedJql = "status in (Done, Closed, Resolved)";
        if (isCategory)
            recentlyCompletedJql += " AND category = \"WMS Development\"";
        else
        {
            var projectList = string.Join("\", \"", includedProjects.Select(p => p.Replace("\"", "\\\"")));
            recentlyCompletedJql += $" AND project in (\"{projectList}\")";
            if (excludedProjects != null && excludedProjects.Length > 0)
            {
                var excludedList = string.Join("\", \"", excludedProjects.Select(p => p.Replace("\"", "\\\"")));
                recentlyCompletedJql += $" AND project not in (\"{excludedList}\")";
            }
        }
        recentlyCompletedJql += " AND project not in (NI, GQA, NT, BI, BSW, AT, AR, pmp, ATL, GC)";
        recentlyCompletedJql += " AND assignee not in (\"Chahak Mittal\", \"Connor Wood\", \"Chris Melton\", \"Noah Surprenant\", \"Riley Perez\", \"Robert Trupiano\", \"Ryan Soulard\", \"Leon Wilke\", \"Omar Salih\", \"Roman Grigoriev\", \"Seth Wickham\", \"Will Lennon\", \"Donald Young\", \"Michael Foster\", \"wayne b\", \"Isaiah Childs\", \"Christian Marino Matsoukis\", \"David Pierce\", \"Aaron Anderson\", \"Igor Ivanovski\", \"Jeff Priskorn\", \"R Sanders\", \"Paul Allard\", \"George Moshi\")";
        recentlyCompletedJql += " ORDER BY resolved DESC";
        var recentlyCompletedIssues = await GetIssuesFromJql(recentlyCompletedJql);

        reportBuilder.AppendLine("## Recently Completed (Last 5 Items)\n");
        var recentlyCompletedFiltered = recentlyCompletedIssues.Where(i => !new[] { "NI", "GQA", "NT", "BI", "BSW", "AT", "AR", "pmp", "ATL", "GC" }.Contains(i.Fields.Project.Name)).Take(5).ToList();
        if (recentlyCompletedFiltered.Count == 0)
        {
            reportBuilder.AppendLine("_No recently completed items._\n");
        }
        else
        {
            reportBuilder.AppendLine("| Status | Project | Jira ID | Name | Completed Date | Assignee |");
            reportBuilder.AppendLine("|--------|---------|---------|------|----------------|----------|");
            foreach (var issue in recentlyCompletedFiltered)
            {
                var completedDate = issue.Fields.ResolutionDate?.ToString("yyyy-MM-dd") ?? "Unknown";
                var statusIcon = "✅"; // Completed items get green checkmark

                reportBuilder.AppendLine($"| {statusIcon} | {issue.Fields.Project.Name} | [{issue.Key}]({_jiraBaseUrl}/browse/{issue.Key}) | {EscapeMd(issue.Fields.Summary)} | {completedDate} | {issue.Fields.Assignee?.DisplayName ?? "Unassigned"} |");
            }
            reportBuilder.AppendLine();
        }

        // Write the report to a file
        var fileName = $"DailyReport_{projectManagerName.Replace(" ", "_")}_{timestamp}.md";
        await File.WriteAllTextAsync(fileName, reportBuilder.ToString());
        Console.WriteLine($"Report for {projectManagerName} written to {fileName}");
    }
}

public class JiraSearchResult
{
    [JsonPropertyName("issues")]
    public List<JiraIssue> Issues { get; set; }
    [JsonPropertyName("total")]
    public int Total { get; set; }
}

public class JiraIssue
{
    [JsonPropertyName("key")]
    public string Key { get; set; }
    [JsonPropertyName("fields")]
    public JiraFields Fields { get; set; }
    [JsonPropertyName("changelog")]
    public JiraChangelog Changelog { get; set; }
}

public class JiraFields
{
    [JsonPropertyName("summary")]
    public string Summary { get; set; }
    [JsonPropertyName("duedate")]
    public string DueDateRaw { get; set; }
    [JsonPropertyName("created")]
    public string CreatedRaw { get; set; }
    [JsonPropertyName("resolutiondate")]
    public string ResolutionDateRaw { get; set; }
    [JsonPropertyName("assignee")]
    public JiraUser Assignee { get; set; }
    [JsonPropertyName("status")]
    public JiraStatus Status { get; set; }
    [JsonPropertyName("parent")]
    public JiraParent Parent { get; set; }
    [JsonPropertyName("project")]
    public JiraProject Project { get; set; }
    [JsonPropertyName("customfield_10366")]  // Tester custom field
    public JiraUser Tester { get; set; }
    [JsonPropertyName("issuetype")]
    public JiraIssueType IssueType { get; set; }

    [JsonIgnore]
    public DateTime? DueDate => DateTime.TryParse(DueDateRaw, out var dt) ? dt : (DateTime?)null;
    [JsonIgnore]
    public DateTime? Created => DateTime.TryParse(CreatedRaw, out var dt) ? dt : (DateTime?)null;
    [JsonIgnore]
    public DateTime? ResolutionDate => DateTime.TryParse(ResolutionDateRaw, out var dt) ? dt : (DateTime?)null;
}

public class JiraUser
{
    [JsonPropertyName("displayName")]
    public string DisplayName { get; set; }
}

public class JiraStatus
{
    [JsonPropertyName("name")]
    public string Name { get; set; }
}

public class JiraParent
{
    [JsonPropertyName("key")]
    public string Key { get; set; }
}

public class JiraProject
{
    [JsonPropertyName("name")]
    public string Name { get; set; }
}

public class JiraChangelog
{
    [JsonPropertyName("histories")]
    public List<JiraHistory> Histories { get; set; }
}

public class JiraHistory
{
    [JsonPropertyName("created")]
    public string CreatedRaw { get; set; }
    [JsonPropertyName("items")]
    public List<JiraHistoryItem> Items { get; set; }

    [JsonIgnore]
    public DateTime? Created => DateTime.TryParse(CreatedRaw, out var dt) ? dt : (DateTime?)null;
}

public class JiraHistoryItem
{
    [JsonPropertyName("field")]
    public string Field { get; set; }
    [JsonPropertyName("fieldtype")]
    public string FieldType { get; set; }
    [JsonPropertyName("from")]
    public string From { get; set; }
    [JsonPropertyName("fromString")]
    public string FromString { get; set; }
    [JsonPropertyName("to")]
    public string To { get; set; }
    [JsonPropertyName("toString")]
    public string ToString { get; set; }
}

public class JiraIssueType
{
    [JsonPropertyName("name")]
    public string Name { get; set; }
}

public class JiraIssueComparer : IEqualityComparer<JiraIssue>
{
    public bool Equals(JiraIssue x, JiraIssue y)
    {
        if (x == null && y == null) return true;
        if (x == null || y == null) return false;
        return x.Key == y.Key;
    }

    public int GetHashCode(JiraIssue obj)
    {
        return obj?.Key?.GetHashCode() ?? 0;
    }
}
