# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Overall Summary

| Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|---------|---------|------|----------|----------------|------------|--------|
| BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 69 days remaining | 0.0% | Refining |
| BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 69 days remaining | 20.0% | In Progress |
| BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 69 days remaining | 31.2% | In Progress |
| BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 69 days remaining | 0.0% | In Progress |
| BMW Spartanburg | [BMWS-379](https://universalsoftware.atlassian.net/browse/BMWS-379) | Controls API | 2025-08-31 | 69 days remaining | N/A | Refining |
| BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 69 days remaining | 50.0% | In Progress |
| BMW Spartanburg | [BMWS-182](https://universalsoftware.atlassian.net/browse/BMWS-182) | Optimize Grab Handles & Sunvisors Commodities | 2025-08-31 | 69 days remaining | 0.0% | Blocked/Deferred |

## Coming Soon (Next 7 Days)

| Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|---------|---------|------|----------|----------------|----------|
| BMW Spartanburg | [BMWS-396](https://universalsoftware.atlassian.net/browse/BMWS-396) | Hotfix to prevent merging of trailers that are not A-D Pillars | 2025-06-25 | 2 days remaining | Amy DeRousha |
| BMW Spartanburg | [BMWS-390](https://universalsoftware.atlassian.net/browse/BMWS-390) | ASN JIS importer error | 2025-06-27 | 4 days remaining | Amy DeRousha |
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | 4 days remaining | Justin Kerketta |
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | 4 days remaining | Amy DeRousha |

## Late Items

| Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|---------|---------|------|----------|--------------|----------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-20 | 3 days overdue | Cameron Rye |
| BMW Spartanburg | [BMWS-328](https://universalsoftware.atlassian.net/browse/BMWS-328) | Create BMW Order Fulfillment By Part SSRS Report  | 2025-06-20 | 3 days overdue | Alex Gonzalez |

## Blocked Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|---------|---------|------|----------|--------|----------------|--------------|----------|
| BMW Spartanburg | [BMWS-182](https://universalsoftware.atlassian.net/browse/BMWS-182) | Optimize Grab Handles & Sunvisors Commodities | 2025-08-31 | Blocked/Deferred | 69 days remaining | 11 days | Kyle Mundie |
| BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 192 days remaining | 12 days | Shayne Vallad |
| BMW Spartanburg | [BMWS-306](https://universalsoftware.atlassian.net/browse/BMWS-306) | Refactor SFG Generator Code for SFG Forecasting Page Update | Not set | Blocked/Deferred | No due date | 0 days | Isaiah Childs |
| BMW Spartanburg | [BMWS-189](https://universalsoftware.atlassian.net/browse/BMWS-189) | Revise Rack Sheet Procedure | Not set | Blocked/Deferred | No due date | 11 days | Unassigned |
| BMW Spartanburg | [BMWS-188](https://universalsoftware.atlassian.net/browse/BMWS-188) | Update OB Kit Labeling Process | Not set | Blocked/Deferred | No due date | 11 days | Unassigned |
| BMW Spartanburg | [BMWS-187](https://universalsoftware.atlassian.net/browse/BMWS-187) | Validate Grab Handles Without Individual Labels | Not set | Blocked/Deferred | No due date | 11 days | Unassigned |
| BMW Spartanburg | [BMWS-186](https://universalsoftware.atlassian.net/browse/BMWS-186) | Optimize Picking Process | Not set | Blocked/Deferred | No due date | 11 days | Unassigned |
| BMW Spartanburg | [BMWS-185](https://universalsoftware.atlassian.net/browse/BMWS-185) | Evaluate Decrementing Inventory for Grab Handles | Not set | Blocked/Deferred | No due date | 11 days | Unassigned |
| BMW Spartanburg | [BMWS-184](https://universalsoftware.atlassian.net/browse/BMWS-184) | Understand Current State of Sunvisors & Grab Handles Process | Not set | Blocked/Deferred | No due date | 11 days | Unassigned |

## Developer Summary

### Alex Gonzalez

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-328](https://universalsoftware.atlassian.net/browse/BMWS-328) | Create BMW Order Fulfillment By Part SSRS Report  | 2025-06-20 | Review | 3 days overdue |

### Amy DeRousha

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-396](https://universalsoftware.atlassian.net/browse/BMWS-396) | Hotfix to prevent merging of trailers that are not A-D Pillars | 2025-06-25 | Review | 2 days remaining |
| BMW Spartanburg | [BMWS-390](https://universalsoftware.atlassian.net/browse/BMWS-390) | ASN JIS importer error | 2025-06-27 | Review | 4 days remaining |
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | Review | 4 days remaining |

### Cameron Rye

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-20 | Review | 3 days overdue |

### Isaiah Childs

#### In Progress Items

_No items in progress._

### Justin Kerketta

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | In Progress | 4 days remaining |

### Kyle Mundie

#### In Progress Items

_No items in progress._

### Shayne Vallad

#### In Progress Items

_No items in progress._

