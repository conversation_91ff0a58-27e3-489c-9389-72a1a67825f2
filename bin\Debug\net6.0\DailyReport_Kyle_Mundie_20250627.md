# Daily Report

## Highlights

_Add any important highlights, meetings, or steps that are not obvious in other sections._

## Overall Summary

| Project | Jira ID | Name | Due Date | Days Remaining | Completion | Status |
|---------|---------|------|----------|----------------|------------|--------|
| BMW Spartanburg | [BMWS-415](https://universalsoftware.atlassian.net/browse/BMWS-415) | Back Order Process | 2025-07-11 | 14 days remaining | 50.0% | Refining |
| BMW Spartanburg | [BMWS-402](https://universalsoftware.atlassian.net/browse/BMWS-402) | Support Cycle Counts | 2025-08-31 | 65 days remaining | 0.0% | In Progress |
| BMW Spartanburg | [BMWS-394](https://universalsoftware.atlassian.net/browse/BMWS-394) | Order & Inventory Management | 2025-08-31 | 65 days remaining | 25.0% | In Progress |
| BMW Spartanburg | [BMWS-393](https://universalsoftware.atlassian.net/browse/BMWS-393) | ASN, Sequencing & Logistics Flow | 2025-08-31 | 65 days remaining | 58.8% | In Progress |
| BMW Spartanburg | [BMWS-392](https://universalsoftware.atlassian.net/browse/BMWS-392) | Technical Infrastructure & Stability | 2025-08-31 | 65 days remaining | 0.0% | In Progress |
| BMW Spartanburg | [BMWS-372](https://universalsoftware.atlassian.net/browse/BMWS-372) | Reporting, Labeling & User Experience | 2025-08-31 | 65 days remaining | 47.4% | In Progress |
| BMW Spartanburg | [BMWS-379](https://universalsoftware.atlassian.net/browse/BMWS-379) | Controls API | 2025-09-30 | 95 days remaining | 0.0% | Refining |

## Coming Soon (Next 7 Days)

| Project | Jira ID | Name | Due Date | Days Remaining | Assignee |
|---------|---------|------|----------|----------------|----------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-27 | Due today | Cameron Rye |
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | Due today | Justin Kerketta |
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | Due today | Amy DeRousha |
| BMW Spartanburg | [BMWS-413](https://universalsoftware.atlassian.net/browse/BMWS-413) | Configure Dashboard URLs for All Supported Commodities | 2025-06-29 | 2 days remaining | Christian Marino Matsoukis |
| BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | 3 days remaining | Antonio Silva |
| BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | 2025-07-01 | 4 days remaining | Cameron Rye |
| BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-04 | 7 days remaining | Justin Kerketta |

## Late Items

| Project | Jira ID | Name | Due Date | Days Overdue | Assignee |
|---------|---------|------|----------|--------------|----------|
| BMW Spartanburg | [BMWS-408](https://universalsoftware.atlassian.net/browse/BMWS-408) | Add Missing Columns InventoryDetailsPurge | 2025-06-24 | 3 days overdue | Justin Mosley |

## Blocked Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining | Days Blocked | Assignee |
|---------|---------|------|----------|--------|----------------|--------------|----------|
| BMW Spartanburg | [BMWS-264](https://universalsoftware.atlassian.net/browse/BMWS-264) | JIS50 on User Scan Report and Inventory In Location/Inventory Locations | 2026-01-01 | Blocked/Deferred | 188 days remaining | 16 days | Shayne Vallad |
| BMW Spartanburg | [BMWS-383](https://universalsoftware.atlassian.net/browse/BMWS-383) | Replace READ UNCOMMITTED with NOLOCK in Stored Procedures for OutboundSeq Page | Not set | Blocked/Deferred | No due date | 1 days | Matthew Berryhill |

## Developer Summary

### Amy DeRousha

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-323](https://universalsoftware.atlassian.net/browse/BMWS-323) | Error Handling for Incorrectly Formatted SeqWeb File | 2025-06-27 | Review | Due today |

### Antonio Silva

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-411](https://universalsoftware.atlassian.net/browse/BMWS-411) | Receiving process reporting more than actually received | 2025-06-30 | In Progress | 3 days remaining |

### Cameron Rye

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-386](https://universalsoftware.atlassian.net/browse/BMWS-386) | Add a column to the User Scan Report. | 2025-06-27 | Validated | Due today |
| BMW Spartanburg | [BMWS-338](https://universalsoftware.atlassian.net/browse/BMWS-338) | Research the source of the duplication for ASNs | 2025-07-01 | In Progress | 4 days remaining |

### Christian Marino Matsoukis

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-413](https://universalsoftware.atlassian.net/browse/BMWS-413) | Configure Dashboard URLs for All Supported Commodities | 2025-06-29 | Review | 2 days remaining |

### Justin Kerketta

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-343](https://universalsoftware.atlassian.net/browse/BMWS-343) | Adjust the inventory locations page to use the universal dynamic datasource | 2025-06-27 | In Progress | Due today |
| BMW Spartanburg | [BMWS-333](https://universalsoftware.atlassian.net/browse/BMWS-333) | Adjust the 'Technical Order Data' page | 2025-07-04 | In Progress | 7 days remaining |

### Justin Mosley

#### In Progress Items

| Project | Jira ID | Name | Due Date | Status | Days Remaining |
|---------|---------|------|----------|--------|----------------|
| BMW Spartanburg | [BMWS-408](https://universalsoftware.atlassian.net/browse/BMWS-408) | Add Missing Columns InventoryDetailsPurge | 2025-06-24 | Validated | 3 days overdue |

### Matthew Berryhill

#### In Progress Items

_No items in progress._

### Shayne Vallad

#### In Progress Items

_No items in progress._

