# Code Review Duration Report

## Tickets Currently in Review

| Status | Ticket | Summary | Assignee | Days in Review | Entered Review |
|--------|--------|---------|----------|----------------|----------------|
| 🚨 | NVSSA-5 | Navistar - Printer Managment Page | Shayne Vallad | 138 | 2025-02-05 |
| 🚨 | SBZ-10 | ASN Location Management Page - What If Scenario - No Printer | <PERSON> | 95 | 2025-03-19 |
| 🚨 | WES-106 | Migrating the outbound status (ready to depart popup) from YMS to WES - frontend | <PERSON> | 77 | 2025-04-07 |
| 🚨 | BOEIN-37 | Migrate Label Printer page to desktop app | <PERSON> | 77 | 2025-04-07 |
| 🚨 | GMFZGT-285 | Add Feature to View Completed but Not Dispatched Orders | Rakhi Garg | 66 | 2025-04-17 |
| 🚨 | WSSG-43 | Cycle Count Function Seats WMS | <PERSON> | 42 | 2025-05-12 |
| 🚨 | GFCGT-77 | Dashboard Time Range | <PERSON> | 21 | 2025-06-02 |
| 🚨 | BMWS-328 | Create BMW Order Fulfillment By Part SSRS Report  | Alex Gonzalez | 21 | 2025-06-02 |
| 🚨 | WES-107 | Migrating the outbound status report (generate report button) from YMS to WES | Trent Meyering | 20 | 2025-06-02 |
| 🚨 | WES-61 | Migrating the outbound status report from YMS to WES - backend | Trent Meyering | 20 | 2025-06-02 |
| 🚨 | FLEET-89 | Correct Personnel Code logic | Jeff Priskorn | 20 | 2025-06-03 |
| 🚨 | FLEET-41 | Asset detail - Equipment tab | R Sanders | 19 | 2025-06-04 |
| 🚨 | FLEET-43 | Asset detail - Secured Parking tab | R Sanders | 18 | 2025-06-04 |
| 🚨 | WES-167 | Verbiage Changes | Trent Meyering | 17 | 2025-06-06 |
| 🚨 | YP-76 | QA Unit Testing of YP-32: Sorting By Categories - Carrier Report | George Moshi | 16 | 2025-06-06 |
| 🚨 | YP-119 | Add SCAC column to carrier | Ludmil Gueorguiev | 12 | 2025-06-11 |
| 🚨 | WSSG-45 | Seats WMS receiving and storing duplicates | Igor Ivanovski | 11 | 2025-06-12 |
| 🚨 | FLEET-42 | Asset detail - Plate tab  | R Sanders | 11 | 2025-06-12 |
| 🚨 | WSWTGT-58 | Change the BOL for WheelTire | Rakhi Garg | 10 | 2025-06-13 |
| 🚨 | WES-381 | Migrating Docks (empty - assign container) - add attachment - from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-380 | Migrating Docks (outbound - assign container) - add attachments - from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-379 | Migrating Docks (inbound - assign container) - add attachments - from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-152 | Migrating Docks (empty - action menu - add evidence) from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-151 | Migrating Docks (outbound - action menu - add evidence) from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-150 | Migrating Docks (inbound - action menu - add evidence) from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-140 | Migrating Container Page (action menu - add evidence) - At Docks - from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-131 | Migrating Container Page (action menu - see history - attachments) - At Docks - from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | WES-124 | Migrating Container Page (action menu - see history - attachment) - In Yard - from YMS to WES | Trent Meyering | 10 | 2025-06-13 |
| 🚨 | YP-122 | Carrier Master update needed for this epic - edit - adding SCAC | Ludmil Gueorguiev | 7 | 2025-06-16 |
| 🚨 | YP-120 | Carrier Master update needed for this epic - adding SCAC into carrier creation | Ludmil Gueorguiev | 7 | 2025-06-16 |
| 🚨 | WSSG-49 | Change the BOL for Seats | Andres Marcelo Garza Cantu | 7 | 2025-06-16 |
| 🚨 | WSPPGT-40 | Change the BOL for Painted Parts  | Alex Gonzalez | 7 | 2025-06-16 |
| 🚨 | WES-24 | Migrating the outbound status from YMS to WES | Trent Meyering | 7 | 2025-06-16 |
| 🚨 | BMWS-323 | Error Handling for Incorrectly Formatted SeqWeb File | Amy DeRousha | 7 | 2025-06-16 |
| ⚠️ | YP-123 | add scac to the display, sort and search by Scac in Carrier Master | Ludmil Gueorguiev | 6 | 2025-06-17 |
| ⚠️ | YP-92 | Research how to accommodate the switcher queue in the database and generate the data structure | Ludmil Gueorguiev | 6 | 2025-06-17 |
| ⚠️ | WSSG-42 | Report sent for seats inventory feed status | Rakhi Garg | 6 | 2025-06-17 |
| ⚠️ | FLEET-40 | Asset detail - Side-panel Display | R Sanders | 6 | 2025-06-17 |
| ⚠️ | YP-126 | Menu to add zone master | Ludmil Gueorguiev | 5 | 2025-06-18 |
| ⚠️ | YP-129 | Zone Master - adding to database | Ludmil Gueorguiev | 4 | 2025-06-19 |
| ⚠️ | BMWS-390 | ASN JIS importer error | Amy DeRousha | 4 | 2025-06-19 |
| ⚠️ | BMWS-386 | Add a column to the User Scan Report. | Cameron Rye | 4 | 2025-06-19 |
| ⚠️ | DTCMS-92 | Investigate and Resolve Overlength Field Values in DTNA RTNABL_INVNTRY File (Upstream Fix Required) | Cameron Rye | 3 | 2025-06-20 |
| ⚠️ | BMWS-396 | Hotfix to prevent merging of trailers that are not A-D Pillars | Amy DeRousha | 3 | 2025-06-19 |
| ⚠️ | BMWS-395 | Resolve Permission Issues for Android 14 in Xamarin App | Matthew Berryhill | 3 | 2025-06-19 |
| ✅ | YP-136 | Zone Master - delete zones | Ludmil Gueorguiev | 0 | 2025-06-23 |
| ✅ | YP-132 |  Zone Mater - view zones and sort | Ludmil Gueorguiev | 0 | 2025-06-23 |
| ✅ | YP-128 | Zone Master - edit zone | Ludmil Gueorguiev | 0 | 2025-06-23 |
| ✅ | YP-127 | Zone Master - add zone | Ludmil Gueorguiev | 0 | 2025-06-23 |
| ✅ | POR-17 | Location & Job Profile Filters for Workday Demographic API | Christian Marino Matsoukis | 0 | 2025-06-23 |
| ✅ | HIG-34 | Testing | Aaron Anderson | 0 | 2025-06-23 |
| ✅ | FLEET-100 | Asset detail - Side-panel Features (Reinstate) | R Sanders | 0 | 2025-06-23 |

## Summary Statistics

- **Total tickets in review:** 52
- **Average days in review:** 17.2
- **Longest in review:** 138 days
- **Tickets over 3 days:** 42
- **Tickets over 7 days:** 28

